LM Studio 发布了 [0.3.17 版本](https://lmstudio.ai/blog/lmstudio-v0.3.17)，引入了对 Model Context Protocol（MCP，模型上下文协议）的支持，这是推动语言模型访问外部工具和数据源的重要一步。MCP 最初由 Anthropic 开发，它定义了一种标准化接口，让大型语言模型能够连接到 GitHub、Notion、Stripe 等服务，从而实现更强大的上下文推理能力。

此次更新使得 LM Studio 成为了一个 MCP 主机（Host），可以连接本地或远程的 MCP 服务器。用户可以通过编辑应用中的 *mcp.json* 配置文件，或点击支持的“一键添加到 LM Studio”集成功能按钮，来添加 MCP 服务器。

每个 MCP 服务器都在一个独立的进程中运行，这种架构保证了模块化和稳定性，同时保持对本地环境的兼容性。LM Studio 支持依赖于 *npx、uvx* 或其他系统命令的 MCP，只要这些工具已安装并在系统 PATH 中可访问即可。

安全性和用户自主权是该设计的核心。当模型尝试通过 MCP 服务器调用某个工具时，LM Studio 会弹出工具调用确认窗口，用户可以查看、批准、修改或拒绝操作。用户还可以将某些工具加入白名单，方便今后自动调用；相关设置可在“Tools & Integrations（工具与集成）”菜单中统一管理。

一个实际的使用示例是 Hugging Face MCP Server，它允许模型访问 Hugging Face 的 API 来搜索模型或数据集。用户只需在配置中输入自己的 API Token，该服务器就能在 LM Studio 环境中使用了。对于希望用结构化实时数据扩展本地模型的 LLM 开发者来说，这一功能非常实用。

该项目已经引起社区的关注。Xholding Group 的项目经理 Daniele Lucca 在 LinkedIn 上[评论](https://www.linkedin.com/posts/danielelucca_lm-studio-now-supports-mcp-connect-your-activity-7343922222333341696-CK3b?utm_source=share&utm_medium=member_desktop&rcm=ACoAACX5yoEBhsg1xPtc5iaJXHCu_Rv298CmfZA)道：

> 太棒了！这正是我正在做的一个兴趣项目。我在利用外部数据源，把我们行业——AIDC（自动识别与数据采集）20 年来的问题、解决方案和手册“教”给一个 AI。

不过，也有部分用户反馈了早期的问题。一位 Reddit 用户[提到](https://www.reddit.com/r/LocalLLaMA/comments/1lkc5mr/comment/mzqjs6u/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button)：

> 我就是想能加载模型列表就好了。现在我搜索模型时总是报错，还有其他人遇到这个问题吗？

另一位用户[回复](https://www.reddit.com/r/LocalLLaMA/comments/1lkc5mr/comment/mzqkqt4/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button)说：

> 我前两天也遇到了，昨天又没事了，感觉是间歇性的。

LM Studio 鼓励用户通过其 [GitHub 问题追踪器](https://github.com/lmstudio-ai/lmstudio-bug-tracker/issues)反馈 MCP 支持过程中的问题。

0.3.17 版本现已上线，用户可以通过应用内更新或直接访问 [lmstudio.ai](https://lmstudio.ai/download) 下载。