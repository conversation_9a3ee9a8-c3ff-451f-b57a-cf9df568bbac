## 0 前言

Kimi K2 是我们最新发布的MoE，激活参数达 320 亿，总参数量高达 1 万亿。在前沿知识、数学与编程任务中，Kimi K2 在“非思维模型”类别中表现优异，达到当前最先进水平。但这还不是全部 —— Kimi K2 针对“智能体任务”进行了精心优化，它不仅能回答问题，更能**执行任务**。

本次开源：

- Kimi-K2-Base：基础模型，适合研究人员和开发者微调和定制
- Kimi-K2-Instruct：经过后训练优化，适合通用对话与智能体应用，即插即用，无需深度思考

Kimi K2 让先进的智能体智能更加开放与可及。期待看到你用它构建的精彩应用。

## 1 智能体与竞赛级编程能力

SWE-bench Verified：

![](https://p.ipic.vip/v1g1dj.png)

SWE-bench Multilingual：

![](https://p.ipic.vip/79xval.png)

LiveCodeBench v6：

![](https://p.ipic.vip/o41niw.png)

OJBench：

![](https://p.ipic.vip/zg5hm8.png)

## 2 工具使用能力

Tau2-bench 加权平均值：

![](https://p.ipic.vip/nlqszh.png)

AceBench（英文）

![](https://p.ipic.vip/nl93s4.png)

## 3 数学与 STEM 能力

AIME 2025：

![](https://p.ipic.vip/zhtfmg.png)

GPQA-Diamond：

![](https://p.ipic.vip/fad2zg.png)

上述评测模型均为非思考模型

Tau2-Bench 按任务权重加权

SWE-Bench Multilingual 仅评测了 Claude 4 Sonnet，因 Claude 4 Opus 成本过高

## 4 应用场景示例

只需将工具和任务描述交给 Kimi K2，它会自动理解并完成任务，无需你手动编写复杂流程。

### 4.1 工资数据分析

**示例任务：**

利用 2020–2025 年工资数据，分析远程办公比例对工资的影响，并判断该影响是否随工作经验等级（入门/中级/高级/专家）而显著不同，即是否存在交互效应。分析需包含统计证据和丰富的可视化图表，配色统一和谐（建议使用柔和色调）。

**分析流程如下（使用 IPython 执行）**：

1. 加载数据并了解结构
2. 筛选 2020–2025 年的数据，绘制小提琴图按经验和远程比例分类展示工资分布
3. 将 `remote_ratio` 分类为“现场办公”、“混合办公”和“远程办公”，再绘制箱线图比较不同经验等级的工资分布
4. 进行双因素方差分析（ANOVA），检验远程办公与经验等级的主效应与交互效应
5. 因数据不足，改用每组经验等级内远程 vs 现场的成对 t 检验，比较显著性
6. 计算各组合的平均工资，使用柱状图清晰展示交互情况
7. 进一步生成交互图、散点图、平均薪资对比图，直观展示远程办公对不同经验等级工资的影响
8. 最后生成总结图，清晰展示不同经验等级远程 vs 现场办公的工资百分比差异

**结论摘要**：

使用 2020–2025 年的数据，我们检验了远程办公比例对工资的影响及其是否因经验等级而不同。结果发现：**远程办公比例与经验等级之间存在显著的交互作用**。

#### 关键统计结果：

- **入门级员工**：远程比现场高出 **8.0%**，p≈0.20（不显著）
- **中级员工**：远程比现场低 **8.5%**，p=0.003（高度显著）
- **高级员工**：远程比现场低 **3.5%**，p≈0.05（边缘显著）
- **专家级员工**：远程比现场高 **4.6%**，p≈0.61（不显著）

#### 可视化分析：

图表清晰展示了远程办公带来的薪资差异会因经验等级而异。尤其是中级员工，远程办公者面临明显薪资劣势，而入门和专家级可能会略有优势。

#### 总结：

远程办公对工资的影响并非一致，而是随经验等级而变。对于中级员工，远程办公可能带来显著的薪资惩罚，而对入门与专家级员工则可能带来一定优势。

------

### 4.2 可视化网页展示

我们为这份分析设计了一个精美网页，包含：

#### 🎨 设计亮点：

- 柔和 / 馥郁色调（pastel, muted），时尚杂志风格
- 清晰的排版与响应式设计，适配手机与桌面
- 统一的视觉层次与间距控制

#### 📊 数据分析内容：

- **执行摘要**：关键统计数据以图卡形式展示
- **详细分析过程**：包括数据清洗、可视化、统计检验
- **图表展示**：嵌入所有可视化图像

#### 🧮 交互模拟器：

- 用户可输入：经验等级、期望薪资、远程意愿、行业
- 实时模拟推荐是否适合远程办公
- 颜色提示结果（绿=推荐，黄=谨慎，红=不推荐）
- 详细预测远程办公对工资的影响

### 4.3 Kimi K2 的其他示例

- 使用 16 个 IPython 步骤，自动完成数据分析并生成网页
- 结合网页搜索、浏览器操作、滚动、点击和部署，实现自动生成互动站点
- 安排 Coldplay 伦敦演唱会行程，包括搜索航班、餐厅与 Airbnb 预订

想象一下，使用 Kimi K2 探索远程办公薪资，借助“薪资数据分析”这个示例，16 个 IPython 调用自动生成统计结果、可视化图表和一个交互式网页；深入了解 Stanford NLP 系谱图谱，Kimi K2 可通过 5 次网页搜索、4 次浏览、3 次点击、5 次滚动、6 次编辑和 2 次部署，构建出一个交互式站点；又或是计划你心中的 2025 年 Coldplay 伦敦巡演之旅，Kimi K2 可通过 17 次无缝工具调用完成从搜索、日历、Gmail、航班、Airbnb 到餐厅预订的全部流程。

现在，把 Kimi K2 带到你的命令行中吧。它能编辑文件，能执行命令。

Kimi K2 能理解你的操作环境，自主决定下一步执行什么，并顺利完成任务。

### 4.4 示例：用 JavaScript 编写 3D Minecraft 网页版

去官网。

为便于理解，终端中仅显示每条任务轨迹的概要信息。在每个示例中，Kimi K2 都在幕后协调多个工具与命令，来完成复杂目标。例如，Kimi K2 可自动完成 JavaScript 编写的 Minecraft 游戏开发：它管理渲染流程、运行并调试测试用例、在失败时记录日志，并不断迭代代码直到全部测试通过。对于前/后归一化分析，Kimi K2 可使用 Weights & Biases（wandb）数据读取器提取语言模型实验的洞见，并生成一份精致的分析报告。在将一个 Flask 项目迁移至 Rust 时，Kimi K2 会系统地重构整个代码库并运行性能基准测试，以确保性能稳健。

## 5 Kimi K2 基准测试表现

### Kimi-K2-Instruct

下表展示 Kimi-K2-Instruct 的性能表现，显示该模型在众多任务中与最新的开源或闭源模型相当，甚至超越它们。该模型在知识密集型和推理类基准上表现尤为出色，在自然语言理解、数学科学、代码生成与智能体工具使用等方面均有卓越成绩。

- 上述评估的所有模型均为“非思维模型”
- 表中加粗表示全球 SOTA，带下划线表示开源 SOTA
- 带 * 的数据来自模型的技术报告或官方博客
- 除 SWE-bench Verified（非智能体模式）外，所有评测均在 8k 输出 token 长度下进行。SWE-bench Verified（非智能体模式）限制为 16k 输出长度
- Kimi K2 在使用 bash/editor 工具、单次尝试且不进行测试时，在 SWE-bench Verified 测试中达到了 65.8% 的 pass@1。同时，在 SWE-bench Multilingual 测试中也达到了 47.3% 的 pass@1。此外，我们还报告了另一组 SWE-bench Verified 的结果（71.6%），该测试使用了并行测试时计算：通过对多个样本序列打分并选择最优结果来生成最终提交
- 为了确保评估稳定性，我们在 AIME、HMMT、CNMO、PolyMath-en、GPQA-Diamond、EvalPlus 和 Tau2 上使用 avg@k 方式评估
- 部分数据因评估成本过高而未列出。

Kimi-K2-Base

下表总结了 Kimi-K2-Base 模型的表现，显示它在多个任务上可与当前最强的开源预训练模型相媲美，甚至超越。Kimi-K2-Base 在知识密集与推理类基准任务上表现尤为突出，在自然语言理解、数学和代码生成方面尤为擅长。

- 本评测仅涵盖开源预训练模型。我们报告了 Qwen2.5-72B 的结果，因为在评测时 Qwen3-235B-A22B 的基础 checkpoint 尚未开源
- 所有模型均使用统一的评估协议进行评测

详细表格略，原文查

## 6 开放的智能体智能

预训练是代理智能 (Agentic Intelligence) 的关键基础，它建立先验知识，使强化学习 (RL) 的探索变得易于处理、高效且具有泛化能力。然而，正如 Ilya Sutskever 所观察到的，人类数据是一种有限的“化石燃料”，其增长速度远远落后于计算速度。这使得预训练过程中的代币效率成为 AI 缩放定律中一个新的关键系数。

在“体验时代”（David Silver，Richard Sutton，2025）中，后训练至关重要。在这个时代，法学硕士越来越多地从自身产生的互动中学习，获得回报，使他们摆脱人类数据的限制，并超越人类的能力。

Kimi K2 正是基于这些见解而打造的。  

### MuonClip 优化器

在没有严格性的情况下，给定一个近似有限的预训练数据集和一个固定的模型配置，一个更高效的令牌优化器可以产生更高的智能。我们之前的研究“Moonlight”已经证明，在 LLM 训练中，Muon 优化器的性能显著优于广泛使用的 AdamW 优化器。

Kimi K2 的设计旨在进一步扩展 Moonlight，其架构与 DeepSeek-V3 类似。基于缩放律分析，我们减少了 head 的数量以提高长上下文效率，并增加了 MoE 稀疏性以提高 token 效率。在扩展过程中，我们遇到了一个持续存在的挑战：注意力 logit 爆炸导致的训练不稳定性。在我们的实验中，这个问题在 Muon 中更常见，但在 AdamW 中较少出现。现有的解决方案（例如 logit 软上限和查询键规范化）被发现不够完善。

为了解决这个问题，我们引入了 MuonClip 优化器，该优化器基于我们提出的 qk-clip 技术对 Muon 进行了改进。具体来说，qk-clip 通过在 Muon 更新后直接重新缩放查询和键投影的权重矩阵来稳定训练，从而控制源头注意力逻辑的规模。具体而言，查询和键投影的缩放比例如下：

![image-20250717105908953](/Users/<USER>/Library/Application Support/typora-user-images/image-20250717105908953.png)

其中 *α* 是平衡超参数，因此注意力逻辑变为：

![image-20250717105925698](/Users/<USER>/Library/Application Support/typora-user-images/image-20250717105925698.png)

每一步之后都会根据此步骤中的最大注意力逻辑设置自适应因子 *η* （阈值为 *t* ）：

![image-20250717105946839](/Users/<USER>/Library/Application Support/typora-user-images/image-20250717105946839.png)

其中 *t* 是预设阈值。这是一种通用技术，可能适用于其他稳定性用例。

我们的实验表明，MuonClip 能够有效防止逻辑爆炸，同时保持下游任务的性能。在实践中，Kimi K2 使用 MuonClip 在 15.5T 的 token 上进行了预训练，训练峰值为零，证明了 MuonClip 是稳定、大规模 LLM 训练的强大解决方案。

![](https://p.ipic.vip/9r9p39.png)

## 7 智能体能力

### 大规模代理数据合成

Kimi K2 的增强代理能力源于两个重要方面——大规模代理数据合成和通用强化学习。

用于工具使用学习的大规模代理数据合成：为了教会模型复杂的工具使用能力，我们受 ACEBench 启发，开发了一个全面的流程，可以大规模模拟现实世界的工具使用场景。我们的方法系统地演化了数百个包含数千种工具（包括真实的 MCP（模型上下文协议）工具和合成工具）的领域，并生成了数百个拥有不同工具集的代理。

所有任务均基于评分标准，从而实现一致的评估。代理与模拟环境和用户代理交互，创建逼真的多轮工具使用场景。LLM 评委根据任务评分标准评估模拟结果，筛选出高质量的训练数据。这种可扩展的流程能够生成多样化、高质量的数据，为大规模拒绝采样和强化学习奠定基础。

![](https://p.ipic.vip/oo6k4a.png)

### 通用强化学习

通用强化学习：关键挑战在于将强化学习应用于具有可验证和不可验证奖励的任务；可验证任务的典型示例是数学和编程竞赛，而撰写研究报告通常被视为不可验证的任务。除了可验证奖励之外，我们的通用强化学习系统还采用自我判断机制，让模型充当自身的“批评家”，为不可验证任务提供可扩展的、基于评分标准的反馈。

同时，使用可验证奖励的在线策略 rollout 来持续更新评论家，使评论家不断提高其在最新策略上的评估准确性。这可以被视为一种利用可验证奖励来改进不可验证奖励估计的方法。

## 8 使用 Kimi K2

### 在线体验

访问 [kimi.com](https://www.kimi.com/)，从今天开始，Kimi 的网页版和移动版用户均可免费选择并使用全新的 Kimi K2 模型。目前，我们网页版和 App 版的 MCP 功能仍在开发中。我们希望在未来几周内推出这些功能。在此期间，欢迎您试用我们的 Researcher，抢先体验其代理功能。请注意，Kimi K2 尚不支持视觉功能。

### API 接入

Kimi 平台提供与 OpenAI/Anthropic 兼容的接口，方便您轻松将现有应用程序适配至 Kimi K2。我们鼓励开发者探索我们的工具调用 API，以构建代理应用程序。更多信息，请访问 platform.moonshot.ai。：[platform.moonshot.ai](https://platform.moonshot.ai/)

### 本地部署

建议以下推理引擎运行 Kimi K2：vLLM、SGLang、KTransformers 或 TensorRT-LLM。

详细部署说明[GitHub 仓库](https://github.com/MoonshotAI/Kimi-K2?tab=readme-ov-file#4-deployment)

## 9 后续

Kimi K2 为开放式智能代理奠定了坚实的基础，而通用智能代理则拥有更高级的功能，例如思考和视觉理解，未来添加到 Kimi K2。

## 10 限制

内测发现K2处理复杂推理任务或工具定义不明确时，模型可能生成过多token，导致输出被截断或工具调用不完整。若启用工具，某些任务性能可能下降。

构建完整软件项目时，与在代理框架下使用 K2 相比，一次性提示会导致性能下降。