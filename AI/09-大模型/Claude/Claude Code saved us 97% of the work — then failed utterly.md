

## 1 什么是Claude Code？

Claude Code是由Anthropic开发并于2025年2月24日发布的AI编码助手。它是所谓的“受监督编码代理”的一个例子。这些工具可以在软件开发工作流程中执行相对复杂的任务，有时甚至可以自主完成。

大多受监督编码代理都是通过IDE融入工作流程，如Cursor、GitHub Copilot。而与这些不同，它基于终端（开源代理工具Aider和Goose也采用终端而非IDE）。通过终端工作使将代理融入更广泛的生态系统变得更容易，而非局限于IDE。

## 2 比较Claude Code与其他受监督编码代理

不要对这些工具进行比较，因为它们都在迅速发展。现在进行的快照式比较可能在几个月甚至几周内就会过时。值得关注：

- Claude Code类似Cline、Aider，需将API密钥接入LLM。你可能用Claude-Sonnet系列模型，因这些模型在编码表现最出色。相比下，Cursor和GitHub Copilot是订阅产品
- 与Cline和Goose一样，Claude Code可以与MCP集成，这是一种连接LLM与数据源的开放标准

目前，Claude Code的性能和实用性主要取决于它在协调代码上下文、提示模型以及与其他上下文提供者集成方面的表现。

| 工具名称    | 界面 | LLM连接方式                                     |
| ----------- | ---- | ----------------------------------------------- |
| Claude Code | 终端 | API密钥/按使用付费                              |
| Aider       | 终端 | API密钥/按使用付费                              |
| Goose       | 终端 | API密钥/按使用付费                              |
| Cursor      | IDE  | 月度订阅（有一定限制），或使用API密钥按使用付费 |
| Cline       | IDE  | API密钥/按使用付费                              |
| Windsurf    | IDE  | 月度订阅（有一定限制）                          |

## 3 为什么对Claude Code感兴趣？

对AI辅助编码和代理AI很感兴趣，Claude Code的发布自然引起了我们的关注。

虽然潜在的用例范围很广，但我们特别想知道Claude Code是否能帮助我们解决在开发生成式AI代码发现挑战：添加对新语言的支持。

尽管LLM是分析代码库的有力工具，但其中一个缺点是，每当我们想要支持一种新的编程语言——并从其AST中提取相关部分时——我们就需要编写新代码来生成和解释这棵树。这需要时间；通常需要两到四周。我们需要找到相关的代码库示例，构建一套自动化测试，然后对代码库进行更改以支持新语言。这是我们只在需要时才进行这一操作的主要原因。

![CodeConcise架构图](https://www.thoughtworks.com/content/dam/thoughtworks/images/photography/inline-image/insights/blog/deep-dive-codeconcise.png)

## 4 Claude Code哪些方面表现良好？

### 4.1 成功：让Claude Code识别必要的更改

询问Claude Code关于在项目中添加对Python支持所需的更改。对于已经在代码库中工作了几周的开发人员来说，这个问题可能相当简单，但如果你是完全新手，这将非常有价值。

![提示Claude Code为CodeConcise提供支持](https://www.thoughtworks.com/content/dam/thoughtworks/images/photography/inline-image/insights/blog/claude%20code%202.png)

![提示Claude Code为CodeConcise提供支持](https://www.thoughtworks.com/content/dam/thoughtworks/images/photography/inline-image/insights/blog/claude%20code%203.png)

在访问代码和相关文档后，Claude Code取得了令人惊叹的结果。它准确地识别了支持Python所需的所有更改。此外，最后建议的代码表明，该代理不仅检查了我们过去开发的其他摄取工具，还考虑了我们用于实现这些工具的模式，以便为我们正在询问的新代码进行构建。

### 4.2 有限成功：让Claude Code为CodeConcise实施必要的更改

对Claude Code的第二个提示是让它自己实际实施建议的更改：

***我需要构建一个新工具，用于将Python代码加载到项目中。请完成这项工作并进行测试。***

它花了不到三分钟的自主工作时间。所有更改都在本地实施，包括测试。Claude Code建议的所有测试都通过了，但当我们使用项目将它的源代码加载到知识图谱上并运行我们自己的端到端测试时，我们发现了几个问题：

1. 文件系统结构本身并未包含在图中
2. 连接节点的边不符合我们在CodeConcise中的模型。例如，调用依赖项缺失（后续部分如理解管道将无法按照我们的预期进行遍历）

## AI辅助编码中反馈循环的重要性

这个实验很好地提醒了我们，在使用AI帮助我们编写代码时，拥有多个反馈循环是多么重要。如果我们没有测试来验证集成是否真正有效，我们就不会这么早发现这个问题。这可能会既具有破坏性又代价高昂；开发人员和代理都会失去对正在进行的工作的上下文。

在向Claude Code提供反馈后，我们等待了几秒钟，看到代码正在更新。初步查看生成的代码，可以看出该代理能够很好地遵循代码中的模式，例如使用观察者在解析代码时创建文件系统结构。

值得注意的是，对于这个特定用例，大部分复杂的思考已经由架构工具的开发人员完成。他们已经做出了将领域核心逻辑与支持新语言解析所需的相对更重复的实现细节分开的决定。Claude Code所要做的就是将这些信息整合起来，并从现有设计中理解需要构建哪些特定于语言的内容。

## 这教会了我们什么？

尽管现有的解决方案架构良好，但平均需要两名开发人员和一位领域专家花费两到四周的时间来构建对一种新语言的支持。让领域专家和Claude Code一起工作，只需几分钟就能为这个特定用例生成代码，几个小时就能验证它。

## 什么没有奏效？

### 完全失败：让Claude Code帮助我们添加JavaScript支持

我们对实验的结果相当满意，所以尝试用同样的方法为JavaScript添加支持：

***我想让你为javascript添加一个摄取工具。我已经为你准备好了lexerbase和parserbase。请像在其他地方一样使用stageobserver，并像在tsql加载器中一样使用访问者模式来实现lexer和parser。***

第一次，它尝试使用ANTLR语法来实现JavaScript。我们无法让语法正常工作（这超出了Claude Code和CodeConcise的范围），因此无法验证代码是否真正有效。

第二次，我们提示Claude Code，让它改用treesitter。我们觉得运气可能不太好，它开始使用不存在的库。同样，我们无法验证生成的代码。

第三次也是这个系列的最后一次，我们让它尝试另一种方法。它决定使用正则表达式匹配来解析代码。

那么，它成功了吗？*没有：代码引用了不存在的内部包。*

![Claude Code](https://www.thoughtworks.com/content/dam/thoughtworks/images/photography/inline-image/insights/blog/claude%20code%204.png)

显然，该代理缺乏对其生成的代码进行更强验证的机制。就好像它缺少了一个简单的单元测试可以提供的反馈。这并非新现象；实际上，我们已经多次观察到其他编码助手也存在这种情况。

我们想知道第一次Python工作是否只是运气好，于是让它也为C做同样的事情。结果与Python差不多，尽管它采用的是正则表达式匹配，而不是更可靠的基于AST的方法。

## 这让我们对Claude Code和编码代理了解了什么？

上述实验针对的是一个非常具体的用例，因此重要的是不要得出不恰当的结论。然而，仍然有一些重要的收获值得分享：

1. 输出结果可能非常不一致。当被要求实现Python和C的摄取工具时，Claude Code产生了令人惊叹的结果，但在被要求为JavaScript做同样的事情时，结果却很差。
2. 编码助手的结果取决于多个因素：
   1. **代码质量。**我们一直认为对人类来说重要的高质量代码，对于代理来说似乎也是如此。这意味着，当代理与编写良好、模块化、清晰且具有分离关注点和一些文档的代码一起工作时，我们最大化了它产生高质量输出的机会。
   2. **库生态系统。**构建Python解析器是否更容易，是因为Python（CodeConcise所基于的语言）已经提供了一个标准模块，用于将Python代码转换为AST？我们的假设是，尽管代理需要一套良好的工具来执行它们所做的决策，但它们的输出质量还取决于它们是否可以使用设计良好、广泛认可的库来解决问题。
   3. **训练数据。**众所周知，大型语言模型（LLM）以及由此扩展的AI代理，对于其原始训练数据集中有大量数据的编程语言，能够产生更好的代码。
   4. **大型语言模型。**由于代理是基于大型语言模型构建的，它们的性能直接受到它们所使用的底层模型性能的影响。
   5. **代理本身。**这包括提示工程、工作流设计以及它所拥有的工具。
   6. **人类搭档。**通常情况下，当看到人与代理一起工作时，我们能够得到两者的最佳效果。此外，熟悉代理的开发人员通常知道一些技巧，可以引导它们完成工作，并最终产生更好的结果。

Claude Code展现出很大的潜力，看到另一个可以在终端中使用的受监督编码代理令人兴奋。Claude Code肯定会像其他领域一样不断发展，我们期待探索如何继续将它们融入我们的工作中。
