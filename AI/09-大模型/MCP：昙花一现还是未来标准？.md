## 0 前言

Model Context Protocol（MCP）最近在全网引发热议——它真有实际价值，还是只是营销号的氛围感？LangChain 的 CEO <PERSON> Chase 和 LangGraph 负责人 Nuno Campos 针锋相对，探讨 MCP 是否真的值得关注。

**Harrison**：MCP 在你想要为一个**你无法控制**的智能体提供工具时就会变得有用。

如对Claude Desktop、Cursor这些应用，用户无法控制底层的智能体，而智能体本身只具备一些内置工具。

但若我想让它使用一个默认没有的工具呢？就需要某种协议，否则智能体根本不知咋调用这工具。

MCP对非开发者创建智能体也很有帮助。目前趋势，让**领域专家**能构建智能体，而无需技术背景。这些用户可能不会（或不愿）直接修改智能体逻辑，但肯定希望给智能体添加工具。MCP就很有价值。

**Nuno**：我觉得你低估智能体与工具的适配度。确实，若Windsurf（假设它自带网页搜索工具很差）支持用户替换成一个更好的，那可行。但这不算真正使用场景。

更吸引人的场景：用户能仅靠一个神奇工具，就让 Cursor 获得连其开发者都没想象过的新能力。但现实**大多行不通**。我见过的生产环境下，智能体的系统消息、架构甚至其他部分都必须根据所使用的工具进行调整。

**Harrison**：好吧，这些智能体的准确率可能达不到 99%……但即使不完美，它们仍可足够实用，不是吗？工具的描述和指令确实很重要，但我们也知道：

- MCP 提供了工具定义——而好的 MCP 服务器可能比用户自己随便写的描述更详细
- MCP 允许提供提示词（prompts）——用户可在这里加入额外的使用说明
- 随底层模型进步，智能体调用工具的能力会越来越强

我不认为 MCP 及其工具调用能力能直接催生下一个 Cursor，但它仍有一定价值，尤其在内部或个人智能体场景。

**Nuno**：但我们的工具调用测试表明，即便在专门为某些工具优化的智能体，当前模型的调用正确率**只有 50%**。如换成 MCP 这种通用协议，成功率恐怕更低。而即使个人智能体，**一半的调用都失败**，这也没啥用吧？

而且的确，模型会变更强，但用户**期望值也提高**。贝索斯说：“客户的期望永远不会静止不变——它们只会不断上升。这是人性。”

如果你能掌控整个产品——UI、提示词、架构、工具——你就能满足这些期望。否则，Good luck！

**Harrison**：模型能力会持续进步，我愿意赌这点。所以无论当前智能体的成功率多少，未来**只会更高**。

我觉得正确比较方式不是拿 MCP 智能体 V.S 高度定制化智能体，而是看 MCP 能否提供**灵活的集成能力**。

就像 Zapier 让你能将邮件连接到 Google Sheets、Slack 等。现实中有**无数**可能工作流，而 MCP 可让用户自己去创建它们，而不必等一个专门智能体来适配每种需求。

你觉得这个 Zapier 的类比如何？

**Nuno**：LangChain两年前就已提供包含 500 个工具的库，但我很少看到它们被真正用于生产环境。这些工具都遵循同样的“协议”，可兼容任何模型，也可随意替换。那 MCP 又有啥不同？是因为 MCP 的“优秀形态”要求用户在本地终端里跑无数个服务器，还只能兼容桌面应用？这可不算优势……

老实说，我觉得 **Zapier 已经是 MCP 的上限** 。

**Harrison**：MCP 工具和 LangChain 工具最大区别在：**MCP 不是为智能体开发者设计**。其最大价值在于让**用户**能为一个**自己无法开发**的智能体添加工具。

若我是一个开发，要从头构建一个智能体，我不会用 MCP。但 MCP 目标用户不是开发者，而是**希望拓展现有智能体能力的普通用户**。MCP 让非开发者也能给智能体添加工具，而 LangChain 工具更适合开发者使用。市场规模，非开发者数量**远大于**开发者。

至于 MCP 目前形态，确实糟糕。但它未来会改进，设想的 MCP 未来形态：**用户可一键安装 MCP 应用（无需再在本地终端跑服务器），而且能在 Web 端使用**。我相信 MCP 正朝这方向发展。

你觉得 MCP 需要哪些改进？如果改进到位，你会认为它有价值吗？

**Nuno**：好吧，听起来 MCP 需要变成 OpenAI 的 **Custom GPT**，那它现在的热度才合理。但**Custom GPT 本身也没那么火**。MCP 又有啥独特？

**Harrison**：其实，MCP 更像 OpenAI 的 **插件（Plugins）**，但它们也没成功 🙂 说实话，我几乎没用过插件，所以可能对它们的情况了解不深。但我认为：

- MCP 的**生态系统**已经远超当年的插件生态
- 现在的模型比当时的插件时代更强，能更好利用这些工具

**Nuno**：嗯，我不太确定 MCP 生态真的比插件更大。我随便搜了一下，就找到一个 MCP 服务器目录，目前上面列出 893 个服务器。而你可能只是看到 Twitter 上 MCP 相关的讨论比较多 🙂。

回到你的问题，如果 MCP 想要成为 AI 发展史上的一个重要章节，而不仅仅是个小插曲，它需要：

1. **降低复杂度**——为啥一个工具协议还要负责处理提示词和 LLM 生成？
2. **简化实现**——为啥一个工具协议需要双向通信？我看过官方解释，但光是为能接收服务器日志就这么做？不合理。
3. **支持服务器端部署**——一个**无状态协议**才是关键，LLM 时代也不能忽视已有的在线扩展经验。而一旦能用于服务器，还涉及**身份认证（auth）**，分布式环境下不易解决。
4. **弥补质量损失**——智能体随意调用陌生工具，往往会导致体验下降，如何弥补？

**Harrison**：你说的对，我可能确实受到 Twitter 上最近 MCP 讨论的影响。但同时，Twitter上也有很多怀疑声音！

所以，你认为 MCP 是昙花一现，还是未来标准？