## 0 前言

Grok 4 是目前全球最智能的模型。原生支持工具使用和实时搜索集成，向 SuperGrok 和 Premium+ 用户开放，也可通过 xAI API 使用。还推出新的 SuperGrok Heavy 订阅层级，提供对 Grok 4 Heavy 的访问权限——Grok 4 最强大版。

[试用 SuperGrok](https://grok.com/plans)
 [访问 API](https://docs.x.ai/)

## 1 强化学习的扩展

在 Grok 3 中，我们将下一个词预测的预训练规模提升到了前所未有的高度，打造出了在世界知识和表现方面无与伦比的模型。还推出了 Grok 3 Reasoning，它通过强化学习进行训练，使其能更深入思考问题并提高解决准确率。观察到了一些规模化趋势，表明强化学习训练有望大幅扩展。

在 Grok 4 中，我们利用了 Colossus —— 一个由 200,000 个 GPU 组成的集群，以预训练的规模运行了用于提升 Grok 推理能力的强化学习训练。这得益于整个系统中的一系列创新，包括新的基础设施和算法优化，使训练的计算效率提升了 6 倍。还进行大规模的数据收集工作，将可验证的训练数据从主要集中在数学和编程领域，扩展到更多领域。最终的训练过程在比以往大一个数量级的计算量下，实现平稳的性能提升。

### 1.1 人类的最终考试

位于人类知识前沿的专家级基准测试

![](https://p.ipic.vip/338n2o.png)

## 2 原生工具使用

Grok 4 在训练中通过强化学习学习了如何使用工具。这使它能在许多传统大语言模型难以应对的场景下，借助代码解释器、网页浏览等工具来增强思维能力。无论是搜索实时信息还是回答复杂研究问题，Grok 4 都能自主生成搜索查询，从网络中提取相关知识，深入挖掘，构建高质量回答。

还训练 Grok 使用强大的工具，从 X（原推特）深处获取信息。Grok 可以使用先进的关键词和语义搜索工具，甚至查看媒体内容，以进一步提升回答质量。

X 搜索、网页搜索、研究案例见原文。

## 3 Grok 4 Heavy

我们在并行测试时计算（test-time compute）方面取得了进一步进展，使 Grok 能够同时考虑多个假设。我们称这个模型为 **Grok 4 Heavy**，它在性能和可靠性方面树立了新标准。Grok 4 Heavy 在多数学术基准测试中达到了饱和状态，并且是首个在“人类最终考试”中获得 50% 得分的模型——这是被设计为“最后一个封闭式学术基准”的测试。

![](https://p.ipic.vip/4lihxq.png)

## 4 前沿智能

Grok 4 在前沿智能方面实现了飞跃，在封闭模型中在 ARC-AGI V2 上创下 15.9% 的新纪录（几乎是 Opus 的 ~8.6% 的两倍，超出此前最高成绩 8 个百分点）。在 agent 型 Vending-Bench 测试中，Grok 4 表现卓越，平均净收益达 $4694.15，售出 4569 单位，远超 Claude Opus 4（$2077.41，1412 单位）、人类（$844.05，344 单位）和其他模型。

Grok 4 Heavy 在 2025 年 USAMO（美国数学奥林匹克）中取得 61.9% 的成绩，并且是第一个在“人类最终考试”文本子集中得分 50.7% 的模型，展示了其通过大规模强化学习和原生工具使用在复杂推理方面的卓越能力。

![](https://p.ipic.vip/q9q120.png)

### GPQA（科学问答）

| 模型                      | 得分 |
| ------------------------- | ---- |
| Grok 4 Heavy（含 Python） | 88.4 |
| Grok 4                    | 87.5 |
| Gemini 2.5 Pro            | 86.4 |
| o3                        | 83.3 |
| Claude Opus 4             | 79.6 |

### LiveCodeBench（1 月 - 5 月，竞赛编程）

| 模型                      | 得分 |
| ------------------------- | ---- |
| Grok 4 Heavy（含 Python） | 79.4 |
| Grok 4（含 Python）       | 79.3 |
| Grok 4                    | 79   |
| Gemini 2.5 Pro            | 74.2 |
| o3                        | 72   |

### USAMO 2025（奥林匹克数学证明）

| 模型                      | 得分 |
| ------------------------- | ---- |
| Grok 4 Heavy（含 Python） | 61.9 |
| Gemini Deep Think         | 49.4 |
| Grok 4                    | 37.5 |
| Gemini 2.5 Pro            | 34.5 |
| o3                        | 21.7 |

### HMMT 2025（竞赛数学）

| 模型                      | 得分 |
| ------------------------- | ---- |
| Grok 4 Heavy（含 Python） | 96.7 |
| Grok 4（含 Python）       | 93.9 |
| Grok 4                    | 90   |
| Gemini 2.5 Pro            | 82.5 |
| o3                        | 77.5 |
| Claude Opus 4             | 58.3 |

### AIME’25（竞赛数学）

| 模型                      | 得分 |
| ------------------------- | ---- |
| Grok 4 Heavy（含 Python） | 100  |
| Grok 4（含 Python）       | 98.8 |
| o3（含 Python）           | 98.4 |
| Grok 4                    | 91.7 |
| o3                        | 88.9 |
| Gemini 2.5 Pro            | 88   |
| Claude Opus 4             | 75.5 |

### ARC-AGI-2（抽象与推理）

| 模型           | 得分 |
| -------------- | ---- |
| Grok 4         | 15.9 |
| Claude Opus 4  | 8.6  |
| o3             | 6.5  |
| Gemini 2.5 Pro | 4.9  |

------

## 5 Grok 4 API

Grok 4 API 为开发者提供了前沿级别的多模态理解能力，拥有 256,000 的上下文窗口和强大的推理能力，能够处理文本和视觉等复杂任务。API 支持跨 X、网页和各大新闻源的实时数据搜索，借助原生工具使用实现准确及时的回答。它还具备企业级安全与合规性，包括 SOC 2 Type 2、GDPR 和 CCPA 等认证，确保在敏感应用中的可靠性。Grok 4 即将支持云服务合作伙伴，方便企业大规模部署创新 AI 解决方案。

## 6 Grok 4 语音模式

通过升级后的语音模式与 Grok 交流，体验更加真实、快速、智能的语音互动。我们全新推出了一个沉静自然的声音，并重新设计了对话流程，使其更贴近真实对话。

现在，Grok 还能“看见”你所看到的内容！只需打开摄像头，说出问题，Grok 就会在语音聊天过程中实时分析画面并回应你。这个模型由我们自主训练，结合了最先进的强化学习框架和语音压缩技术。

![Grok 应用中语音模式通过摄像头讲解所见画面](https://x.ai/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fvoice-vision.270067a9.webp&w=3840&q=75)

开启语音聊天时的视频功能，Grok 就能在与你交谈时“看见”画面内容。

## 7 接下来的计划

xAI 将继续把强化学习扩展到前所未有的水平，基于 Grok 4 的技术进展，不断突破 AI 的边界。我们计划将训练目标从可验证的奖励机制扩展到处理真实世界中的复杂问题，使模型能在动态环境中学习与适应。

多模态能力也将持续增强，整合视觉、音频等感知，提供更自然直观的交互体验。总体目标仍是让模型变得更智能、更快速、更高效，真正实现帮助人类解决深层问题的愿景。

参考：

- https://x.ai/news/grok-4