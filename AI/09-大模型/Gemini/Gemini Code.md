https://developers.google.com/gemini-code-assist/resources/release-notes

## 2025年7月18日-提高代码完成速度

#### VS Code Gemini Code Assist `2.41.0` VS Code Gemini 代码辅助 `2. 41. 0`

VS Code Extension `2.41.0` 提高了代码完成建议速度。

## 2025年7月17日-检查点、选定的代码片段和终端输出以及其他功能现已普遍可用 (GA)

 以下功能于 2025 年 5 月和 6 月以预览版形式推出，现已正式发布：

- [Revert to checkpoints 恢复到检查点](https://developers.google.com/gemini-code-assist/docs/chat-gemini#revert_to_a_checkpoint_in_chat)
- [Add selected code snippets to context
  将选定的代码片段添加到上下文中](https://developers.google.com/gemini-code-assist/docs/chat-gemini#add_selected_code_snippets_to_context)
- [Prompt Gemini Code Assist with selected terminal output
  使用选定的终端输出提示双子代码辅助](https://developers.google.com/gemini-code-assist/docs/chat-gemini#prompt_with_selected_terminal_output_using_chat)
- [Specify filenames in your workspace
  在工作区中指定文件名](https://developers.google.com/gemini-code-assist/docs/chat-gemini#specify_files_and_folders_in_your_workspace_context)
- [Exclude files from Gemini Code Assist use
  从 Gemini Code Assist 使用中排除文件](https://developers.google.com/gemini-code-assist/docs/create-aiexclude-file)

## 2025年7月15日-Thinking tokens

在 Gemini Code Assist 做出回应之前，您会看到对 Gemini 思维过程的洞察，以表明 Gemini 正在积极处理您的请求。

![Thinking tokens in VS Code Gemini Code Assist.](https://cloud.google.com/gemini/images/release-notes-images/vscode-thinking-tokens.gif)

## 2025年7月1日-Gemini 2.5 Pro 和 Gemini 2.5 Flash 正式上市 (GA)

Gemini 2.5 Pro 和 Gemini 2.5 Flash 现已正式发布 (GA)。这些型号适用于 Gemini Code Assist 的*所有*用户层级，并支持 Gemini Code Assist 的聊天、代码生成和代码转换功能。

集成 Gemini 2.5 Pro 和 Gemini 2.5 Flash 稳定版后，Gemini Code Assist 处理复杂任务的能力将得到显著提升。这些模型在编码、数学、科学和复杂推理等领域表现出色，能够提供更准确、更实用的建议。

![Gemini Code Assist 2.5 Flash and Pro comparison chart.](https://cloud.google.com/gemini/images/release-notes-images/gca-flash-pro-chart.png)

## 2025年6月25日

### 在 Gemini Code Assist 中使用代理模式，为 VS Code 的个人内部频道提供服务（预览版）

![Review and approve](https://cloud.google.com/gemini/images/release-notes-images/vscode-agent-mode-approve-changes.gif)

**通过交互式审查和批准保持控制** 。

您可以在代理模式下使用 Gemini Code Assist 聊天功能，完成复杂、多步骤的任务和目标，并完全掌控每一项更改。在进行任何修改之前，代理会提供一份计划供您审核。您可以编辑、请求更改、批准或拒绝任何建议的更改。这种协作方式将 AI 的强大功能与您的专业知识相结合，从而生成更优质的代码并提升工作流程的效率。

要开始使用代理模式，请参阅[使用代理聊天作为结对程序员 ](https://developers.google.com/gemini-code-assist/docs/use-agentic-chat-pair-programmer)。

### 在 Gemini Code Assist 中使用多文件编辑功能，供 VS Code 的个人内部频道以代理模式使用（预览版）

![Multi-file edits](https://cloud.google.com/gemini/images/release-notes-images/vscode-agent-mode-multi-file-edit.gif)

**告别单文件编辑，迎接项目范围的更改** 。

借助代理模式下的多文件编辑功能，代理可以根据单个提示对您的整个代码库进行并发更改。这项强大的新功能简化了大规模重构、功能实现和错误修复的流程。只需描述您需要的更改，代理就会智能地识别并修改所有相关文件，从而节省您的时间和精力。如果您想将更改恢复到先前的状态，还可以选择撤消对本地文件的更改。

### 在 Gemini Code Assist 中为 VS Code 代理模式下的个人内部人员频道使用完整的项目上下文（预览）

![Explain my codebase](https://cloud.google.com/gemini/images/release-notes-images/vscode-agent-mode-explain-codebase.gif)

**更智能、更准确的代码建议，具有完整的项目意识** 。

代理模式可以全面了解您的整个项目。代理会分析您的整个代码库，并根据您的目标按需请求文件和文件夹。完整的项目上下文使代理能够创建更准确、上下文感知的代码补全、建议和重构。这种对项目架构、依赖项和编码模式的更深入理解意味着您可以更轻松地获得更高质量、更一致的代码。

## June 18, 2025 2025年6月18日

### Chat code suggestion preview 聊天代码建议预览

Chat code suggestions are [displayed in a preview block](https://developers.google.com/gemini-code-assist/docs/chat-gemini#configure_preview_pane) by default with VS Code Gemini Code Assist `2.37.0`, improving the readability of generated chat responses. You can [configure preview pane settings](https://developers.google.com/gemini-code-assist/docs/chat-gemini#configure_preview_pane) to alternatively display chat code suggestions as fully collapsed or expanded.
在 VS Code Gemini Code Assist `2.37.0` 中，聊天代码建议默认[显示在预览块中 ](https://developers.google.com/gemini-code-assist/docs/chat-gemini#configure_preview_pane)，从而提高了生成的聊天响应的可读性。您可以[配置预览窗格设置 ](https://developers.google.com/gemini-code-assist/docs/chat-gemini#configure_preview_pane)，以完全折叠或展开的方式显示聊天代码建议。

![Preview pane in VS Code Gemini Code Assist.](https://cloud.google.com/gemini/images/release-notes-images/vscode-preview-pane.gif)

### Revert to a checkpoint (Preview) 恢复到检查点（预览）

You can [revert to a checkpoint for chat code suggestions](https://developers.google.com/gemini-code-assist/docs/chat-gemini#revert_to_a_checkpoint_in_chat) with VS Code Gemini Code Assist `2.37.0`, in Preview. Reverting to a checkpoint enables you to revert affected source files to a point before any code suggestions were applied.
您可以使用 VS Code Gemini Code Assist `2.37.0` 预览版[恢复到聊天代码建议的检查点 ](https://developers.google.com/gemini-code-assist/docs/chat-gemini#revert_to_a_checkpoint_in_chat)。恢复到检查点功能允许您将受影响的源文件恢复到应用任何代码建议之前的某个时间点。

![Preview pane in VS Code Gemini Code Assist.](https://cloud.google.com/gemini/images/release-notes-images/vscode-revert-to-checkpoint.gif)

### Configure local codebase awareness 配置本地代码库感知

You can [configure local codebase awareness](https://developers.google.com/gemini-code-assist/docs/configure-local-codebase-awareness) with VS Code Gemini Code Assist `2.37.0`.
您可以使用 VS Code Gemini Code Assist `2.37.0`[ 配置本地代码库感知 ](https://developers.google.com/gemini-code-assist/docs/configure-local-codebase-awareness)。

## June 12, 2025 2025年6月12日

### Configure AI exclusion files 配置 AI 排除文件

You can now [configure the use of `.aiexclude` and `.gitignore` files](https://developers.google.com/gemini-code-assist/docs/create-aiexclude-file#configure_context_exclusion_settings) to exclude files from the local context with VS Code Gemini Code Assist (version `2.36.0`).
现在，您可以[配置 `.aiexclude` 和 `.gitignore` 文件的使用 ](https://developers.google.com/gemini-code-assist/docs/create-aiexclude-file#configure_context_exclusion_settings)，以使用 VS Code Gemini Code Assist（版本 `2.36.0` ）从本地上下文中排除文件。

### Add code snippets to the chat context 将代码片段添加到聊天上下文

You can now select, attach, and direct Gemini to focus on code snippets with VS Code Gemini Code Assist (version `2.36.0`). [Code snippet selection](https://developers.google.com/gemini-code-assist/docs/chat-gemini#add_selected_code_snippets_to_context) enables discrete analysis of smaller code blocks instead of entire files, as Preview.
现在，您可以使用 VS Code Gemini Code Assist（版本 `2.36.0` ）选择、附加并指示 Gemini 专注于代码片段。[ 代码片段选择](https://developers.google.com/gemini-code-assist/docs/chat-gemini#add_selected_code_snippets_to_context)功能支持对较小的代码块（而非整个文件）进行离散分析，就像预览一样。

![Selected code snippets in VS Code Gemini Code Assist](https://cloud.google.com/gemini/images/release-notes-images/vscode-code-snippets.gif)

### Add terminal output to the chat context 将终端输出添加到聊天上下文

Terminal output can now be [attached to the chat context](https://developers.google.com/gemini-code-assist/docs/chat-gemini#prompt_with_selected_terminal_output_using_chat) with VS Code Gemini Code Assist (version `2.36.0`). You can now ask Gemini Code Assist questions about terminal commands and output, as Preview.
现在可以使用 VS Code Gemini Code Assist（版本 `2.36.0` ）将终端输出[附加到聊天上下文 ](https://developers.google.com/gemini-code-assist/docs/chat-gemini#prompt_with_selected_terminal_output_using_chat)。现在，您可以向 Gemini Code Assist 询问有关终端命令和输出的问题（预览版）。

![Selected terminal output in VS Code Gemini Code Assist](https://cloud.google.com/gemini/images/release-notes-images/vscode-terminal-snippets.gif)

## June 05, 2025 2025年6月5日

### Stop in-progress chat responses 停止正在进行的聊天回复

You can now [stop chat responses](https://developers.google.com/gemini-code-assist/docs/chat-gemini#stop_in-progress_chat) with VS Code Gemini Code Assist (version `2.35.0`). Undesired long running or errant chat responses are immediately halted.
您现在可以使用 VS Code Gemini Code Assist (版本 `2.35.0` )[ 停止聊天响应 ](https://developers.google.com/gemini-code-assist/docs/chat-gemini#stop_in-progress_chat)。长时间运行或错误的聊天响应会被立即停止。

![Stop chat responses.](https://cloud.google.com/gemini/images/release-notes-images/vscode-stop-chat.png)

### Clickable filenames in chat (Preview) 聊天中可点击的文件名（预览）

You can now click filenames referenced in a chat response to open the file in the IDE with VS Code Gemini Code Assist (`2.35.0`), as Preview.
现在，您可以单击聊天回复中引用的文件名，使用 VS Code Gemini Code Assist ( `2.35.0` ) 在 IDE 中以预览形式打开该文件。

![Clickable filenames in VS Code.](https://cloud.google.com/gemini/images/release-notes-images/vscode-clickable-filenames.png)

### Exclude files from local context (Preview) 从本地上下文中排除文件（预览）

[Context exclusion of files using `.gitIgnore` is now enforced](https://developers.google.com/gemini-code-assist/docs/create-aiexclude-file). Files present in `.gitignore` are now excluded from the local context with chat, code generation, code completion, and code transformation, as Preview.
[现在强制使用 `.gitIgnore` 进行文件上下文排除 ](https://developers.google.com/gemini-code-assist/docs/create-aiexclude-file)`.gitignore` 中存在的文件现在将从聊天、代码生成、代码补全和代码转换的本地上下文中排除（预览版）。

### Automatic scrolling 自动滚动

VS Code Gemini Code Assist (version `2.35.0`) now [automatically scrolls through chat responses](https://developers.google.com/gemini-code-assist/docs/chat-gemini#configure_automatic_scrolling), enabling easier and faster readability. You can disable automatic scrolling in the Gemini Code Assist settings.
VS Code Gemini 代码助手 (版本 `2.35.0` ) 现[可自动滚动浏览聊天回复 ](https://developers.google.com/gemini-code-assist/docs/chat-gemini#configure_automatic_scrolling)，从而更轻松、更快速地提供可读性。您可以在 Gemini 代码助手设置中禁用自动滚动功能。

![Automatic scrolling in VS Code Gemini Code Assist.](https://cloud.google.com/gemini/images/release-notes-images/vscode-automatic-scrolling.gif)

## May 28, 2025 2025年5月28日

### Manage files and folders in the Context Drawer 在上下文抽屉中管理文件和文件夹

You can now [view and manage files and folders requested to be included in Gemini Code Assist's context, using the Context Drawer](https://developers.google.com/gemini-code-assist/docs/chat-gemini#manage_files_and_folders_in_the_context_drawer). After you specify a file or folder to be used as context for your Gemini Code Assist prompts, these files and folders are placed in the Context Drawer, where you can review and remove them from the prompt context.
现在，您可以[使用上下文抽屉 (Context Drawer) 查看和管理请求包含在 Gemini Code Assist 上下文中的文件和文件夹 ](https://developers.google.com/gemini-code-assist/docs/chat-gemini#manage_files_and_folders_in_the_context_drawer)。指定要用作 Gemini Code Assist 提示上下文的文件或文件夹后，这些文件和文件夹将被放置在上下文抽屉中，您可以在其中查看它们并将其从提示上下文中移除。

This gives you more control over which information Gemini Code Assist considers when responding to your prompts.
这使您可以更好地控制 Gemini Code Assist 在响应您的提示时考虑哪些信息。

![Context Drawer for Gemini Code Assist for VS Code](https://cloud.google.com/gemini/images/vscode-context-drawer.png)

## May 22, 2025 2025年5月22日

Gemini Code Assist now uses Gemini 2.5. These models are now used in the following experiences:
Gemini Code Assist 现采用 Gemini 2.5 版本。这些模型现已应用于以下体验：

- Chat 聊天
- Code generation 代码生成
- Code transformation 代码转换

## May 14, 2025 2025年5月14日

### Create custom commands 创建自定义命令

You can now [configure and use custom commands](https://developers.google.com/gemini-code-assist/docs/chat-gemini#create_custom_commands) with IntelliJ Gemini Code Assist (version `1.15.0`). Create, save, and execute your own pre-configured prompts to perform repetitive tasks faster and more easily in the IDE.
现在，您可以使用 IntelliJ Gemini Code Assist（版本 `1.15.0` ）[ 配置和使用自定义命令 ](https://developers.google.com/gemini-code-assist/docs/chat-gemini#create_custom_commands)。创建、保存并执行您自己的预配置提示，以便在 IDE 中更快、更轻松地执行重复性任务。

To view the custom commands settings, go to **Settings** > **Tools** > **Gemini** > **Prompt Library**.
要查看自定义命令设置，请转到**设置** > **工具** > **Gemini** > **提示库** 。

![IntelliJ Prompt Library](https://cloud.google.com/gemini/images/intellij-prompt-library.png)

Chat responses with error messages now have action buttons for IntelliJ Gemini Code Assist (version `1.15.0`).
带有错误消息的聊天回复现在具有 IntelliJ Gemini Code Assist（版本 `1.15.0` ）的操作按钮。

## May 06, 2025 2025年5月6日

### Prompt with folders in your local workspace (Preview) 使用本地工作区中的文件夹进行提示（预览）

You can now [include folders from your local IDE project](https://developers.google.com/gemini-code-assist/docs/write-code-gemini#intellij_11) for IntelliJ Gemini Code Assist (version `1.14.0`) to use as context for your prompts, in [Preview](https://cloud.google.com/products#product-launch-stages). To specify a folder in your chat prompt, type @ and select the folder you want to specify.
现在，您可以在[预览版](https://cloud.google.com/products#product-launch-stages)中将[本地 IDE 项目中的文件夹添加](https://developers.google.com/gemini-code-assist/docs/write-code-gemini#intellij_11)到 IntelliJ Gemini Code Assist（版本 `1.14.0` ）中，用作提示的上下文。要在聊天提示中指定文件夹，请输入 @ 并选择要指定的文件夹。

Directing Code Assist to add folders to your chat can improve responses by specifying use of the contents within your selected folder(s), with support up to a 1M token context window.
指示代码助手将文件夹添加到您的聊天中可以通过指定使用所选文件夹中的内容来改善响应，并支持最多 1M 令牌上下文窗口。

![folders gif](https://cloud.google.com/gemini/images/release-notes-images/folders.gif)

## April 30, 2025 2025年4月30日

You can now [include folders from your local IDE project](https://developers.google.com/gemini-code-assist/docs/write-code-gemini#prompt_with_specific_files_and_folders_in_your_workspace_with_local_codebase_awareness) for IntelliJ Gemini Code Assist (version `1.14.0`) to use as context for your prompts, in Preview.
现在，您可以在预览中将[本地 IDE 项目中的文件夹包含](https://developers.google.com/gemini-code-assist/docs/write-code-gemini#prompt_with_specific_files_and_folders_in_your_workspace_with_local_codebase_awareness)在 IntelliJ Gemini Code Assist（版本 `1.14.0` ）中，以用作提示的上下文。

## April 29, 2025 2025年4月29日

VS Code Gemini Code Assist (version `2.32.0`) now supports [creation and management of multiple chats](https://developers.google.com/gemini-code-assist/docs/write-code-gemini#create_multiple_chats).
VS Code Gemini Code Assist（版本 `2.32.0` ）现在支持[创建和管理多个聊天 ](https://developers.google.com/gemini-code-assist/docs/write-code-gemini#create_multiple_chats)。

VS Code Gemini Code Assist (version `2.32.0`) now supports streamlined multi-part chat code suggestions. You have the option to accept a single code change or all suggested changes.
VS Code Gemini 代码助手 (版本 `2.32.0` ) 现支持精简的多部分聊天代码建议。您可以选择接受单个代码更改或所有建议的更改。

You can now [specify and apply rules](https://developers.google.com/gemini-code-assist/docs/chat-gemini#create_rules) to each chat request with VS Code Gemini Code Assist (version `2.32.0`).
现在，您可以使用 VS Code Gemini Code Assist（版本 `2.32.0` ）[ 指定并应用规则](https://developers.google.com/gemini-code-assist/docs/chat-gemini#create_rules)到每个聊天请求。

## April 15, 2025 2025年4月15日

Fixed markdown rendering issues in chat for IntelliJ Gemini Code Assist.
修复了 IntelliJ Gemini Code Assist 聊天中的 markdown 渲染问题。

## April 09, 2025 2025年4月9日

Gemini Code Assist for individuals tools are in Preview. You can use tools to access external services from your IDE. To learn more about tools, see the [Gemini Code Assist tools overview](https://developers.google.com/gemini-code-assist/docs/tools-agents/tools-overview).
Gemini Code Assist 个人版工具目前处于预览阶段。您可以使用这些工具从 IDE 访问外部服务。要了解更多关于这些工具的信息，请参阅 [Gemini Code Assist 工具概述 ](https://developers.google.com/gemini-code-assist/docs/tools-agents/tools-overview)。

Gemini Code Assist for individuals now uses Gemini 2.5 in Chat, excluding tools, which provides improved coding performance.
个人版 Gemini Code Assist 现在在聊天中使用 Gemini 2.5（不包括工具），从而提高了编码性能。

Streamed chat responses are now generally available for IntelliJ and VS Code Gemini Code Assist. You can disable this feature in settings.
IntelliJ 和 VS Code Gemini Code Assist 现已正式支持流式聊天回复。您可以在设置中禁用此功能。

## April 01, 2025 2025年4月1日

Code customization for chat is generally available for VS Code and IntelliJ Gemini Code Assist. This feature provides contextually relevant code suggestions and insights in your IDE's Gemini Code Assist chat interface. Code customization for chat is available without any additional configuration required. For more information on how to use code customization for chat effectively, see [Use code customization](https://developers.google.com/gemini-code-assist/docs/use-code-customization).
VS Code 和 IntelliJ Gemini Code Assist 现已正式支持聊天代码自定义功能。此功能可在 IDE 的 Gemini Code Assist 聊天界面中提供与上下文相关的代码建议和洞察。聊天代码自定义功能无需任何额外配置即可使用。有关如何有效使用聊天代码自定义的更多信息，请参阅[使用代码自定义 ](https://developers.google.com/gemini-code-assist/docs/use-code-customization)。

## March 28, 2025 2025年3月28日

Local codebase awareness is now available for IntelliJ Gemini Code Assist. You can now include files from your local IDE project in the prompt context by typing @ in the chat prompt box.
IntelliJ Gemini Code Assist 现已支持本地代码库感知功能。您现在可以通过在聊天提示框中输入 @ 来将本地 IDE 项目中的文件添加到提示上下文中。

You can now see what files are used by IntelliJ Gemini Code Assist chat and can customize the context as needed.
您现在可以查看 IntelliJ Gemini Code Assist 聊天使用了哪些文件，并可以根据需要自定义上下文。

## March 18, 2025 2025年3月18日

Streamed chat responses are now available in public preview for IntelliJ and VS Code Gemini Code Assist. You can disable this feature in settings.
IntelliJ 和 VS Code Gemini Code Assist 的流式聊天回复现已开放公开预览。您可以在设置中禁用此功能。

You can now configure and use custom commands in the inline chat menu and lightbulb menu for VS Code Gemini Code Assist. To view custom commands settings, go to **Settings** > **Gemini Code Assist** > **Custom Commands**.
您现在可以在 VS Code Gemini Code Assist 的内联聊天菜单和灯泡菜单中配置和使用自定义命令。要查看自定义命令设置，请前往 **“设置”** > **Gemini Code Assist** > **自定义命令** 。

Fixed an issue with an infinite progress bar while trying to log in to IntelliJ Gemini Code Assist.
修复了尝试登录 IntelliJ Gemini Code Assist 时出现无限进度条的问题。

## March 12, 2025 2025年3月12日

Gemini Code Assist now supports data residency at rest. Data residency meets compliance and regulatory requirements by allowing you to specify the geographic locations (regions) where Gemini Code Assist data is stored.
Gemini Code Assist 现已支持静态数据驻留。数据驻留功能允许您指定 Gemini Code Assist 数据的存储地理位置（区域），从而满足合规性和监管要求。

You can now use code customization for Gemini Code Assist Enterprise with VPC Service Controls. This allows secure access to on-premises source control systems. For more information, see [Configure VPC Service Controls for Gemini](https://developers.google.com/gemini-code-assist/docs/configure-vpc-service-controls).
您现在可以使用 VPC Service Controls 为 Gemini Code Assist Enterprise 进行代码自定义。这允许安全访问本地源代码控制系统。有关更多信息，请参阅[为 Gemini 配置 VPC Service Controls](https://developers.google.com/gemini-code-assist/docs/configure-vpc-service-controls) 。

## March 05, 2025 2025年3月5日

Various bug fixes and minor product enhancements for IntelliJ Gemini Code Assist extension.
IntelliJ Gemini Code Assist 扩展的各种错误修复和小的产品增强。

## February 25, 2025 2025年2月25日

Gemini Code Assist for individuals is now available. Gemini Code Assist for individuals is free and uses the same models as Gemini Code Assist Standard and Enterprise, including a tailored version of Gemini 2.0 for coding use cases. For more information, see the [blog post](https://blog.google/technology/developers/gemini-code-assist-free).
Gemini Code Assist 个人版现已推出。Gemini Code Assist 个人版免费，采用与 Gemini Code Assist 标准版和企业版相同的模型，并包含针对编码用例的 Gemini 2.0 定制版本。更多信息，请参阅[博客文章 ](https://blog.google/technology/developers/gemini-code-assist-free)。

## February 21, 2025 2025年2月21日

Gemini Code Assist now uses a code-optimized version of Gemini 2.0. This new model is used in the following experiences:
Gemini Code Assist 现采用代码优化版 Gemini 2.0。此新模型应用于以下体验：

- Chat 聊天
- Code generation 代码生成
- Code transformation 代码转换

## February 19, 2025 2025年2月19日

IntelliJ Gemini Code Assist now shows disconnected network status in the Gemini status bar instead of an error.
IntelliJ Gemini Code Assist 现在在 Gemini 状态栏中显示断开的网络状态而不是错误。

## February 18, 2025 2025年2月18日

Code customization for Gemini Code Assist Enterprise now supports repositories hosted on the following:
Gemini Code Assist Enterprise 的代码定制现在支持以下托管的存储库：

- [GitHub Enterprise Cloud GitHub 企业云](https://cloud.google.com/developer-connect/docs/connect-github-enterprise-cloud)
- [GitHub Enterprise GitHub 企业版](https://cloud.google.com/developer-connect/docs/connect-github-enterprise)
- [GitLab](https://cloud.google.com/developer-connect/docs/connect-gitlab)
- [GitLab Enterprise GitLab 企业版](https://cloud.google.com/developer-connect/docs/connect-gitlab-enterprise)
- [Bitbucket Cloud Bitbucket 云](https://cloud.google.com/developer-connect/docs/connect-bitbucket-cloud)
- [Bitbucket Data Center Bitbucket 数据中心](https://cloud.google.com/developer-connect/docs/connect-bitbucket-dc)

## February 07, 2025 2025年2月7日

Various bug fixes and minor product enhancements for VSCode Gemini Code Assist extension.
VSCode Gemini Code Assist 扩展的各种错误修复和小的产品增强。

## February 06, 2025 2025年2月6日

IntelliJ Gemini Code Assist now has a setting to block suggestions that contain citations.
IntelliJ Gemini Code Assist 现在具有阻止包含引用的建议的设置。

Fixed issues with Google Cloud project settings for VS Code Gemini Code Assist.
修复了 VS Code Gemini Code Assist 的 Google Cloud 项目设置问题。

## January 30, 2025 2025年1月30日

For new customers with billing accounts that have never had a Gemini Code Assist subscription, we automatically apply up to 50 license credits for the first month, regardless of Gemini Code Assist edition. For more information, see [Set up Gemini Code Assist](https://cloud.google.com/gemini/docs/discover/set-up-gemini).
对于从未订阅过 Gemini Code Assist 的新客户，无论其 Gemini Code Assist 版本如何，我们都会在第一个月自动提供最多 50 个许可证积分。更多信息，请参阅[设置 Gemini Code Assist](https://cloud.google.com/gemini/docs/discover/set-up-gemini) 。

## January 22, 2025 2025年1月22日

IntelliJ Gemini Code Assist now provides citations in Gemini Chat. When you insert code from the Gemini Code Assist chat pane, and the code has citations, those citations are displayed in the editor.
IntelliJ Gemini Code Assist 现在可在 Gemini Chat 中提供引用功能。当您从 Gemini Code Assist 聊天窗格插入代码时，如果该代码包含引用，则这些引用将显示在编辑器中。

Admins can now block all suggestions containing citations during code completion, generation, and chat conversation for VS Code Gemini Code Assist. If the admin level citations block is enabled, the local citations size limit is set to `0`.
管理员现在可以在 VS Code Gemini Code Assist 的代码补全、生成和聊天对话期间屏蔽所有包含引用的建议。如果启用了管理员级别的引用屏蔽，则本地引用大小限制将设置为 `0` 。

## January 09, 2025 2025年1月9日

Various bug fixes and minor product enhancements for VSCode and IntelliJ Gemini Code Assist extension.
针对 VSCode 和 IntelliJ Gemini Code Assist 扩展的各种错误修复和小的产品增强。

## December 12, 2024 2024年12月12日

Customer-managed encryption keys (CMEK) are now integrated with code customization for Gemini Code Assist. You can now use a CMEK to encrypt and control data-at-rest in a cloud service through [Cloud Key Management Service](https://cloud.google.com/security/products/security-key-management). For more information, see [Encrypt data with customer-managed encryption keys](https://cloud.google.com/gemini/docs/codeassist/encrypt-data-cmek).
客户管理的加密密钥 (CMEK) 现已与 Gemini Code Assist 的代码自定义功能集成。您现在可以使用 CMEK 通过[云密钥管理服务 (Cloud Key Management Service)](https://cloud.google.com/security/products/security-key-management) 加密和控制云服务中的静态数据。有关更多信息，请参阅[使用客户管理的加密密钥加密数据 ](https://cloud.google.com/gemini/docs/codeassist/encrypt-data-cmek)。

The maximum number of repositories that can be indexed for Gemini Code Assist code customization has increased to 20,000. The maximum number of repositories per repository group is 500. For more information, see [Code customization overview](https://cloud.google.com/gemini/docs/codeassist/code-customization-overview#limitations).
Gemini Code Assist 代码定制中可索引的存储库数量上限已增至 20,000 个。每个存储库组的最大存储库数量为 500 个。更多信息，请参阅[代码定制概述 ](https://cloud.google.com/gemini/docs/codeassist/code-customization-overview#limitations)。

## December 11, 2024 2024年12月11日

[VS Code Gemini Code Assist](https://marketplace.visualstudio.com/items?itemName=google.geminicodeassist) is now a separate extension from Cloud Code.
[VS Code Gemini Code Assist](https://marketplace.visualstudio.com/items?itemName=google.geminicodeassist) 现在是 Cloud Code 的独立扩展。

You can now adjust the `cloudcode.duetAI.recitation.maxCitedLength` setting in the settings UI for VS Code Gemini Code Assist. You no longer need to update the `settings.json` file to make this adjustment. For more information, see [Disable code suggestions that match cited sources](https://cloud.google.com/gemini/docs/codeassist/write-code-gemini#disable_code_suggestions_that_match_cited_sources).
您现在可以在 VS Code Gemini Code Assist 的设置界面中调整 `cloudcode.duetAI.recitation.maxCitedLength` 设置。您无需再更新 `settings.json` 文件即可进行此调整。有关更多信息，请参阅[禁用与引用来源匹配的代码建议 ](https://cloud.google.com/gemini/docs/codeassist/write-code-gemini#disable_code_suggestions_that_match_cited_sources)。

## December 09, 2024 2024年12月9日

Gemini Code Assist for [IntelliJ](https://cloud.google.com/gemini/docs/codeassist/write-code-gemini#generate_code_with_prompts) now supports code transformation. Press Alt+\ (for Windows and Linux) or Cmd+\ (for macOS) to use commands or natural language prompts in the Quick Pick bar to request modifications to your code, and get a diff view to show pending changes to your code.
Gemini Code Assist for [IntelliJ](https://cloud.google.com/gemini/docs/codeassist/write-code-gemini#generate_code_with_prompts) 现已支持代码转换。按 Alt+\ （适用于 Windows 和 Linux）或 Cmd+\ （适用于 macOS）即可使用快速选择栏中的命令或自然语言提示来请求修改代码，并获取差异视图以显示待处理的代码更改。

## November 20, 2024 2024年11月20日

Gemini Code Assist clients are communicating with a new API (`cloudcode-pa.googleapis.com`), which may require updates to your configuration. See [Required APIs for users behind firewalls](https://cloud.google.com/gemini/docs/discover/set-up-gemini#required_apis_for_users_behind_firewalls) and [Configure VPC Service Controls](https://cloud.google.com/gemini/docs/discover/set-up-gemini#vpc-sc) for more information.
Gemini Code Assist 客户端正在与新的 API ( `cloudcode-pa.googleapis.com` ) 通信，这可能需要您更新配置。有关更多信息，请参阅[防火墙后用户所需的 API](https://cloud.google.com/gemini/docs/discover/set-up-gemini#required_apis_for_users_behind_firewalls) 和[配置 VPC 服务控制 ](https://cloud.google.com/gemini/docs/discover/set-up-gemini#vpc-sc)。

## November 12, 2024 2024年11月12日

[IntelliJ Gemini Code Assist](https://plugins.jetbrains.com/plugin/24198-gemini-code-assist) is now a separate extension from Cloud Code.
[IntelliJ Gemini Code Assist](https://plugins.jetbrains.com/plugin/24198-gemini-code-assist) 现在是 Cloud Code 的独立扩展。

## November 11, 2024 2024年11月11日

You can now automatically or manually assign Gemini Code Assist licenses. For more information, see [Manage Gemini for Google Cloud licenses](https://cloud.google.com/gemini/docs/manage-licenses).
您现在可以自动或手动分配 Gemini Code Assist 许可证。有关更多信息，请参阅[管理 Gemini for Google Cloud 许可证 ](https://cloud.google.com/gemini/docs/manage-licenses)。

## November 05, 2024 2024年11月5日

You can now purchase Gemini Code Assist Enterprise edition on the **Admin for Gemini** page within the Google Cloud console. For more information, see [Add or change Gemini for Google Cloud subscriptions](https://cloud.google.com/gemini/docs/admin).
您现在可以在 Google Cloud 控制台的 **Gemini 管理员**页面上购买 Gemini Code Assist 企业版。有关详情，请参阅[添加或更改 Gemini for Google Cloud 订阅 ](https://cloud.google.com/gemini/docs/admin)。

## October 30, 2024 2024年10月30日

Improved display of selected files using the `@` file picker in the staged prompt for VS Code Gemini Code Assist.
使用 VS Code Gemini Code Assist 的阶段提示中的 `@` 文件选择器改进了所选文件的显示。

Improved error handling for inline code completion for IntelliJ Gemini Code Assist.
改进了 IntelliJ Gemini Code Assist 内联代码完成的错误处理。

The IntelliJ Gemini Code Assist right-click menu items are now listed under **Gemini**, and are also available in the floating toolbar (the yellow bulb icon).
IntelliJ Gemini Code Assist 右键单击菜单项现在列在 **Gemini** 下，并且也可在浮动工具栏（黄色灯泡图标）中使用。

General bug fixes and improvements for IntelliJ Gemini Code Assist.
IntelliJ Gemini Code Assist 的常规错误修复和改进。

VS Code and IntelliJ Gemini Code Assist now support admin feedback block. This feature allows admins to control whether developers can send Gemini Code Assist feedback.
VS Code 和 IntelliJ Gemini Code Assist 现已支持管理员反馈阻止功能。此功能允许管理员控制开发者是否允许发送 Gemini Code Assist 反馈。

## October 16, 2024 2024年10月16日

General bug fixes and improvements to code transformation for the VS Code Gemini Code Assist extension.
对 VS Code Gemini Code Assist 扩展的代码转换进行了常规错误修复和改进。

Improved error handling for the IntelliJ Gemini Code Assist plugin. Added more actions to quickly recover from errors.
改进了 IntelliJ Gemini Code Assist 插件的错误处理。添加了更多操作以快速从错误中恢复。

## October 09, 2024 2024年10月9日

The Standard and Enterprise editions for Gemini Code Assist are now available. For more information about supported features for each edition, see the [supported features table](https://cloud.google.com/gemini/docs/codeassist/overview#supported-features).
Gemini Code Assist 的标准版和企业版现已推出。有关各版本支持功能的更多信息，请参阅[支持功能表 ](https://cloud.google.com/gemini/docs/codeassist/overview#supported-features)。

## October 07, 2024 2024年10月7日

You can now assign Gemini Code Assist subscription licenses to users in your organization. Use the **Admin for Gemini** page in the Google Cloud console or use the `billingAccounts.orders` API to view, assign, or unassign user licenses. For more information, see [Manage Gemini for Google Cloud licenses](https://cloud.google.com/gemini/docs/manage-licenses).
您现在可以将 Gemini Code Assist 订阅许可分配给组织中的用户。您可以使用 Google Cloud 控制台中的 **“Gemini 管理”** 页面或 `billingAccounts.orders` API 查看、分配或取消分配用户许可。如需了解更多信息，请参阅[管理 Gemini for Google Cloud 许可 ](https://cloud.google.com/gemini/docs/manage-licenses)。

## September 30, 2024 2024年9月30日

Code customization is now [generally available](https://cloud.google.com/products#product-launch-stages) in Gemini Code Assist for:
代码定制现已在 Gemini Code Assist 中[推出，](https://cloud.google.com/products#product-launch-stages) 适用于：

- VS Code with the Gemini Code Assist + Cloud Code extension (version 2.18.0+)
  带有 Gemini Code Assist + Cloud Code 扩展的 VS Code（版本 2.18.0+）
- IntelliJ with the Gemini Code Assist/Cloud Code plugin (version 1.1.0)
  带有 Gemini Code Assist/Cloud Code 插件的 IntelliJ（版本 1.1.0）
- Cloud Workstations 云工作站
- Cloud Shell Editor Cloud Shell 编辑器

With code customization, Gemini Code Assist lets you get enhanced code suggestions based on your organization's private codebase(s) (in [GitHub.com](https://github.com/) or [GitLab.com](https://about.gitlab.com/)) and in line with your coding conventions. For more information, see the [code customization overview](https://cloud.google.com/gemini/docs/codeassist/code-customization-overview).
通过代码自定义，Gemini Code Assist 可让您根据组织的私有代码库（位于 [GitHub.com](https://github.com/) 或 [GitLab.com](https://about.gitlab.com/) 中）并符合您的编码约定，获得增强的代码建议。有关更多信息，请参阅[代码自定义概述 ](https://cloud.google.com/gemini/docs/codeassist/code-customization-overview)。

Local codebase awareness is now available for VS Code Gemini Code Assist. Local codebase awareness helps you with high quality code suggestions that are syntactically correct and semantically meaningful within the broader context of your codebase, when you specify files for Gemini Code Assist to use as context. You can specify files in your chat prompt by typing `@` and selecting the files. For more information, see [Code with Gemini Code Assist](https://cloud.google.com/code/docs/vscode/write-code-gemini#prompt_with_specific_files_in_your_workspace_with_local_codebase_awareness).
VS Code Gemini Code Assist 现已支持本地代码库感知功能。当您指定文件供 Gemini Code Assist 用作上下文时，本地代码库感知功能可为您提供高质量的代码建议，这些建议在代码库的更广泛上下文中语法正确且语义有意义。您可以在聊天提示中输入 `@` 并选择文件来指定文件。更多信息，请参阅[使用 Gemini Code Assist 进行编码 ](https://cloud.google.com/code/docs/vscode/write-code-gemini#prompt_with_specific_files_in_your_workspace_with_local_codebase_awareness)。

Gemini Code Assist for [VS Code](https://cloud.google.com/code/docs/vscode/write-code-gemini#generate_code_with_prompts), [Cloud Shell](https://cloud.google.com/code/docs/shell/write-code-gemini#use-code-transformation-features), and [Cloud Workstations](https://cloud.google.com/workstations/docs/write-code-gemini#use-code-transformation-features) now supports code transformation. You can use commands or natural language prompts in the Quick Pick bar to request modifications to your code, and get a diff view to show pending changes to your code.
Gemini Code Assist 现已支持 [VS Code](https://cloud.google.com/code/docs/vscode/write-code-gemini#generate_code_with_prompts) 、 [Cloud Shell](https://cloud.google.com/code/docs/shell/write-code-gemini#use-code-transformation-features) 和 [Cloud Workstations 的](https://cloud.google.com/workstations/docs/write-code-gemini#use-code-transformation-features)代码转换。您可以使用“快速选择”栏中的命令或自然语言提示来请求修改代码，并获取差异视图以显示待处理的代码更改。

Improved error handling for the IntelliJ Gemini Code Assist plugin. Error messages are now actionable with precise error reasons and are supported for chat, code completion, and code generation.
改进了 IntelliJ Gemini Code Assist 插件的错误处理。现在，错误消息可由用户根据具体错误原因进行处理，并支持聊天、代码补全和代码生成。

## September 05, 2024 2024年9月5日

You can now use Gemini Code Assist's **Commit** message button, which is integrated into JetBrains IDEs' default **Commit** tool window, to generate a commit message based on your changes.
您现在可以使用 Gemini Code Assist 的**提交**消息按钮（集成到 JetBrains IDE 的默认**提交**工具窗口中）根据您的更改生成提交消息。

General bug fixes for IntelliJ Gemini Code Assist.
IntelliJ Gemini Code Assist 的常规错误修复。

## August 12, 2024 2024年8月12日

The IntelliJ Gemini Code Assist/Cloud Code plugin now installs a bundle of two plugins:
IntelliJ Gemini Code Assist/Cloud Code 插件现在安装了两个插件包：

- Google Cloud Code 谷歌云代码
- Gemini Code Assist 双子座代码辅助

Chat conversation history now persists across IDE sessions and restarts for IntelliJ Gemini Code Assist.
聊天对话历史记录现在会在 IDE 会话中保留，并为 IntelliJ Gemini Code Assist 重新启动。

You can now exclude any files from AI completions and chat interactions using `.aiexclude` in IntelliJ Gemini Code Assist, with a format similar to `.gitignore`.
现在，您可以使用 IntelliJ Gemini Code Assist 中的 `.aiexclude` 从 AI 补全和聊天交互中排除任何文件，其格式类似于 `.gitignore` 。

Improved performance and reliability in IntelliJ Gemini Code Assist.
提高了 IntelliJ Gemini Code Assist 的性能和可靠性。

Code completions for IntelliJ Gemini Code Assist are now completely integrated with native IDE completion framework, which makes it easier to use and override key combinations to trigger, and less conflicting with various IDE or Tab based actions.
IntelliJ Gemini Code Assist 的代码补全现已与原生 IDE 补全框架完全集成，这使得使用和覆盖要触发的组合键变得更加容易，并且与各种 IDE 或基于 Tab 的操作的冲突更少。

Updated IntelliJ Gemini Code Assist chat UX and styling.
更新了 IntelliJ Gemini Code Assist 聊天 UX 和样式。

You can now use the arrow keys to traverse previous chat history entries to repeat and edit questions in IntelliJ Gemini Code Assist.
您现在可以使用箭头键遍历以前的聊天历史记录条目，以在 IntelliJ Gemini Code Assist 中重复和编辑问题。

## July 30, 2024 2024年7月30日

The VS Code Gemini Code Assist extension now supports Code Transformation. Press `Ctrl+I` for Windows and Linux or `Cmd+I` for macOS and use the `/generate` command to generate code based on your prompt anywhere in the file. For more information, see [Code with Gemini Code Assist](https://cloud.google.com/code/docs/vscode/write-code-gemini#prompt-gemini-in-code-file).
VS Code Gemini Code Assist 扩展现已支持代码转换。在 Windows 和 Linux 系统中按 `Ctrl+I` ，在 macOS 系统中 `Cmd+I` ，然后使用 `/generate` 命令，即可根据提示在文件中的任意位置生成代码。更多信息，请参阅[使用 Gemini Code Assist 进行编码 ](https://cloud.google.com/code/docs/vscode/write-code-gemini#prompt-gemini-in-code-file)。

Fixed crashing issue when using VS Code Gemini Code Assist completions.
修复了使用 VS Code Gemini Code Assist 完成时崩溃的问题。

## July 02, 2024 2024年7月2日

Gemini Code Assist is available to try at no cost until November 8, 2024, limited to one user per billing account. Customers can purchase a license on the [Gemini Admin page](https://console.cloud.google.com/gemini-admin) in the Google Cloud console. For more information, see [Set up Gemini Code Assist for a project](https://cloud.google.com/gemini/docs/discover/set-up-gemini).
Gemini Code Assist 可免费试用至 2024 年 11 月 8 日，每个结算帐号仅限一位用户使用。客户可以在 Google Cloud 控制台的 [Gemini Admin 页面](https://console.cloud.google.com/gemini-admin)购买许可。更多信息，请参阅[为项目设置 Gemini Code Assist](https://cloud.google.com/gemini/docs/discover/set-up-gemini) 。

## June 25, 2024 2024年6月25日

Added a tips card in chat showing useful keyboard shortcuts for using the VS Code Gemini Code Assist plug-in.
在聊天中添加了提示卡，显示使用 VS Code Gemini Code Assist 插件的有用键盘快捷键。

Added a diff button to code snippets in chat so that code returned from Gemini Code Assist can be easily compared to the code in the VS Code editor.
在聊天中的代码片段中添加了一个差异按钮，以便可以轻松地将 Gemini Code Assist 返回的代码与 VS Code 编辑器中的代码进行比较。

Rejected Gemini Code Assist code suggestions will no longer come back in the VS Code Gemini plug-in.
被拒绝的 Gemini Code Assist 代码建议将不再出现在 VS Code Gemini 插件中。

## June 11, 2024 2024年6月11日

Updated Gemini Code Assist to use [Gemini 1.5 Flash model](https://deepmind.google/technologies/gemini/flash/) (with 32k token window), with improved support for code explanation, unit test generation, and code transformations. Automatically triggered code completions use an 8k token window.
更新了 Gemini Code Assist，使其能够使用 [Gemini 1.5 Flash 模型 ](https://deepmind.google/technologies/gemini/flash/)（带有 32k 令牌窗口），并改进了对代码解释、单元测试生成和代码转换的支持。自动触发的代码补全使用 8k 令牌窗口。

General bug fixes and completion speed improvements in the Intellij Gemini plug-in.
Intellij Gemini 插件中的常规错误修复和完成速度改进。

Fixed issue where citation warnings would persist after being ignored or deleted.
解决了引用警告被忽略或删除后仍然存在的问题。

## May 15, 2024 2024年5月15日

API management: Use Gemini Code Assist to facilitate API design including OpenAPI spec generation with enterprise context from natural language prompts and built in visual API designer to further refine the specification.
API 管理：使用 Gemini Code Assist 促进 API 设计，包括根据自然语言提示使用企业上下文生成 OpenAPI 规范，并内置可视化 API 设计器以进一步完善规范。

API hub interaction from Cloud Code: An update to Cloud Code extension enables you to interact with any API in your API hub using a local mock server in Cloud Code, make changes to the API, and publish it back to API hub.
来自 Cloud Code 的 API 中心交互：Cloud Code 扩展的更新使您能够使用 Cloud Code 中的本地模拟服务器与 API 中心中的任何 API 进行交互，对 API 进行更改，然后将其发布回 API 中心。

## May 08, 2024 2024年5月8日

Added notification on how to set up [VPC configuration for Gemini Code Assist](https://cloud.google.com/gemini/docs/configure-vpc-service-controls#configure-perimeter).
添加了有关如何[为 Gemini Code Assist 设置 VPC 配置的](https://cloud.google.com/gemini/docs/configure-vpc-service-controls#configure-perimeter)通知。

Gemini Code Assist is transitioning to a new API. This release adds a safeguard should users encounter errors.
Gemini Code Assist 正在过渡到新的 API。此版本添加了一项安全措施，以防用户遇到错误。

## April 29, 2024 2024年4月29日

The Intellij Gemini plug-in name updated to Gemini Code Assist / Cloud Code for easier discoverability in the marketplace.
Intellij Gemini 插件名称已更新为 Gemini Code Assist / Cloud Code，以便在市场上更容易发现。

Yaml file support is now disabled by default for new installations of the Intellij Gemini plug-in. To enable support, please visit the settings (Settings > Tools > Gemini + Google Cloud Code > Kubernetes).
新安装的 Intellij Gemini 插件现在默认禁用 Yaml 文件支持。如需启用支持，请访问设置（设置 > 工具 > Gemini + Google Cloud Code > Kubernetes）。

The VS Code Gemini plug-in name is updated in the marketplace to Gemini Code Assist + Google Cloud Code for easier discoverability.
VS Code Gemini 插件名称在市场中更新为 Gemini Code Assist + Google Cloud Code，以便于发现。

Revamped our Gemini Code Assist enablement flow in VSCode Gemini plug-in. It now requires less steps, is more intuitive, and has a sleek new look.
改进了 VSCode Gemini 插件中 Gemini Code Assist 的启用流程。现在，启用流程步骤更少、更加直观，并拥有更简洁的全新外观。

## April 16, 2024 2024年4月16日

Various fixes for Gemini error handling.
针对 Gemini 错误处理进行各种修复。

## April 09, 2024 2024年4月9日

The Code Transformation features in Gemini Code Assist are now available in [Public Preview](https://cloud.google.com/products#product-launch-stages) for Cloud Shell. For more information, see [Use Code Transformation features](https://cloud.google.com/code/docs/shell/write-code-gemini#use-code-transformation-features).
Gemini Code Assist 中的代码转换功能现已在 Cloud Shell 的[公共预览版](https://cloud.google.com/products#product-launch-stages)中推出。有关更多信息，请参阅[使用代码转换功能 ](https://cloud.google.com/code/docs/shell/write-code-gemini#use-code-transformation-features)。

Gemini Code Assist is available to try at no cost until July 11, 2024, limited to one user per billing account. Customers can purchase a license on the [Gemini Admin page](https://console.cloud.google.com/gemini-admin) in the Google Cloud console. For more information, see [Set up Gemini Code Assist for a project](https://cloud.google.com/gemini/docs/discover/set-up-gemini).
Gemini Code Assist 可免费试用至 2024 年 7 月 11 日，每个结算帐号仅限一位用户使用。客户可以在 Google Cloud 控制台的 [Gemini Admin 页面](https://console.cloud.google.com/gemini-admin)购买许可。如需了解更多信息，请参阅[为项目设置 Gemini Code Assist](https://cloud.google.com/gemini/docs/discover/set-up-gemini) 。

The Transform Code features in Gemini Code Assist are now available in [Public Preview](https://cloud.google.com/products#product-launch-stages) for [Workstations](https://cloud.google.com/workstations/docs/write-code-gemini#use-code-transformation-features) and [Cloud Shell](https://cloud.google.com/code/docs/shell/write-code-gemini#use-code-transformation-features).
Gemini Code Assist 中的 Transform Code 功能现已在 [Workstations](https://cloud.google.com/workstations/docs/write-code-gemini#use-code-transformation-features) 和 [Cloud Shell](https://cloud.google.com/code/docs/shell/write-code-gemini#use-code-transformation-features) 的[公共预览版](https://cloud.google.com/products#product-launch-stages)中提供。

## April 08, 2024 2024年4月8日

Code Transformations for Gemini Code Assist in Cloud Shell are now available for [Public Preview](https://cloud.google.com/products#product-launch-stages). You can now use an inline text box directly in your code file to do the following:
Cloud Shell 中 Gemini Code Assist 的代码转换功能现已开放[公开预览 ](https://cloud.google.com/products#product-launch-stages)。现在，您可以直接在代码文件中使用内联文本框执行以下操作：

- Generate comment lines to document your code.
  生成注释行来记录您的代码。
- Troubleshoot code with issues.
  解决代码问题。
- Improve code readability.
  提高代码的可读性。
- Make code more efficient.
  使代码更高效。

You can also view context sources of a generated response in the **Gemini: Chat** pane.
您还可以在 **Gemini：聊天**窗格中查看生成的响应的上下文源。

For more information, see [Code with Gemini Code Assist](https://cloud.google.com/code/docs/shell/write-code-gemini#use-code-transformation-features).
有关更多信息，请参阅[使用 Gemini Code Assist 进行编码 ](https://cloud.google.com/code/docs/shell/write-code-gemini#use-code-transformation-features)。

## April 03, 2024 2024年4月3日

The Gemini Code Assist code generation feature now allows users to generate code repeatedly at the same cursor location.
Gemini Code Assist 代码生成功能现在允许用户在同一光标位置重复生成代码。

Fixed an issue where handling very small .csv, .tsv, and .jsonl files crashed the Gemini Cloud Code extension.
修复了处理非常小的 .csv、.tsv 和 .jsonl 文件导致 Gemini Cloud Code 扩展崩溃的问题。

Fixed an issue where empty files weren't being included as chat context.
修复了空文件未包含在聊天上下文中的问题。

Fixed telemetry data issue in the [VSCode Gemini Code Assist plug-in](https://cloud.google.com/code).
修复了 [VSCode Gemini Code Assist 插件](https://cloud.google.com/code)中的遥测数据问题。

## April 02, 2024 2024年4月2日

Duet AI for Developers is now Gemini Code Assist.
Duet AI for Developers 现在是 Gemini Code Assist。

The code generation feature has been updated to the Intellij Duet AI plug-in to allow users to generate code repeatedly at the same cursor location.
Intellij Duet AI 插件的代码生成功能已更新，允许用户在同一光标位置重复生成代码。

Fixed an issue in the Intellij Duet AI plug-in where empty files were not being included as chat context.
修复了 Intellij Duet AI 插件中空文件未包含为聊天上下文的问题。

## March 19, 2024 2024年3月19日

Retrieval-augmented generation is rolling out in the [Intellij Duet AI plug-in](https://cloud.google.com/code) as an experiment to select users to improve code suggestions for their repository.
检索增强生成正在 [Intellij Duet AI 插件](https://cloud.google.com/code)中推出，作为一项实验，旨在选择用户改进其存储库的代码建议。

Added thumbs up and thumbs down buttons in the [Intellij Duet AI plug-in](https://cloud.google.com/code) chat window for quick response quality feedback .
在 [Intellij Duet AI 插件](https://cloud.google.com/code)聊天窗口中添加了竖起大拇指和竖起大拇指按钮，以便快速响应质量反馈。

You can now use Duet AI to help you create a synthetic monitor in [Cloud Monitoring](https://cloud.google.com/monitoring). This feature is in Public Preview. For more information, see [Create a synthetic monitor](https://cloud.google.com/monitoring/synthetic-monitors/create).
现在，您可以使用 Duet AI 在 [Cloud Monitoring](https://cloud.google.com/monitoring) 中创建合成监控器。此功能目前处于公开预览阶段。有关更多信息，请参阅[创建合成监控器 ](https://cloud.google.com/monitoring/synthetic-monitors/create)。

Improved notifications when Duet AI needs action or has an error.
当 Duet AI 需要采取行动或出现错误时改进通知。

Improved notifications when the [Intellij Duet AI plug-in](https://cloud.google.com/code) needs action or has an error.
当 [Intellij Duet AI 插件](https://cloud.google.com/code)需要操作或出现错误时，改进通知。

The [Intellij Duet AI plug-in](https://cloud.google.com/code) [Skaffold version](https://cloud.google.com/deploy/docs/using-skaffold/select-skaffold) has been updated to 2.10.1
[Intellij Duet AI 插件 ](https://cloud.google.com/code)[Skaffold 版本](https://cloud.google.com/deploy/docs/using-skaffold/select-skaffold)已更新至 2.10.1

The context source panel in the [VSCode Duet AI plug-in](https://cloud.google.com/code) chat window now displays which file was included in the user's chat request.
[VSCode Duet AI 插件](https://cloud.google.com/code)聊天窗口中的上下文源面板现在显示用户聊天请求中包含哪个文件。

Local code customization is enabled in the [VSCode Duet AI plug-in](https://cloud.google.com/code) as an experiment to select users to improve code suggestions. Additional files in the user's local directory will open when the user requests code generation. Local files are scored and included in the response based on relevance to the user's active context.
[VSCode Duet AI 插件](https://cloud.google.com/code)已启用本地代码自定义功能，旨在筛选用户改进代码建议。当用户请求代码生成时，本地目录中的其他文件将会打开。本地文件会根据与用户当前上下文的相关性进行评分，并包含在响应中。

Thumbs up and down in Duet AI chat for quick response quality feedback.
在 Duet AI 聊天中点赞或踩点，以获得快速响应的质量反馈。

Improved error handling and messaging in the [VSCode Duet AI plug-in](https://cloud.google.com/code) code editor and in chat. Error specific actions are now provided to better assist users in resolving errors.
改进了 [VSCode Duet AI 插件](https://cloud.google.com/code)代码编辑器和聊天中的错误处理和消息传递功能。现在提供针对错误的特定操作，以更好地帮助用户解决错误。

Improved confusing messaging and troubleshooting steps for permission denied errors and API enablement errors in the [VSCode Duet AI plug-in](https://cloud.google.com/code).
改进了 [VSCode Duet AI 插件](https://cloud.google.com/code)中权限被拒绝错误和 API 启用错误的混乱消息和故障排除步骤。

## March 14, 2024 2024年3月14日

Updated Duet AI for Developers chat assistance and Smart Actions to use [Gemini 1.0 Pro models](https://ai.google.dev/models/gemini).
更新了 Duet AI，为开发人员提供聊天协助和智能操作以使用 [Gemini 1.0 Pro 模型 ](https://ai.google.dev/models/gemini)。

Updated Duet AI for Developers code completion model to increase response accuracy.
更新了 Duet AI for Developers 代码完成模型以提高响应准确性。

Improved code completion triggering logic to reduce occurrences of code completion previously dismissed.
改进的代码完成触发逻辑以减少先前忽略的代码完成的发生。

Duet AI Chat in Google Cloud console now uses Gemini 1.0 Pro models when giving responses.
Google Cloud 控制台中的 Duet AI Chat 现在在做出回应时使用 Gemini 1.0 Pro 模型。

## March 05, 2024 2024年3月5日 2024年3月5日

Duet AI Chat UI improvements, such as better message rendering and code formatting when using "Explain this" smart action
Duet AI 聊天 UI 改进，例如使用“解释此”智能操作时更好的消息呈现和代码格式

## January 31, 2024 2024年1月31日 2024年1月31日

Duet AI for Developers is available to try at no cost until May 11, 2024, limited to one user per billing account. Customers can purchase a license on the [Duet AI Admin page](https://console.cloud.google.com/duet-ai-admin) in the Google Cloud console. For more information, see [Set up Duet AI for a project](https://cloud.google.com/duet-ai/docs/discover/set-up-duet-ai).
Duet AI for Developers 现已免费试用，有效期至 2024 年 5 月 11 日，每个结算帐号仅限一位用户使用。客户可以在 Google Cloud 控制台的 [Duet AI 管理页面](https://console.cloud.google.com/duet-ai-admin)购买许可。如需了解更多信息，请参阅[为项目设置 Duet AI](https://cloud.google.com/duet-ai/docs/discover/set-up-duet-ai) 。

## January 23, 2024 2024年1月23日 2024年1月23日

Fixed an issue where users with certain network/firewall configurations are unable to use Duet AI Chat.
修复了某些网络/防火墙配置的用户无法使用 Duet AI Chat 的问题。

Fixed an issue in VSCode Duet AI plug-in where users might get a notification about an unexpected error when enabling Duet AI.
修复了 VSCode Duet AI 插件中的一个问题，用户在启用 Duet AI 时可能会收到有关意外错误的通知。

Fixed an issue where some users were frequently asked to log in with VSCode Duet AI plug-in.
修复部分用户使用 VSCode Duet AI 插件频繁登录的问题。

Fixed an issue where VSCode Duet AI plug-in is occasionally unable to start after log in.
修复 VSCode Duet AI 插件登录后偶尔无法启动的问题。

## December 13, 2023 2023年12月13日

Duet AI for Developers is now GA!
Duet AI for Developers 现已正式发布！

Added a Duet AI code generation hint tooltip.
添加了 Duet AI 代码生成提示工具提示。

[Duet AI](https://cloud.google.com/duet-ai/docs), an AI-powered collaborator in Google Cloud, is [Generally available](https://cloud.google.com/products#product-launch-stages). The Generally available release includes the following:
[Duet AI](https://cloud.google.com/duet-ai/docs) 是 Google Cloud 中一款由 AI 驱动的协作工具，[ 现已正式发布 ](https://cloud.google.com/products#product-launch-stages)。正式发布版本包含以下内容：

- [Duet AI for Developers](https://cloud.google.com/duet-ai/docs/discover/developers) is [Generally available](https://cloud.google.com/products#product-launch-stages).
  [Duet AI for Developers](https://cloud.google.com/duet-ai/docs/discover/developers) 现已[全面上市 ](https://cloud.google.com/products#product-launch-stages)。
- The Duet AI pane in [VSCode](https://cloud.google.com/code/docs/vscode/write-code-duet-ai), [JetBrains](https://cloud.google.com/code/docs/intellij/write-code-duet-ai), [Cloud Workstations](https://cloud.google.com/workstations/docs/write-code-duet-ai) and [Cloud Shell Editor](https://cloud.google.com/code/docs/shell/write-code-duet-ai) uses an improved model with higher accuracy and more complete responses.
  [VSCode](https://cloud.google.com/code/docs/vscode/write-code-duet-ai) 、 [JetBrains](https://cloud.google.com/code/docs/intellij/write-code-duet-ai) 、 [Cloud Workstations](https://cloud.google.com/workstations/docs/write-code-duet-ai) 和 [Cloud Shell Editor](https://cloud.google.com/code/docs/shell/write-code-duet-ai) 中的 Duet AI 窗格使用了改进的模型，具有更高的准确性和更完整的响应。
- In [VSCode](https://cloud.google.com/code/docs/vscode/write-code-duet-ai), a sign-in expiration issue for Duet AI has been fixed.
  在 [VSCode](https://cloud.google.com/code/docs/vscode/write-code-duet-ai) 中，Duet AI 的登录过期问题已修复。
- In [VSCode](https://cloud.google.com/code/docs/vscode/write-code-duet-ai) and [JetBrains](https://cloud.google.com/code/docs/intellij/write-code-duet-ai) IDEs, you can enable Duet AI entirely from the IDE instead of visiting the Google Cloud console.
  在 [VSCode](https://cloud.google.com/code/docs/vscode/write-code-duet-ai) 和 [JetBrains](https://cloud.google.com/code/docs/intellij/write-code-duet-ai) IDE 中，您可以完全从 IDE 启用 Duet AI，而无需访问 Google Cloud 控制台。
- In [VSCode](https://cloud.google.com/code/docs/vscode/write-code-duet-ai) and [JetBrains](https://cloud.google.com/code/docs/intellij/write-code-duet-ai) IDEs, improved filtering and sorting reduces the amount of generated code that contains only comments.
  在 [VSCode](https://cloud.google.com/code/docs/vscode/write-code-duet-ai) 和 [JetBrains](https://cloud.google.com/code/docs/intellij/write-code-duet-ai) IDE 中，改进的过滤和排序减少了仅包含注释的生成代码量。
- Improved logic for defining the final code completion token. This resolves issues where extra closing brackets were suggested and where lines of code broken in the middle were returned.
  改进了定义最终代码补全标记的逻辑。这解决了建议使用多余的右括号以及返回中间断开的代码行的问题。
- In [JetBrains](https://cloud.google.com/code/docs/intellij/write-code-duet-ai) IDEs, Duet AI cancels code generation when the user changes the code position before a response is returned. This ensures generated code isn't rendered in the wrong place.
  在 [JetBrains](https://cloud.google.com/code/docs/intellij/write-code-duet-ai) IDE 中，如果用户在返回响应之前更改代码位置，Duet AI 会取消代码生成。这确保生成的代码不会渲染到错误的位置。

Added Duet AI smart actions. Highlight the desired code, right-click, and select:
添加了 Duet AI 智能操作。选中所需代码，右键单击，然后选择：

- "Explain this" to have Duet AI explain the code
  “解释一下”让 Duet AI 解释代码
- "Generate Unit Tests" to have Duet AI generate unit tests for you
  “生成单元测试”让 Duet AI 为您生成单元测试

Allow enabling Duet API during onboarding flow
允许在入职流程中启用 Duet API

Partially accept a Duet AI code suggestion with `Shift` + `Enter`
使用 `Shift` + `Enter` 部分接受 Duet AI 代码建议

Added detailed and actionable message to Duet AI error notification.
向 Duet AI 错误通知添加了详细且可操作的消息。

Added a Duet AI code generation hint tooltip in VSCode Duet AI plug-in.
在 VSCode Duet AI 插件中添加了 Duet AI 代码生成提示工具提示。

Fixed completion result only displaying one suggestion in VSCode Duet AI plug-in.
修复了 VSCode Duet AI 插件中完成结果仅显示一条建议的问题。

Fix Duet AI generation hint re-appearing between sessions in VSCode Duet AI plug-in.
修复 VSCode Duet AI 插件中会话之间重新出现 Duet AI 生成提示的问题。

Updated code generation shortcut to avoid key binding conflict with Intellij new UI
更新了代码生成快捷方式以避免与 Intellij 新 UI 的键绑定冲突

Added project selection from VSCode Duet AI plug-in quickpick.
添加了从 VSCode Duet AI 插件快速选择的项目选择。

Easier onboarding in VSCode Duet AI plug-in for Duet AI with API enable button.
使用 API 启用按钮，可以更轻松地在 VSCode Duet AI 插件中为 Duet AI 加入。

## November 01, 2023 2023年11月1日 2023年11月1日

Duet AI for Cloud Shell is now available. Use Duet AI, your AI-powered collaborator, to accomplish tasks more effectively and efficiently. Duet AI provides contextualized responses to your prompts to help guide you on what you're trying to do with your code. It also shares source citations regarding which documentation and code samples the assistant used to generate its responses.
Duet AI for Cloud Shell 现已推出。使用 Duet AI，您的 AI 协作助手，更高效地完成任务。Duet AI 会根据您的提示提供情境化响应，帮助您了解如何使用代码执行操作。它还会分享助手生成响应时所用文档和代码示例的来源引用。

If you use the latest version of the Cloud Shell editor, which is Code - OSS based, you can use Duet AI for Cloud Shell. For more information, see the [Duet AI in Google Cloud overview](https://cloud.google.com/duet-ai/docs/overview) and [Code with Duet AI assistance](https://cloud.google.com/code/docs/shell/write-code-duet-ai).
如果您使用基于 Code - OSS 的最新版 Cloud Shell 编辑器，则可以将 Duet AI 用于 Cloud Shell。有关更多信息，请参阅 [Google Cloud 中的 Duet AI 概览](https://cloud.google.com/duet-ai/docs/overview)和[使用 Duet AI 辅助编写代码 ](https://cloud.google.com/code/docs/shell/write-code-duet-ai)。

[VPC Service Controls for Duet AI](https://cloud.google.com/duet-ai/docs/configure-vpc-service-controls) is [Generally Available](https://cloud.google.com/products#product-launch-stages).
[Duet AI 的 VPC 服务控制](https://cloud.google.com/duet-ai/docs/configure-vpc-service-controls)[现已全面推出 ](https://cloud.google.com/products#product-launch-stages)。

[Duet AI for Cloud Workstations](https://cloud.google.com/workstations/docs/write-code-duet-ai) is available.
[适用于云工作站的 Duet AI](https://cloud.google.com/workstations/docs/write-code-duet-ai) 现已推出。

[Duet AI for VS Code](https://cloud.google.com/code/docs/vscode/write-code-duet-ai) is available in [Cloud Shell Editor](https://cloud.google.com/code/docs/shell/write-code-duet-ai).
[Duet AI for VS Code](https://cloud.google.com/code/docs/vscode/write-code-duet-ai) 可在 [Cloud Shell Editor](https://cloud.google.com/code/docs/shell/write-code-duet-ai) 中使用。

[Duet AI for VS Code](https://cloud.google.com/code/docs/vscode/write-code-duet-ai) provides an indication that code generation is in progress.
[Duet AI for VS Code](https://cloud.google.com/code/docs/vscode/write-code-duet-ai) 提供代码生成正在进行的指示。

[Duet AI](https://cloud.google.com/duet-ai/docs) has an improved threshold to filter out low quality code completions.
[Duet AI](https://cloud.google.com/duet-ai/docs) 具有改进的阈值，可以过滤掉低质量的代码完成。

[Duet AI](https://cloud.google.com/duet-ai/docs) added multi-file support for code generation.
[Duet AI](https://cloud.google.com/duet-ai/docs) 增加了对代码生成的多文件支持。

[Duet AI](https://cloud.google.com/duet-ai/docs) added multi-file support for code generation.
[Duet AI](https://cloud.google.com/duet-ai/docs) 增加了对代码生成的多文件支持。

You don't need to switch between using REST and gRPC when using [Duet AI](https://cloud.google.com/duet-ai/docs). Duet AI does this for you.
使用 [Duet AI](https://cloud.google.com/duet-ai/docs) 时，您无需在 REST 和 gRPC 之间切换。Duet AI 会为您完成这项工作。

The following improvements have been made when using [using IntelliJ IDEA and other JetBrains IDEs with Duet AI](https://cloud.google.com/code/docs/intellij/write-code-duet-ai):
将 [IntelliJ IDEA 和其他 JetBrains IDE 与 Duet AI 结合使用](https://cloud.google.com/code/docs/intellij/write-code-duet-ai)时，进行了以下改进：

- Duet AI supports linux ARM builds in IntelliJ
  Duet AI 在 IntelliJ 中支持 Linux ARM 构建
- A daily sign-in expiration issue has been fixed.
  每日签到过期问题已修复。
- Linux support on JetBrains IDE's has been improved.
  JetBrains IDE 对 Linux 的支持已得到改进。
- Chat in IntelliJ and JetBrains IDEs is available in [Preview](https://cloud.google.com/products#product-launch-stages).
  IntelliJ 和 JetBrains IDE 中的聊天功能目前处于[预览阶段 ](https://cloud.google.com/products#product-launch-stages)。
- Confidence scoring is enabled which should improve code completion quality.
  启用置信度评分可以提高代码完成质量。

[Duet AI](https://cloud.google.com/duet-ai/docs) for Developers code generation and code completion latency has been reduced when it's used in IntelliJ and VSCode.
[Duet AI](https://cloud.google.com/duet-ai/docs) for Developers 在 IntelliJ 和 VSCode 中使用时代码生成和代码完成延迟已减少。

[Duet AI](https://cloud.google.com/duet-ai/docs) in Google Cloud has fewer false positive responses and increased its response length for questions about code.
Google Cloud 中的 [Duet AI 的](https://cloud.google.com/duet-ai/docs)误报响应更少，并且增加了有关代码问题的响应长度。

## October 31, 2023 2023年10月31日 2023年10月31日

Duet AI Chat is now in preview for IntelliJ and JetBrains IDEs.
Duet AI Chat 现已推出 IntelliJ 和 JetBrains IDE 的预览版。

VPC Service Controls for Duet AI is in preview in the Intellij Duet AI plug-in.
Duet AI 的 VPC 服务控制在 Intellij Duet AI 插件中处于预览状态。

Improvements to the Duet AI code generation behavior including more relevant results and better feedback.
Duet AI 代码生成行为的改进包括更相关的结果和更好的反馈。

Improving the way the Intellij Duet AI plug-in code suggestions interact with the IDE's built in code completions. Duet AI will not display its suggestions when the IDE's code completions are available. To trigger Duet AI instead: hit "Escape" to dismiss the IDE's suggestions and Duet will automatically trigger.
改进了 Intellij Duet AI 插件代码建议与 IDE 内置代码补全功能的交互方式。当 IDE 的代码补全功能可用时，Duet AI 将不会显示其建议。要触发 Duet AI，请按“Esc”键关闭 IDE 的建议，Duet 将自动触发。

Update the Intellij Duet AI plug-in code generation gutter icon
更新 Intellij Duet AI 插件代码生成边栏图标

Improvements to the Intellij Duet AI plug-in error handling.
Intellij Duet AI 插件错误处理的改进。

Updated the button to generate code with the VSCode Duet AI plug-in.
更新了使用 VSCode Duet AI 插件生成代码的按钮。

You can ask the VSCode Duet AI plug-in to generate code, explain the currently opened file, or generate unit tests
您可以要求 VSCode Duet AI 插件生成代码、解释当前打开的文件或生成单元测试

Generating code in the VSCode Duet AI plug-in will now give a clear indication that generation is in progress
在 VSCode Duet AI 插件中生成代码现在将清楚地指示生成正在进行中

In the VSCode Duet AI plug-in, updated Duet AI Chat to include a button to open a code sample in a new file, and buttons to provide feedback on Duet AI response.
在 VSCode Duet AI 插件中，更新了 Duet AI Chat，其中包含一个用于在新文件中打开代码示例的按钮，以及用于提供有关 Duet AI 响应的反馈的按钮。

## October 03, 2023 2023年10月3日

Improved Intellij Duet AI plug-in filtering logic to provide better results.
改进了 Intellij Duet AI 插件过滤逻辑，以提供更好的结果。

Decreased the latency of VSCode Duet AI plug-in requests.
降低了 VSCode Duet AI 插件请求的延迟。

VSCode Duet AI plug-in should show more relevant completion results and fewer "noisy" results.
VSCode Duet AI 插件应该显示更多相关的完成结果和更少的“嘈杂”结果。

## September 20, 2023 2023年9月20日

Improved Intellij Duet AI plug-in filtering logic to provide better results.
改进了 Intellij Duet AI 插件过滤逻辑，以提供更好的结果。

## August 29, 2023 2023年8月29日

The Intellij Duet AI plug-in and VSCode Duet AI plug-in are now available in public preview. Use Duet AI, your AI-powered collaborator, to accomplish tasks more effectively and efficiently. Duet AI provides contextualized responses to your prompts to help guide you on what you're trying to do with your code. It also shares source citations regarding which documentation and code samples the assistant used to generate its responses. You can do this right in the IDE to avoid having to context-switch out to your browser or documentation.
Intellij Duet AI 插件和 VSCode Duet AI 插件现已开放公开预览。使用 Duet AI，您的 AI 协作助手，更高效地完成任务。Duet AI 会根据您的提示提供上下文相关的响应，帮助您了解如何使用代码进行操作。它还会分享助手生成响应时所用文档和代码示例的来源引用。您可以直接在 IDE 中执行此操作，无需切换到浏览器或文档。

[Duet AI](https://cloud.google.com/duet-ai/docs), an AI-powered collaborator in Google Cloud, is now available in [Preview](https://cloud.google.com/products/#product-launch-stages).
[Duet AI](https://cloud.google.com/duet-ai/docs) 是 Google Cloud 中由 AI 驱动的协作器，现已推出[预览版 ](https://cloud.google.com/products/#product-launch-stages)。