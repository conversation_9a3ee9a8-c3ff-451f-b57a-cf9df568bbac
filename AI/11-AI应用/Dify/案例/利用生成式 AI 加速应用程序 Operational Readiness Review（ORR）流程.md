在当今快速发展的技术环境中，确保应用程序在投入生产前达到高可用性、安全性和可靠性标准至关重要。运营就绪审查（Operational Readiness Review, ORR）作为一种系统化的评估方法，帮助团队在应用程序部署前识别潜在风险和改进机会。然而，传统的 ORR 流程往往耗时且需要大量人力资源。本文将介绍如何利用生成式 AI 技术和 AWS Well-Architected 框架简化 ORR 流程，并展示一个基于 Dify、Amazon Bedrock 和 AWS Well-Architected Tool 的解决方案。

## 什么是运营就绪审查（ORR）?

运营就绪审查（ORR）是一个结构化的评估过程，用于确定应用程序或系统是否已准备好投入生产环境。ORR 通常涵盖多个关键领域，包括:

- 安全性与合规性
- 可靠性与弹性
- 性能与效率
- 可操作性与可维护性
- 成本优化

ORR 的目标是在应用程序部署前识别并解决潜在问题，确保系统能够满足业务需求和技术标准。在做 ORR 的时候，运维和开发团队的负责人需要在公司统一的审查标准之上，对应用程序的各个方面进行审查，并最终生成报告。为了简化客户构建标准的过程，亚马逊云科技在 Well-Architected 框架下提供了一个全面的 ORR 评估基础，帮助团队构建安全、高性能、弹性强、高效且经济的应用程序。可参考此[链接](https://github.com/awslabs/operational-readiness-review-custom-war-lens)。

## 传统 ORR 流程面临的挑战

尽管 ORR 对于确保应用程序质量至关重要，但传统的 ORR 流程面临几个主要挑战：

- **时间密集型：**全面的 ORR 可能需要数天甚至数周时间，延迟产品上市时间
- **专业知识要求：**需要多个领域的专家参与审查过程
- **文档分析负担：**审查人员需要阅读和分析大量技术文档
- **一致性问题：**不同审查人员可能对相同问题有不同解读
- **知识传递困难：**审查结果和建议的有效传递存在挑战

这些挑战导致许多组织要么简化 ORR 流程（牺牲质量），要么投入大量资源（增加成本），或者完全跳过这一步骤（增加风险）。

## 利用生成式 AI 和 Dify 加速 ORR 流程

“ORR on Dify”是一个创新解决方案，它结合了 AWS Well-Architected 框架、大语言模型和 Dify 平台的能力，自动化 ORR 流程中的文档分析和评估环节。该解决方案能够:

- 自动分析应用程序设计文档
- 基于 AWS Well-Architected 框架评估应用程序架构
- 生成详细的审查报告，包括风险识别和改进建议
- 直接与 AWS Well-Architected Tool 集成，更新工作负载评估

通过这种方式，ORR 参与者可以将注意力集中在验证 AI 生成的评估结果和制定改进计划上，而不是花费大量时间阅读和分析文档。

## 解决方案架构

“ORR on Dify”解决方案由五个核心组件组成，架构如下图：

[![img](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai1.png)](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai1.png)

- **Dify** **平台：**提供知识库和大语言模型集成能力，用于文档处理和智能分析
- **Amazon Bedrock：**提供强大的基础模型能力，支持文档理解和专业评估
- **AWS Well-Architected Tool：**提供结构化的评估框架和 ORR 自定义镜头
- **AWS Lambda API：**与 AWS Well-Architected Tool 交互，获取评估模板和更新工作负载
- **Streamlit** **应用**: 提供用户友好的界面，用于文档上传、审查启动和结果展示

### 核心组件详解

#### Dify 平台

Dify 是一个开源的 LLMOps 平台，在此解决方案中主要提供：

- 知识库功能，用于存储和索引应用程序设计文档
- 大语言模型集成，用于文档理解和分析
- 工作流引擎，用于编排 ORR 评估流程
- 在工作流中定义各个步骤，基于 Bedrock 中的模型而完成复杂的分析任务

#### Amazon Bedrock

AWS 的生成式 AI 模型平台服务：

- 提供高性能的基础模型（如文本生成和 Embedding 模型等）
- 提供 Claude Haiku 文本生成模型，提供高性价比选择
- 提供 Titan Embedding v2 模型，用于知识的集成和召回

通过Bedrock，解决方案能理解技术文档的复杂性，并基于 AWS Well-Architected 框架进行专业评估。

#### AWS Well-Architected Tool

AWS Well-Architected Tool 是 AWS 提供的一项服务，用于评估架构并提供改进建议：

- 提供标准化的评估框架和最佳实践
- 支持自定义镜头，用于特定场景的评估
- AWS 提供专门的 ORR 自定义镜头，涵盖 ORR 的关键方面
- 记录和跟踪工作负载评估结果
- 生成详细的改进计划和风险报告

AWS ORR 自定义镜头是专为运营就绪审查设计的评估模板，包含一系列结构化问题和最佳实践，帮助团队全面评估应用程序的运营就绪状态。通过使用这一自定义镜头，组织可以确保其应用程序在部署前满足 AWS 推荐的运营标准。

#### AWS Lambda API

Lambda API 作为 AWS Well-Architected Tool 的接口层，提供两个主要功能:

- get_lens_info：获取 Well-Architected 自定义镜头（ORR 模板）信息
- operate_wa_tool：创建或更新 Well-Architected 工作负载评估

这些 API 使得解决方案能够无缝集成到 AWS Well-Architected 框架中。

#### Streamlit 应用

Streamlit 应用提供直观的用户界面，包含三个主要页面：

- **上传文档：**将应用程序设计文档上传到 Dify 知识库
- **审阅应用：**选择知识库和 ORR 模板，启动自动化审查
- **设置：**配置 API 连接和验证系统状态

### Dify 工作流详解

Dify 工作流是解决方案的核心，它定义了 ORR 评估的自动化流程。工作流文件 orr-on-llm-workflow-v5.yml 包含多个步骤，每个步骤执行特定功能：

[![img](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai2.gif)](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai2.gif)

[![img](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai3.gif)](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai3.gif)

1. **获取镜头信息（****Get Lens Info）**：

1. - 调用 Lambda API 获取 Well-Architected 自定义镜头信息
   - 提取评估支柱、问题和最佳实践
   - 为后续评估准备结构化模板

1. **知识库查询（****Knowledge Base Query）：**

1. - 基于评估问题查询上传的应用程序文档
   - 提取相关内容，为评估提供证据
   - 处理文档中的技术细节和架构信息

1. **评估分析（****Assessment Analysis）：**

- - 利用 Amazon Bedrock 模型分析文档内容（Prompt 如下图）
  - 基于 AWS Well-Architected 框架评估应用程序设计
  - 为每个问题生成风险评级和详细分析

[![img](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai4.png)](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai4.png)

1. **生成报告（****Report Generation）：**

1. - 汇总各个支柱的评估结果
   - 生成结构化的审查报告
   - 提供具体的改进建议和最佳实践参考

1. **更新工作负载（****Update Workload）**：

1. - 将评估结果同步到 AWS Well-Architected Tool
   - 创建或更新工作负载记录
   - 保存详细的评估答案和选择

工作流中的每个步骤都经过精心设计，确保评估过程的完整性和准确性。通过这种结构化的工作流，解决方案能够自动化执行传统上需要多位专家手动完成的评估任务。

## 部署与使用指南

### 部署流程

部署“ORR on Dify”解决方案需要以下步骤：

1. **准备** **AWS 环境**：

1. - 确保拥有 AWS 账户和必要权限
   - 安装 AWS CLI 并配置凭证

1. **部署** **Dify 服务**：

1. - 使用提供的 AWS CloudFormation 模板部署 Dify 服务
   - 记录 Dify 服务 URL 和访问凭证
   - 在 Dify 中配置 Bedrock 为模型提供商

1. **配置** **Amazon Bedrock 访问：**

1. - 确保账户已启用 Amazon Bedrock 服务
   - 获取必要的模型访问权限

1. **部署** **AWS Lambda API**：

1. - 使用 AWS CloudFormation 模板部署 Lambda 函数和 API Gateway
   - 记录 API 端点 URL

1. **导入** **AWS Well-Architected 自定义镜头**：

1. - 使用提供的工具导入 ORR 评估模板
   - 记录自定义镜头 ARN
   - 或使用 AWS 提供的 ORR 自定义镜头

1. **配置环境变量**：

1. - 设置 Dify 相关环境变量
   - 设置 AWS Lambda API 相关环境变量
   - 设置 AWS Well-Architected Tool 相关环境变量

1. **启动应用**：

详细的部署指南可在[项目](https://github.com/xzy0223/ORR-on-Dify)的 deploy/README.md 文件中找到。

### 使用流程

使用“ORR on Dify”进行应用程序审查的典型流程如下，项目启动后请通过链接访问应用：

1. **上传设计文档**：

- - 访问”上传文档”页面
  - 选择或创建知识库
  - 上传应用程序设计文档（支持 PDF、Word、Markdown 等格式）

[![img](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai5.png)](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai5.png)

1. **启动审查**：

- - 访问“审阅应用”页面
  - 选择包含设计文档的知识库
  - 选择适用的 ORR 模板
  - 按照提示录入信息
  - 点击“开始工作流”按钮

[![img](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai6.png)](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai6.png)

1. **查看审查结果**：

1. - 审查完成后，系统会显示详细的评估报告
   - 报告包括各个评估领域的风险级别和改进建议
   - 同时，AWS Well-Architected Tool 中的工作负载也会被更新

[![img](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai7.png)](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai7.png)

[![img](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai8.png)](https://s3.cn-north-1.amazonaws.com.cn/awschinablog/accelerating-the-application-operational-readiness-review-process-with-generative-ai8.png)

## 解决方案优势

“ORR on Dify”解决方案为 ORR 流程带来多项显著优势：

- **效率提升：**将文档分析时间从数天缩短到数分钟
- **一致性增强：**确保评估标准的一致应用
- **专业知识补充：**通过 Amazon Bedrock 提供领域专业知识
- 可追溯性：自动记录评估过程和结果
- **与** **AWS** **最佳实践对齐：**直接集成 AWS Well-Architected 框架和 ORR 自定义镜头
- **工作流自动化：**通过 Dify 工作流引擎实现端到端自动化

## 结论

运营就绪审查是确保应用程序质量和可靠性的关键步骤，但传统流程面临效率和资源挑战。“ORR on Dify”解决方案通过结合生成式 AI、Dify 平台、Amazon Bedrock 和 AWS Well-Architected Tool，显著简化了 ORR 流程，使团队能够更快、更一致地进行应用程序评估。

AWS 提供的 ORR 自定义镜头进一步增强了解决方案的价值，为评估提供了标准化的框架和最佳实践。通过将这一自定义镜头与生成式 AI 技术相结合，组织可以获得既符合 AWS 标准又高效的评估体验。

这种创新方法不仅提高了 ORR 的效率，还增强了评估质量，使组织能够在不牺牲速度的情况下维持高标准。随着生成式 AI 技术的不断发展，我们期待看到更多类似的解决方案出现，进一步优化软件开发和运营流程。

要开始使用“ORR on Dify”，请访问 [GitHub 仓库](https://github.com/xzy0223/ORR-on-Dify)获取完整的部署指南和源代码。