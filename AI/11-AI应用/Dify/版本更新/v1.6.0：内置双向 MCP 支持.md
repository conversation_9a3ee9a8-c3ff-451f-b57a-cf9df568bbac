AI 应用正迅速从简单对话迈向更复杂的任务执行。为了高效运作，智能体（Agent）需要访问外部数据、API、日历和代码库。过去，这通常意味着要编写大量自定义的“胶水代码”，成本高难扩展。

MCP标准化了 AI 智能体发现和使用外部服务的方式。此前版本用 MCP 工具须通过插件调用。而Dify 1.6.0d的MCP 支持已实现内置双向集成：

- [可以直接从 Dify 调用任何 MCP 服务。](https://docs.dify.ai/en/guides/tools/mcp)
- [也可以将你自己的 Dify 智能体或工作流暴露为 MCP 服务，供其他客户端调用。](https://docs.dify.ai/en/guides/application-publishing/publish-mcp)

这样的升级带来了更快速、更稳定的集成体验，同时也便于功能扩展。

# 在 Dify 中使用 MCP 的三种方式

## 将 MCP 服务配置为工具

在工具页中，选择 MCP 类型，然后添加一个服务器，比如[Linear](https://linear.app/docs/mcp)、[Notion](https://notion.notion.site/Beta-Overview-Notion-MCP-206efdeead058060a59bf2c14202bd0a)（原生 MCP 应用），或者[Zapier](https://mcp.zapier.com/mcp/servers)、[Composio](https://mcp.composio.dev/)（集成平台）。配置一次 Zapier，即可解锁 8000 多个授权应用。

注意：目前仅支持基于 HTTP 的 MCP 服务。[协议版本为 2025-03-26。](https://modelcontextprotocol.io/specification/2025-03-26/basic/authorization)

### Linear 配置步骤：

1. 进入工具 > MCP > 添加 MCP 服务
2. 填写 Linear MCP URL、显示名称和服务器标识符

![img](https://framerusercontent.com/images/aigmmeDSLRU40wRsGmVoQLk8.png)

1. 完成授权后，你将获得 22 个 Linear 工具，支持创建、更新和查询项目、任务、评论、文档、团队和用户信息。

![img](https://framerusercontent.com/images/lrOa1Sp2hYe5xgusxtkoO4Tj8YM.png)

## 智能体智能调用 MCP 工具

在提示词中定义智能体的角色，并连接 Linear 服务器：

“你是一个连接了 Linear 的智能体，拥有 22 个 API 工具。请根据需要使用它们来管理任务、项目和文档，并查询团队、用户和周期信息。”

![img](https://framerusercontent.com/images/wPnqJIJDC7BuXY9hS8OdvjzCD9M.png)

当用户要求为研发团队创建一个任务时，智能体会自动选择 get_team、get_user 和 create_issue 工具，创建并指派任务，无需人工操作。

## 在工作流中编排 MCP 工具

### 动态路径：Agent 节点

在工作流中插入“Linear 助手”智能体节点，运行时自动选择合适的 Linear 工具。这种方式适合处理复杂多变的任务，例如将用户反馈自动分发到三个专属智能体：

- 正面反馈智能体 → 将亮点内容发送给市场团队
- 技术问题智能体 → 为技术支持创建 Bug 任务
- 产品建议智能体 → 生成结构化需求文档，供产品团队使用

当某条反馈被分配给相应智能体后，系统会自动分析内容、设定优先级，并使用 Linear 的 MCP API 创建任务。过去需要手动处理的流程，如今几秒钟内即可完成，确保各部门快速响应用户。

![img](https://framerusercontent.com/images/QLaATePAnrIVCfygcucx75b1MU.png)

### 精准路径：独立 MCP 节点

也可以将 MCP 工具作为独立节点加入工作流，由你手动设定调用顺序，不依赖 LLM 决策。这种方式适用于：

- 标准化的业务流程
- 严格的任务顺序执行
- 对延迟或成本有严格要求的场景

你还可以添加知识库、通知插件或额外的 MCP 服务器，让多个平台协同工作，丰富整个流程。

![img](https://framerusercontent.com/images/RcRYQXzozI4chmobWD3i9KEBV2A.png)

# 将你的 AI 发布为 MCP 服务

任何 Dify 智能体或工作流都可以被发布为标准的 MCP 端点：

1. 服务描述：简明地说明工作流的作用，便于外部 LLM 判断何时调用
2. 参数说明：清晰列出 Start 节点的所有输入参数，确保客户端传参正确

![img](https://framerusercontent.com/images/TWc0EM0nZPNFIdA8F8shdUiU2g.png)

完成上述两项配置后，Dify 会生成一个服务器 URL，你的工作流就成为一个标准的 MCP 服务，Claude、Cursor 或其他支持 MCP 的客户端都可以直接调用。

![img](https://framerusercontent.com/images/KuG4P1o2AlnrId50wLyUzY1Bbpg.png)

# 为互联未来做好准备

MCP 的原生集成不仅是一个功能更新，更是一种对开放标准的承诺。你今天构建的应用，已为未来的 AI 互联生态打下基础。赶快体验 Dify v1.6.0 的新能力，开始构建属于你的智能系统吧。