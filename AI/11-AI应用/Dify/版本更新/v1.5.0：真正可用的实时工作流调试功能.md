## 0 前言

Dify 1.5.0记录每个节点的执行结果和实时追踪变量，让调试工作流程不再靠猜。开发者现可即时测试单个步骤，无需高成本重跑或手动输入，从“盲试”走向精准调试。

构建 AI 应用意味着处理复杂的逻辑链。你的工作流程可能从知识检索节点开始，调用实时数据工具，经过多个LLM节点推理，最终由模板节点整合生成输出。虽然功能强大，但调试这些流程却困难。

当最终结果偏离预期时，关键问题是：**到底哪里出了错？**也许RAG抓取无关文档，工具返回了错误数据，或 LLM 推理出现偏差……传统的工作流程就像“黑箱操作”。

你在一端输入，等待另一端输出，全凭运气。出现问题，只能翻日志、重跑流程，耗时又费 API 费用——一切都建立在猜。

开发者真正需要的是透明和控制，而不是日志猎人。因此，Dify 1.5.0 彻底重构了工作流构建与调试方式，让 AI 应用开发实现实时迭代。

## 1 之前的局限

Dify曾支持单步执行，让你可以单独测试某节点。但这有明显限制：

- **结果不保存**：节点执行结果不保留，每次调试得重新开始
- **变量手动输入**：每次调试都要手动输入该节点所需的所有变量，无法复用上游结果
- **信息分散**：需要逐个节点查看日志，无法整体把握数据流
- **重跑代价高**：每次查错都要重跑整个流程，包括那些已经没问题的 API 调用

当工作流异常，开发者像侦探一样逐个排查节点输出。被动式调试：

- 浪费时间
- 让开发过程缺乏洞察力，效率低下

TODO 原文视频帧

## 2 升级亮点

Dify v1.5.0 直击上述痛点，提供真正所见即所得（WYSIWYG）开发环境：

### 2.1 上次执行记录 & 单步可控运行

**上次执行记录**：每个节点现自动保存上次成功运行的全部信息，包括输入、输出和元数据。不管你是单步运行还是整个流程一键执行，系统都会像飞行记录仪，记录每步详情，为调试提供可靠依据。

**变量自动传递**：借助这记录功能，真正实现逐步调试。变量检查面板中存有节点所需的数据时，直接点击运行即可，系统会自动获取依赖并实时更新监控结果。就像在 Jupyter Notebook 中逐格执行一样，点哪个节点就运行哪个节点，数据关系由系统自动处理。

### 2.2 变量检查面板（Variable Inspect）

画布底部新增了一个[变量检查面板](https://docs.dify.ai/en/guides/workflow/debug-and-preview/variable-inspect)，作为全局控制中心，实时显示整个工作流中所有变量及其内容。无需在各个节点之间来回查找输入输出，所有数据一目了然。

这个面板的强大之处在于：**你可以直接修改变量值**，测试不同数据对后续节点的影响，而无需重跑那些成本高昂的上游操作，比如复杂的 LLM 调用或 API 请求。

这两项改进结合起来，让工作流程开发变得更加透明。每个节点的状态都能被保存和可视化，每一次调试都能快速定位问题并验证修改，复杂的 AI 应用开发就像搭积木一样简单。wow，这就像 IDEA 的 debug 栏工具一样！这才是真正的 LLM IDE！

## 3 实战：构建AI投资研究助手

AI 投资研究助手工作流：

> 起点 → 知识检索节点从数据库提取财报信息 & Exa 网页爬虫抓取实时数据（并行处理）→ 模板节点合并内容 → LLM 节点进行处理 → 最终输出

![img](https://framerusercontent.com/images/Mc8ixtdJwsml2si9BLEG2qY99M.png)

常见新手错误是：不清楚知识检索节点输出的格式，导致模板节点未正确引入数据库内容。模板节点虽然“看起来没报错”，但生成的输入不完整，LLM 得不到完整信息，最终输出效果平庸。

![img](https://framerusercontent.com/images/6UrcU2ZVqw0Ge1FHZlWY3qmXb8U.png)

### 3.1 旧方式的弊端

之前，如果输出结果不理想，整个排查过程非常痛苦：

**查找问题**：翻遍运行历史，点开每个节点看输出，最后才发现模板节点漏掉了知识库内容。

**修改测试**：返回编辑模式，修改模板代码，然后面临两个尴尬选择：

- 重跑整个流程，包括费时的知识检索和网页爬虫；
- 只调试 LLM 节点，但需要手动输入修复后的模板输出。

**重复循环**：如果结果仍不满意，又得重新开始上述流程。

这一过程不仅耗时，而且 API 成本极高，特别是在多轮调试。

### 3.2 新方式高效流程

现可这样操作：

1. **运行完整工作流**：点击运行一次，系统会自动保存每个节点的结果到变量面板，一目了然
2. **快速发现问题**：通过变量检查面板立刻发现，Exa 网页搜索正常，但模板节点的输出缺少知识库数据

![img](https://framerusercontent.com/images/pWTxE8llgkMj6WFZcHWQL6cVmwg.png)

3. **精准修复**：修改模板节点代码，确保整合知识库内容。

![img](https://framerusercontent.com/images/BCpSQSwqmLmw2wiObipwIjBwQs.png)

4. **分步骤测试**：

- 只运行模板节点：系统自动使用上游数据，更新输出结果。

![img](https://framerusercontent.com/images/I7581QPSyLEO2MT8QAyEQ0rlxOI.png)

![](https://framerusercontent.com/images/yetRwN0KgzijVcUHg2f9etU.png)

- 只运行 LLM 节点：自动使用修复后的模板输出作为输入，立即看到效果，无需重跑上游步骤。

![img](https://framerusercontent.com/images/yUAivLHH8X9bFJPSkGuKdRN7I.png)

5. **继续迭代**：如果 LLM 输出还不理想，可以调整提示词，只运行该节点，验证只需几秒。

------

### 3.3 前后对比

**旧方式：** 发现问题 → 翻查历史 → 手动输入变量 → 分步调试 → 重配节点 → 重跑流程 → 检查结果（循环多次）

**新方式：** 发现问题 → 查看变量检查面板 → 修改节点或直接编辑变量 → 单步运行 → 立即看结果

过去需要几十分钟的调试，现在只要几分钟，效率提升立竿见影。

## 4 总结

Dify 1.5.0 的核心升级为复杂 AI 开发带来了可视化和确定性。通过实时交互和透明的状态管理，开发者可以更快验证思路、精准定位问题、自信地构建高质量、可部署的 AI 应用。