负责AI大模型在业务场景中的核心应用开发，AIGC图片生成，商品推荐，智能导购

参与生成式大模型的能力构建，包括但不限于模型设计、prompt优化、预训练、模型加速、数据集能力建设等

深入理解业务需求，设计并优化AI大模型算法，提升模型性能和准确性，确保模型在实际业务场景中的高效运行

进行模型微调，以适应不同电商业务场景的需求，确保模型性能的最优化

了解并评估AI技术的最新发展，探索新技术在电商业务场景中的应用，解决落地过程中的技术难题。

要求：
计算机科学、软件工程或相关领域的本科及以上学历，具备扎实的计算机科学基础和编程能力，精通Python、TypeScript，熟悉Java语言。

至少1年AI大模型（如GPT/Gemini/豆包/文心一言等）应用开发经验，熟悉RAG、微调、知识库建设等相关技术，熟悉transformer、BERT、GPT等模型及其微调算法（如LoRA），以及pytorch/tensorflow深度学习框架。

掌握自然语言处理（NLP）相关技术，如文本预处理、词嵌入等，能够处理自然语言相关的技术难题。

对LangChain/Dify等框架有应用开发经验，能够利用这些框架提升AI大模型的开发效率和质量。

向量数据库Milvus/Chroma使用经验

## 职位

1. 负责风控领域大模型相关应用的模型选型、研发、调优与落地。
2. 结合具体风控业务场景，构建基于大模型的风险防控能力，优化反欺诈、反作弊等风控场景的防控能力。
3. 实现风控大模型的高效部署（量化、蒸馏、剪枝），满足风控场景业务性能和系统性能响应要求。
    职位要求
    一、算法基础 ：
    深入理解大模型技术栈（Transformer、LoRA、RLHF），至少主导过1个LLM落地项目。
    二、工程能力 ：
    熟练使用PyTorch、TensorFlow、LangChain等框架，掌握大模型微调工具（Hugging Face、vLLM）。
    具备分布式训练经验（DDP、DeepSpeed），熟悉模型服务化（Triton、TF Serving）。
    三、业务理解 ：
    熟悉电商风控反作弊、反欺诈等场景的业务逻辑与数据特点（如用户画像、交易图谱）。
    掌握风控指标设计，能平衡风险与用户体验。

## 大模型算法（偏aigc方向）、一大模型算法（偏语言）

职位描述：
负责开发和优化基于大模型的智能营销客服和室内效果图生成产品，参与从模型设计、训练、优化到部署的全流程工作，推动产品在性能和用户体验上的持续提升。

主要职责：
1. 智能营销客服系统开发：
  - 设计和实现基于大模型的智能客服对话系统，提升客服自动化和智能化水平。
  - 优化对话生成模型，提升对话的流畅性、准确性和用户满意度。
  - 优化问答库和知识库，提升召回率。
2. 室内效果图生成：
  - 开发基于生成模型（如GAN、Diffusion Models等）的室内设计效果图生成系统。
  - 优化图像生成模型，提升生成效果的真实感、细节表现和多样性。
  - 结合用户需求，实现个性化定制功能，如风格切换、家具替换等。
  - 研究并解决图像生成中的技术难点，如光照、材质、空间布局等。
3. 模型优化与部署：
  - 针对大模型进行性能优化，包括模型压缩、加速、分布式训练等。
  - 将模型部署到生产环境，确保系统的稳定性、可扩展性和高效性。
  - 持续监控模型表现，进行迭代优化。
4. 技术研究与创新：
  - 跟踪大模型领域的前沿技术，探索其在智能客服和图像生成中的应用。
  - 参与技术方案的制定和评审，推动技术创新和产品升级。

任职要求：
1. 教育背景：
  - 计算机科学、人工智能、机器学习、数学或相关领域的硕士及以上学历。
2. 技术能力：
  - 熟练掌握深度学习、自然语言处理（NLP）、计算机视觉（CV）等相关技术。
  - 熟悉主流的大模型框架（如GPT、BERT、Stable Diffusion等），并有实际项目经验。
  - 熟悉PyTorch、TensorFlow、langchain等框架，具备扎实的编程能力（Python为主）。
  - 有模型优化、分布式训练、模型部署经验者优先。
3. 项目经验：
  - 有智能客服、对话系统、图像生成、个性化推荐等相关项目经验者优先。
  - 熟悉多模态模型（如文本+图像）者优先。
4. 其他要求：
  - 具备良好的沟通能力和团队协作精神。
  - 对技术有热情，具备较强的学习能力和解决问题的能力。

加分项：
- 在顶级会议（如NeurIPS、ICML、CVPR、ACL等）发表过相关论文。
- 有产品化经验，能够从技术角度推动产品落地。