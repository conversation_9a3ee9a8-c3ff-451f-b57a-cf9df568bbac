# “上下文工程”的崛起

*头图来自 [Dex Horthy 在 Twitter 上的分享](https://x.com/dexhorthy/status/1933283008863482067?ref=blog.langchain.com)*

“上下文工程”（Context Engineering）是指构建动态系统，能够以合适的格式提供正确的信息和工具，从而让大语言模型（LLM）有可能完成任务。

大多数情况下，当一个智能代理表现不稳定或失败，其根本原因往往是模型没有接收到适当的上下文、指令或工具。

LLM 应用正从简单的单次提示（prompt）演变为更加复杂和动态的智能代理系统。因此，“上下文工程”正逐渐成为 [AI 工程师最重要的一项技能](https://cognition.ai/blog/dont-build-multi-agents?ref=blog.langchain.com#a-theory-of-building-long-running-agents)。

------

## 什么是“上下文工程”？

“上下文工程”是构建动态系统，以正确的格式提供正确的信息和工具，从而使 LLM 有可能完成任务。

这是我喜欢的一个定义，它融合了 [Tobi Lutke](https://x.com/tobi/status/1935533422589399127?ref=blog.langchain.com)、[Ankur Goyal](https://x.com/ankrgyl/status/1913766591910842619?ref=blog.langchain.com) 和 [Walden Yan](https://cognition.ai/blog/dont-build-multi-agents?ref=blog.langchain.com) 等人的观点。我们来分解一下它的内涵：

### 上下文工程是一种系统

复杂的智能代理往往需要从多个来源获取上下文。这些来源可能包括：开发者、用户、过往交互记录、工具调用结果，或者其他外部数据。将这些信息整合起来，本身就是一个复杂的系统工程。

### 这个系统是动态的

很多上下文信息是动态生成的。因此，最终构建提示的逻辑也必须是动态的，而不是一条静态提示就能解决的。

### 必须提供“正确的信息”

许多智能代理系统失败的原因，是因为没有提供模型完成任务所需的关键信息。LLM 并不能读心，如果不给它正确的输入，它就无法推断出你想要什么。输入的是垃圾，输出自然也是垃圾。

### 必须提供“正确的工具”

有时候，单靠输入信息还不足以让 LLM 完成任务。为了让它胜任工作，必须为其提供合适的工具，比如查找信息的工具、执行操作的接口等等。给模型配备工具，和给它提供信息一样重要。

### 格式也很关键

就像人与人沟通一样，与 LLM 沟通时，信息的表达方式同样重要。一个简洁明了的错误信息往往比一长串 JSON 更有帮助。工具的输入参数设计也同理——格式直接影响 LLM 是否能正确使用它们。

### 模型是否“有可能完成任务”？

这是设计上下文工程时需要反复自问的问题。它提醒我们：LLM 不是万能的，需要你帮它准备好条件，才能让它发挥作用。这也有助于识别失败原因——是上下文不全？还是信息齐全但模型出错了？不同的原因对应不同的解决方式。

------

## 为什么上下文工程很重要？

当智能代理系统出问题时，表面上是 LLM 出错，但从本质上讲，错误有两个可能的原因：

1. 模型本身能力不够，发生了错误；
2. 模型没有接收到足够或正确的上下文，无法输出合理结果。

随着模型能力的提升，越来越多的错误其实源自第二个原因：上下文不当。这种情况可能有以下几种表现：

- 缺失关键信息：模型不是通灵者，如果不给它信息，它就不知道；
- 格式不当：像人类一样，模型对表达方式很敏感，输入格式会极大影响其表现。

------

## 上下文工程和提示工程有何不同？

为何从“提示工程”转向“上下文工程”？早期，开发者更注重如何巧妙地写提示词来诱导模型给出更好答案。但随着应用越来越复杂，越来越明显的是，**为 AI 提供完整、结构化的上下文**远比措辞本身重要。

可以说，提示工程其实是上下文工程的一个子集。哪怕你有了所有需要的信息，如何把这些信息组织成最终的提示，依然很关键。只不过，现在面对的是动态数据组合，而非一次性静态提示。

此外，很多“上下文”其实包括模型行为的基本指令，比如让模型如何做出判断、如何调用工具等。这些指令既是提示工程的内容，也属于上下文工程的一部分。你可以把它看成两者的结合。

------

## 上下文工程的常见示例

一些基础的上下文工程实践包括：

- **工具使用**：确保当智能代理需要外部信息时，能调用合适的工具；返回的数据要对 LLM 易于理解。
- **短期记忆**：当对话时间较长时，将对话进行总结，以便后续使用。
- **长期记忆**：在用户过去的交互中表达过偏好，系统能在未来调用这些信息。
- **提示工程**：清晰地在提示中列出模型应遵守的行为规范。
- **信息检索**：根据当前任务动态获取相关信息并插入提示中。

------

## LangGraph 如何支持上下文工程

我们在构建 [LangGraph](https://github.com/langchain-ai/langgraph?ref=blog.langchain.com) 时，目标是打造最可控的智能代理框架，而这种高度可控性正是实现上下文工程的关键。

通过 LangGraph，你可以：

- 自定义每一个执行步骤；
- 精准控制传入 LLM 的内容；
- 决定输出存储的方式。

你对整个流程拥有完全掌控，这让你可以自由地实现各种上下文工程策略。相比之下，其他一些更注重抽象封装的代理框架，往往限制了你对上下文的控制，某些环节甚至无法修改传入模型的数据。

附带推荐一篇非常值得阅读的文章：[Dex Horthy 的《12 Factor Agents》](https://github.com/humanlayer/12-factor-agents?ref=blog.langchain.com)。其中很多观点与上下文工程息息相关，比如“掌握你的提示”、“掌握上下文构建权”等等。这篇博客的头图也取自 Dex 的分享，我们很欣赏他对这个领域的深刻洞察。

------

## LangSmith 如何帮助上下文工程

[LangSmith](https://smith.langchain.com/?ref=blog.langchain.com) 是我们构建的 LLM 应用可观测性和评估平台。虽然当初构建 LangSmith 时，“上下文工程”这一术语尚未流行，但它的功能正好能助力上下文工程的实践。

LangSmith 能做到：

- **追踪代理每一步调用**：你可以看到模型调用前，为收集数据所执行的各个步骤；
- **查看 LLM 的输入输出**：了解模型接收到的上下文信息及其格式，从而判断它是否具备完成任务所需的一切；
- **分析工具使用情况**：评估 LLM 是否获得了恰当的辅助工具，以帮助其完成任务。

------

## 关键在于沟通

几个月前，我写了一篇博客，叫《[沟通才是最关键的](https://blog.langchain.com/communication-is-all-you-need/)》。文章的核心观点是：与 LLM 的沟通比人们想象中困难，也比人们给予的关注更多，而这正是导致智能代理出错的根源之一。

实际上，许多沟通相关的问题本质上就是“上下文工程”的问题。

上下文工程并不是一个全新概念——过去一两年里，很多代理系统的构建者都在默默进行这项工作。如今只是有了一个更贴切的新名称，来描述这项日益重要的技能。我们今后会写更多关于这方面的内容，也相信我们所构建的工具（如 LangGraph、LangSmith）正是为上下文工程而生的，因此我们很期待这个方向被更多人重视并发展。