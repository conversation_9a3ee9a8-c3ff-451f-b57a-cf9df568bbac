### TL;DR

智能体需要上下文才能执行任务。而“上下文工程”就是在每一步的执行中，为智能体填充恰当的信息到其上下文窗口的技术与艺术。本文通过分析一些主流智能体产品和论文，总结出上下文工程的四种常见策略：**写入、选择、压缩、隔离**，并介绍了 LangGraph 如何支持这些策略。

**我们也制作了关于上下文工程的视频，点击观看 [\**这里\**](https://youtu.be/4GiqzUHD5AA?ref=blog.langchain.com)**

![img](https://blog.langchain.com/content/images/2025/07/image.png)
 常见的上下文工程类型

------

### 什么是上下文工程

正如 Andrej Karpathy 所说，大型语言模型（LLM）就像是一种全新的操作系统。模型本身类似 CPU，而其上下文窗口就像 RAM，是模型的“工作内存”。上下文窗口的容量有限，不能无限添加信息，就像操作系统会精心安排什么能进入 RAM，我们也需要对上下文窗口进行“工程”处理，即上下文工程。

Karpathy 如此描述：

> *“上下文工程就是在每一步中，巧妙地将恰当的信息放入上下文窗口的艺术和科学。”*

![img](https://blog.langchain.com/content/images/2025/07/image-1.png)
 常见的 LLM 应用中涉及的上下文类型

构建 LLM 应用时，我们需要处理不同类型的上下文：

- **指令类**：如提示词、范例、工具描述等
- **知识类**：包括事实、记忆等
- **工具类**：来自调用工具的反馈信息

------

### 智能体中的上下文工程

随着 LLM 在推理与工具调用方面能力提升，2025 年智能体系统受到了极大关注。智能体通过交替调用 LLM 和工具，来完成长时间运行的任务。

![img](https://blog.langchain.com/content/images/2025/07/image-2.png)
 智能体在多个步骤中交替调用 LLM 与工具，根据反馈决定下一步

然而，这种方式会导致大量 token 累积，引发一系列问题，比如上下文窗口溢出、成本/延迟上升、性能下降等。Drew Breunig 指出了上下文过长可能带来的问题：

- [上下文污染](https://www.dbreunig.com/2025/06/22/how-contexts-fail-and-how-to-fix-them.html#context-poisoning)：虚假内容被纳入上下文
- [上下文干扰](https://www.dbreunig.com/2025/06/22/how-contexts-fail-and-how-to-fix-them.html#context-distraction)：干扰模型输出
- [上下文混淆](https://www.dbreunig.com/2025/06/22/how-contexts-fail-and-how-to-fix-them.html#context-confusion)：无关信息影响结果
- [上下文冲突](https://www.dbreunig.com/2025/06/22/how-contexts-fail-and-how-to-fix-them.html#context-clash)：信息彼此矛盾

![img](https://blog.langchain.com/content/images/2025/07/image-3.png)
 工具调用反馈会在多轮对话中逐步累积

Cognition 强调：

> *“上下文工程基本上是 AI 智能体开发者的头号工作。”*

Anthropic 也指出：

> *“智能体的对话可能持续数百轮，迫切需要有效的上下文管理策略。”*

目前的策略主要分为四类：**写入、选择、压缩、隔离**。我们将在下文逐一说明，并展示 LangGraph 如何支持它们。

------

### 写入上下文

*将信息保存到上下文窗口之外，以支持智能体完成任务*

#### 备忘录（Scratchpads）

人类在处理复杂任务时会记笔记，智能体也可以通过“scratchpad”做类似的事。Anthropic 的研究系统就用它来保存计划：

> *主研究员在开局阶段将计划保存到内存中，以防上下文超出限制被截断。*

实现方式包括通过工具调用写入文件，或写入运行时状态对象。

#### 长期记忆（Memories）

scratchpad 主要用于当前 session，而“记忆”可在多个 session 间持续存在。Reflexion 提出了通过回顾每一步生成记忆，而 Generative Agents 会定期将反馈信息合成为记忆。

![img](https://blog.langchain.com/content/images/2025/07/image-5.png)
 LLM 可用于创建或更新记忆内容

这种机制已经被 ChatGPT、Cursor 和 Windsurf 等产品所采用，可自动生成并持久保存用户交互中的长期记忆。

------

### 选择上下文

*将有用的信息拉入上下文窗口以支持当前任务*

#### 来自 Scratchpad 的选择

如果 scratchpad 是工具实现的，智能体可通过工具调用读取；若作为运行时状态存储，开发者可以控制每一步暴露给 LLM 的部分。

#### 来自记忆的选择

有记忆，就需有选择机制。智能体可能会选取：

- 示例（情节记忆）
- 指令（程序性记忆）
- 事实（语义记忆）

![img](https://blog.langchain.com/content/images/2025/07/image-6.png)

一些产品如 Claude Code 使用特定文件（如 `CLAUDE.md`）保存指令或范例，Cursor 和 Windsurf 也用规则文件控制上下文。

但当记忆规模扩大，选择就变复杂了。例如 ChatGPT 会从大量用户记忆中挑选信息。常见方法是使用 Embedding 或图谱技术进行索引和检索。但选择仍有风险：Simon Willison 分享了 ChatGPT 在生成图像时无意中注入了他的位置，这种失控让用户感觉“上下文不再属于他们”。

#### 工具选择

工具太多可能导致混淆。一个解决方案是应用 RAG 技术从工具描述中检索最相关的工具，提高选择准确性。

#### 知识选择

RAG 是上下文工程中的关键技术。尤其在代码智能体中，如何从大量代码中检索相关上下文是一大挑战。例如 Windsurf 就结合了语义检索、AST 分析、关键词搜索和重排序等技术。

------

### 压缩上下文

*保留执行任务所需的最少 token*

#### 上下文总结

面对长对话和工具调用造成的 token 膨胀，总结是一种常见手段。例如 Claude Code 在接近上下文极限时会自动执行 summarization。

![img](https://blog.langchain.com/content/images/2025/07/image-7.png)
 总结可用于多个阶段

也可以在工具调用后加入总结处理，或在多个智能体间交接时使用总结降低 token 开销。Cognition 使用专门微调模型实现更高质量的总结。

#### 上下文修剪

相比总结，修剪更偏向于使用规则删除不必要的信息，如删除旧消息。Provence 是一个为问答任务训练的上下文修剪器。

------

### 隔离上下文

*将不同类型的信息分离处理，辅助任务完成*

#### 多智能体架构

通过多个子智能体分别处理任务的不同部分，是最常见的隔离方式。例如 OpenAI 的 Swarm，每个子智能体拥有自己的工具、指令与上下文窗口。

![img](https://blog.langchain.com/content/images/2025/07/image-8.png)
 使用多个智能体分担不同任务和上下文

Anthropic 的实验表明，隔离上下文的多个智能体比单一智能体效果更佳。但也带来新的挑战，比如 token 开销大、提示工程复杂、需要协调各个子智能体。

#### 使用环境隔离上下文

HuggingFace 的 CodeAgent 将工具调用在沙盒中执行，LLM 只接收执行结果，而非全部上下文。这样可以有效避免 token 超限问题。

![img](https://blog.langchain.com/content/images/2025/07/image-9.png)
 沙盒环境可将部分上下文隔离在 LLM 外部

#### 使用状态对象

智能体的运行状态对象可实现上下文的隔离与延迟使用。例如，将工具返回结果存入状态的某个字段，在合适时再传入 LLM。

------

### 使用 LangSmith / LangGraph 实现上下文工程

想要实践这些策略，你需要两样基础设施：

1. **数据观察能力**：通过 LangSmith 查看 token 使用情况，确定优化点
2. **性能测试能力**：同样使用 LangSmith 评估不同上下文策略对智能体效果的影响

#### 写入上下文

LangGraph 提供短期（线程级）与长期记忆机制，支持将信息写入状态对象（scratchpad）或持久存储（长期记忆）。

#### 选择上下文

每个节点都可以从状态中读取信息，并控制暴露给 LLM 的部分。长期记忆还支持语义检索和文件拉取等多种方式。

LangGraph BigTool 库可以通过语义搜索方式，帮助智能体从众多工具中选择最合适的。

#### 压缩上下文

LangGraph 允许你自定义节点结构与状态传递逻辑，非常适合添加压缩策略，比如定期总结对话、对工具结果进行后处理、控制信息长度等。

#### 隔离上下文

你可以通过状态结构将信息分字段管理，仅在需要时传入 LLM。LangGraph 也支持沙盒机制，例如与 E2B 结合使用。此外，它还支持构建多智能体架构，有相关 Supervisor 与 Swarm 库可用。

------

### 总结

上下文工程正逐渐成为智能体开发者的核心技能。本文介绍了以下几种常见实践：

- **写入上下文**：将信息存储在上下文窗口之外
- **选择上下文**：有选择地加入信息以辅助任务
- **压缩上下文**：保留必要 token，节省资源
- **隔离上下文**：分离任务与信息，降低干扰

LangGraph 提供了构建这些机制的灵活能力，LangSmith 则让你能测试和优化效果。两者结合可形成正向迭代，持续提升智能体性能。