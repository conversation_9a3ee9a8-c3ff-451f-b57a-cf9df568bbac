*发布日期：2025 年 7 月 9 日*

**更新 1.102.1**：此更新解决了这些[问题](https://github.com/microsoft/vscode/issues?q=is%3Aissue+is%3Aclosed+milestone%3A"June+2025+Recovery+1"+)。

------

欢迎来到 Visual Studio Code 2025 年 6 月发布版本。此版本包含许多我们希望您会喜欢的更新，其中的一些主要亮点包括：

- **聊天**
  - 探索并为开源的 GitHub Copilot Chat 扩展做出贡献 ([阅读我们的博客文章](https://code.visualstudio.com/blogs/2025/06/30/openSourceAIEditorFirstMilestone))。
  - 生成反映您项目规范的自定义指令 ([查看更多](https://code.visualstudio.com/raw/#generate-custom-instructions))。
  - 使用自定义模式为规划或研究等任务量身定制聊天 ([查看更多](https://code.visualstudio.com/raw/#chat-mode-improvements))。
  - 自动批准选定的终端命令 ([查看更多](https://code.visualstudio.com/raw/#terminal-auto-approval-experimental))。
  - 编辑并重新提交先前的聊天请求 ([查看更多](https://code.visualstudio.com/raw/#edit-previous-requests-experimental))。
- **MCP**
  - MCP 支持现已在 VS Code 中正式发布 ([查看更多](https://code.visualstudio.com/raw/#mcp-support-in-vs-code-is-generally-available))。
  - 使用 MCP 视图和库轻松安装和管理 MCP 服务器 ([查看更多](https://code.visualstudio.com/raw/#mcp-server-discovery-and-installation))。
  - MCP 服务器作为配置文件和设置同步中的一等资源 ([查看更多](https://code.visualstudio.com/raw/#mcp-servers-as-first-class-resources))。
- **编辑器体验**
  - 将任务委托给 Copilot 编码代理，让它在后台处理 ([查看更多](https://code.visualstudio.com/raw/#start-a-coding-agent-session-preview))。
  - 中键点击滚动编辑器 ([查看更多](https://code.visualstudio.com/raw/#scroll-on-middle-click))。

> 如果您想在线阅读这些发行说明，请访问 [code.visualstudio.com](https://code.visualstudio.com/) 上的[更新](https://code.visualstudio.com/updates)页面。**Insiders 用户：** 想尽快尝试新功能吗？您可以下载每日 [Insiders](https://code.visualstudio.com/insiders) 版本，以便在最新更新可用时立即体验。

## 聊天

### Copilot Chat 现已开源

我们很高兴地宣布，我们已经开源了 GitHub Copilot Chat 扩展！源代码现已在 [`microsoft/vscode-copilot-chat`](https://github.com/microsoft/vscode-copilot-chat) 上以 MIT 许可证提供。

这标志着我们在透明度和社区协作方面的一个重要里程碑。通过开源该扩展，我们使社区能够：

- **直接贡献**于 VS Code 中 AI 驱动的聊天体验的开发
- **理解**聊天模式、自定义指令和 AI 集成的实现方式
- **在我们的工作基础上**构建更优秀的 AI 开发者工具
- **参与塑造** AI 辅助编码的未来

您可以探索该仓库，了解[代理模式 (agent mode)](https://github.com/microsoft/vscode-copilot-chat/blob/e1222084830244174e6aa64683286561fa7e7607/src/extension/prompts/node/agent/agentPrompt.tsx)、[内联聊天 (inline chat)](https://github.com/microsoft/vscode-copilot-chat/blob/e1222084830244174e6aa64683286561fa7e7607/src/extension/prompts/node/inline/inlineChatEditCodePrompt.tsx) 和 [MCP 集成](https://github.com/microsoft/vscode-copilot-chat/blob/e1222084830244174e6aa64683286561fa7e7607/src/extension/mcp/vscode-node/mcpToolCallingLoop.tsx) 等功能的实现方式。我们欢迎社区的贡献、反馈和协作。

要了解更多关于这个里程碑以及我们对开源 AI 编辑器工具的更广阔愿景，请阅读我们的详细博客文章：[开源 AI 编辑器 - 第一个里程碑](https://code.visualstudio.com/blogs/2025/06/30/openSourceAIEditorFirstMilestone)。

### 聊天模式改进

上个里程碑中，我们预览了[自定义聊天模式](https://code.visualstudio.com/docs/copilot/chat/chat-modes#_custom-chat-modes)。除了内置的“询问 (Ask)”、“编辑 (Edit)”和“代理 (Agent)”聊天模式外，您还可以定义自己的聊天模式，其中包含特定的指令和一组允许的工具，您希望 LLM 在回复请求时遵循这些指令。

本里程碑中，我们在此领域进行了多项改进和错误修复。

#### 配置语言模型

应广大用户的要求，您现在还可以为聊天模式指定应使用的语言模型。将 `model` 元数据属性添加到您的 `chatmode.md` 文件中，并提供模型标识符（我们为模型信息提供了智能感知）。

![显示聊天模式文件中模型元数据属性智能感知的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/prompt-file-model-code-completion.png)

#### 改进的编辑支持

[聊天模式](https://code.visualstudio.com/docs/copilot/chat/chat-modes)、[提示](https://code.visualstudio.com/docs/copilot/copilot-customization#_prompt-files-experimental)和[指令文件](https://code.visualstudio.com/docs/copilot/copilot-customization#_custom-instructions)的编辑器现在支持所有受支持元数据属性的补全、验证和悬停提示。

![显示工具悬停信息的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/tools-hover.png)

![显示当特定聊天模式的模型不可用时的模型诊断信息的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/prompt-file-diagnostics.png)

#### 聊天视图中的齿轮菜单

聊天视图工具栏中的**配置聊天**操作可让您管理自定义模式以及可重用的指令、提示和工具集：

![显示聊天视图中“配置聊天”菜单的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/chat-gear.png)

选择**模式**会显示所有当前安装的自定义模式，并允许您打开、创建新模式或删除模式。

#### 通过 `vscode` 链接导入模式、提示和指令

您现在可以从外部链接（例如 gist 或我们的 [awesome-copilot](https://github.com/github/awesome-copilot) 社区仓库）导入聊天模式、可重用提示和指令文件。例如，以下链接将导入 Burke's GPT 4.1 Beast Mode 的聊天模式文件：

[将 GPT 4.1 Beast Mode 添加到 VS Code](vscode:chat-mode/install?url=https://raw.githubusercontent.com/github/awesome-copilot/refs/heads/main/chatmodes/4.1-Beast.chatmode.md)

这会提示您选择目标位置（当前工作区或用户设置），并在从 URL 导入模式文件前确认名称。

快去 [awesome-copilot](https://github.com/github/awesome-copilot) 尝试由社区贡献的 100 多个指令、提示和聊天模式吧。

### 生成自定义指令

为您的项目设置[自定义指令](https://code.visualstudio.com/docs/copilot/copilot-customization)可以通过提供有关您的编码标准和项目规范的上下文来显著改善 AI 建议。然而，从头开始创建有效的指令可能具有挑战性。

本里程碑中，我们引入了**聊天：生成指令 (Chat: Generate Instructions)** 命令，以帮助您为工作区快速生成自定义指令。从命令面板或聊天视图的配置菜单中运行此命令，代理模式将分析您的代码库，以生成反映您项目结构、技术和模式的量身定制的指令。

该命令会在您的 `.github` 文件夹中创建一个 `copilot-instructions.md` 文件，或对现有的指令文件提出改进建议。然后，您可以审查和自定义生成的指令，以满足您团队的特定需求。

了解更多关于[使用指令自定义 AI 响应](https://code.visualstudio.com/docs/copilot/copilot-customization)的信息。

### 按需加载指令文件

指令文件可用于描述编码实践和项目要求。指令可以手动或自动作为上下文包含在聊天请求中。

支持多种机制，请参阅我们文档中的[自定义指令](https://code.visualstudio.com/docs/copilot/copilot-customization#_custom-instructions)部分。

对于您希望有条件地包含的较大指令，您可以使用 `.instructions.md` 文件，并结合在 `applyTo` 头部中定义的 glob 模式。当 glob 模式匹配聊天上下文中一个或多个文件时，该文件会自动添加。

此版本的新功能是，大型语言模型可以按需加载指令。每个请求都会获得所有指令文件的列表，以及 glob 模式和描述。在此示例中，LLM 没有为 TypeScript 文件显式添加上下文中的指令。因此，它在创建 TypeScript 文件之前会查找代码风格规则：

![显示按需加载指令文件的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/instructions-loading-on-demand.png)

### 编辑先前的请求（实验性）

您现在可以点击先前的请求来修改文本内容、附加的上下文、模式和模型。提交此更改后，将删除所有后续请求，撤销所做的任何编辑，并在聊天中发送新请求。

我们将分阶段推出不同的编辑请求入口，这将有助于我们收集关于首选编辑和撤销流程的反馈。但是，用户可以使用实验性设置 `chat.editRequests` 来设置他们偏好的模式：

- `chat.editRequests.inline`: 悬停在请求上并选择文本，以在请求内联开始编辑。
- `chat.editRequests.hover`: 悬停在请求上以显示一个工具栏，其中包含一个按钮，用于在请求内联开始编辑。
- `chat.editRequests.input`: 悬停在请求上以显示一个工具栏，该工具栏将在聊天底部的输入框中开始编辑。

### 终端自动批准（实验性）

代理模式现在有了一种在终端中自动批准命令的机制。以下是使用默认设置的演示：



目前有两个设置：允许列表和拒绝列表。允许列表是一个命令*前缀*或正则表达式列表，当匹配时，允许命令在未经明确批准的情况下运行。例如，以下设置将允许任何以 `npm run test` 开头的命令运行，以及*精确匹配* `git status` 或 `git log` 的命令：

```json
"github.copilot.chat.agent.terminal.allowList": {
  "npm run test": true,
  "/^git (status|log)$/": true
}
```

这些设置在不同设置范围之间会合并，因此您可以有一组用户批准的命令，以及特定于工作区的批准命令。

对于链式命令，我们会尝试根据 shell 检测这些情况，并要求所有子命令都得到批准。因此，对于 `foo && bar`，我们会检查 `foo` 和 `bar` 是否都允许，只有在那时它才会在未经批准的情况下运行。我们还尝试检测内联命令，例如 `echo $(pwd)`，这将同时检查 `echo $(pwd)` 和 `pwd`。

拒绝列表的格式与允许列表相同，但会覆盖它并强制要求批准。目前，这主要用于当您在允许列表中有一个广泛的条目，并希望阻止它可能包含的某些命令时。例如，以下设置将允许所有以 `npm run` 开头的命令，除非它以 `npm run danger` 开头：

```json
"github.copilot.chat.agent.terminal.allowList": {
  "npm run": true
},
"github.copilot.chat.agent.terminal.denyList": {
  "npm run danger": true
}
```

得益于[工作区信任](https://code.visualstudio.com/docs/editing/workspaces/workspace-trust)为我们提供的针对提示注入的保护，我们在实现此功能时所采取的安全理念是，在允许列表中包含一小部分无害的命令，并在拒绝列表中包含一组特别危险的命令，以防它们设法通过。允许列表默认为空，因为我们仍在考虑默认值应该是什么，但以下是我们的想法：

- 允许列表：`echo`, `cd`, `ls`, `cat`, `pwd`, `Write-Host`, `Set-Location`, `Get-ChildItem`, `Get-Content`, `Get-Location`
- 拒绝列表：`rm`, `rmdir`, `del`, `kill`, `curl`, `wget`, `eval`, `chmod`, `chown`, `Remove-Item`

我们希望为此功能添加的两个主要部分是：一个用于更轻松地将新命令添加到列表的 UI 入口点 ([#253268](https://github.com/microsoft/vscode/issues/253268)) 和一个允许 LLM 评估命令安全性的可选选项 ([#253267](https://github.com/microsoft/vscode/issues/253267))。我们还计划在下个版本中，在它成为预览设置之前，移除这些设置的 `github.copilot.` 前缀 ([#253314](https://github.com/microsoft/vscode/issues/253314)) 并将它们合并在一起 ([#253472](https://github.com/microsoft/vscode/issues/253472))。

### 终端命令简化

代理模式有时希望运行带有 `cd` 语句的命令，以防万一。我们现在检测到当它与当前工作目录匹配时的情况，并简化运行的命令。

![终端屏幕截图，要求运行 `cd C:\Github\Tyriar\xterm.js && echo hello`，当当前工作目录已经匹配时，只运行 `echo hello`。](https://code.visualstudio.com/raw/images/1_102/terminal-working-dir.png)

### 代理对任务和终端的感知

代理模式现在能理解它创建了哪些后台终端以及哪些任务正在活动。代理可以使用新的 `GetTaskOutput` 工具读取任务输出，这有助于防止运行重复的任务并改善工作区上下文。

![VS Code 窗口的屏幕截图，显示终端面板中有两个构建任务正在运行。左侧终端显示了几个错误。聊天代理回复描述了我的构建任务状态，并总结了每个任务的输出。](https://code.visualstudio.com/raw/images/1_102/task-status.png)

### 最大化聊天视图

您现在可以最大化辅助侧边栏，使其跨越编辑器区域，并隐藏主侧边栏和面板区域。VS Code 会在重启之间记住此状态，并在您打开编辑器或视图时恢复聊天视图。



您可以使用关闭按钮旁边的新图标在最大化状态之间切换，或使用命令面板中的新命令 `workbench.action.toggleMaximizedAuxiliaryBar`。

### 代理模式徽章指示器

当窗口未获得焦点且代理需要用户确认才能继续时，我们现在会在 Dock 中的应用程序图标上显示一个徽章。一旦触发它的相关窗口获得焦点，该徽章就会消失。

![VS Code Dock 图标的屏幕截图，显示代理确认作为徽章。](https://code.visualstudio.com/raw/images/1_102/badge.png)

您可以通过 `chat.notifyWindowOnConfirmation` 设置启用或禁用此徽章。

### 从命令行启动聊天

VS Code CLI 新增了一个子命令 `chat`，允许您在当前工作目录中使用提供的提示启动聊天会话。



基本语法是 `code chat [options] [prompt]`，选项可以是以下任何一种：

- `-m --mode <mode>`：用于聊天会话的模式。可用选项：“ask”、“edit”、“agent”或自定义模式的标识符。默认为“agent”。
- `-a --add-file <path>`：将文件作为上下文添加到聊天会话。
- `--maximize`：最大化聊天会话视图。
- `-r --reuse-window`：强制使用最后一个活动窗口进行聊天会话。
- `-n --new-window`：强制打开一个空窗口进行聊天会话。

支持从标准输入读取，前提是您在末尾传入 `-`，例如 `ps aux | grep code | code chat <prompt> -`

### Fetch 工具支持非 HTTP URL

我们注意到，模型偶尔会使用非 HTTP URL（如 `file://` URL）调用 Fetch 工具。Fetch 工具现在支持这些 URL，而不是禁止它们，并返回 URL 处文件或资源的内容。也支持图片。

### 更清晰的语言模型访问管理

我们重新设计了管理扩展访问由其他扩展提供的语言模型的用户体验。以前，您会在“帐户”菜单中看到一个名为 **AccountName (GitHub Copilot Chat)** 的项目，这与 GitHub Copilot Chat 使用的帐户无关。相反，它允许您管理哪些扩展可以访问 Copilot Chat 提供的语言模型。

为了使其更清晰，我们移除了 **AccountName (GitHub Copilot Chat)** 项目，并用一个名为**管理语言模型访问...** 的新项目取而代之。此项目会打开一个快速选择框，让您管理哪些扩展可以访问 GitHub Copilot Chat 提供的语言模型。

![显示语言模型访问快速选择框的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/lm-access-qp.png)

我们认为这样更清晰……话虽如此，在未来的版本中，我们将探索对语言模型进行更精细的访问控制（例如，只允许特定模型而不是扩展提供的*所有*模型），敬请期待。

### 读取聊天请求

由于聊天扩展本身是开源的，您现在可以访问我们内部使用了一段时间的调试工具之一。要轻松查看 Copilot Chat 发出的所有请求的详细信息，请运行“显示聊天调试视图”命令。这将显示一个树状视图，其中每个请求都有一个条目。您可以看到发送给模型的完整提示、启用的工具、响应以及其他关键细节。您可以通过右键单击 > “导出为...” 保存请求日志。

该视图还有单独的工具调用条目，以及一个在简单浏览器中打开的 prompt-tsx 调试视图。

> 🚨 **注意**：此日志对于排查问题非常有帮助，如果您在提交有关代理行为的问题时分享它，我们将不胜感激。但是，此日志可能包含个人信息，例如您的文件内容或终端输出。在与任何人分享之前，请仔细审查其内容。

### 编辑工具改进

此版本对用于 GPT-4 模型和 Sonnet 模型的编辑工具的预测性和可靠性进行了多项更改。您应该会在这个版本中看到更可靠的编辑行为，我们将在未来的版本中继续改进这些工具。

## MCP

### VS Code 中的 MCP 支持已正式发布

过去几个月，我们一直致力于扩展 VS Code 中的 MCP 支持，并[支持规范中完整的 MCP 功能范围](https://code.visualstudio.com/blogs/2025/06/12/full-mcp-spec-support)。自此版本起，MCP 支持现已在 VS Code 中正式发布！

此外，组织现在可以通过 GitHub Copilot 策略控制 MCP 服务器的可用性。在 GitHub Copilot 文档中了解更多关于[在您的企业中管理 Copilot 的策略和功能](https://docs.github.com/en/enterprise-cloud@latest/copilot/how-tos/administer/enterprises/managing-policies-and-features-for-copilot-in-your-enterprise)的信息。

您可以从我们精选的[热门 MCP 服务器列表](https://code.visualstudio.com/mcp)中安装一些服务器来开始使用。了解更多关于[在 VS Code 中使用 MCP 服务器](https://code.visualstudio.com/docs/copilot/chat/mcp-servers)以及如何使用它们来扩展代理模式的信息。

![显示 MCP 服务器页面的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/mcp-servers-page.png)

如果您想构建自己的 MCP 服务器，请查看我们的 [MCP 开发者指南](https://code.visualstudio.com/api/extension-guides/ai/mcp)以获取有关如何在 VS Code 中利用 MCP 功能的更多详细信息。

### 支持 Elicitations

最新的 MCP 规范增加了对 [Elicitations](https://modelcontextprotocol.io/specification/2025-06-18/client/elicitation) 的支持，作为 MCP 服务器向 MCP 客户端请求输入的一种方式。最新版本的 VS Code 采用了此规范并包含了对 elicitations 的支持。



### MCP 服务器发现与安装

扩展视图中的新 **MCP 服务器** 部分包含欢迎内容，直接链接到我们精选的[热门 MCP 服务器列表](https://code.visualstudio.com/mcp)。访问该网站探索可用的 MCP 服务器，并在任何 MCP 服务器上选择**安装**。这会自动启动 VS Code 并打开 MCP 服务器编辑器，其中显示服务器的自述文件和清单信息。您可以审查服务器详细信息并选择**安装**以将服务器添加到您的 VS Code 实例中。

一旦安装，MCP 服务器会自动出现在您的扩展视图中的 **MCP 服务器 - 已安装** 部分，并且它们的工具将在聊天视图的工具快速选择框中可用。这使得验证您的 MCP 服务器是否正常工作并立即访问其功能变得容易。



### MCP 服务器管理视图

扩展视图中的新 **MCP 服务器 - 已安装** 视图使您可以轻松监控、配置和控制已安装的 MCP 服务器。

![显示已安装服务器的 MCP 服务器管理视图的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/mcp-servers-installed-view.png)

该视图列出了已安装的 MCP 服务器，并通过上下文菜单提供了多个管理操作：

![显示 MCP 服务器上下文菜单操作的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/mcp-server-context-menu.png)

- **启动服务器** / **停止服务器** / **重启服务器**：控制服务器的运行状态
- **断开帐户连接**：从服务器移除帐户访问权限
- **显示输出**：查看服务器的输出日志以进行故障排除
- **显示配置**：打开服务器的运行时配置
- **配置模型访问**：管理服务器可以访问哪些语言模型
- **显示采样请求**：查看采样请求以进行调试
- **浏览资源**：探索服务器提供的资源
- **卸载**：从您的 VS Code 实例中移除服务器

当您选择一个已安装的 MCP 服务器时，VS Code 会打开 MCP 服务器编辑器，显示服务器的自述文件详细信息、清单及其运行时配置。这提供了服务器功能和当前设置的概览，使其易于理解服务器的功能及其配置方式。

![显示带有运行时配置的 MCP 服务器编辑器的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/mcp-server-editor-configuration.png)

**MCP 服务器 - 已安装** 视图还提供了一个**浏览 MCP 服务器...** 操作，可直接带您到社区网站，使服务器发现始终可以在 VS Code 内部访问。

![显示 MCP 服务器视图中“浏览 MCP 服务器”操作的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/mcp-servers-browse-action.png)

### MCP 服务器作为一等资源

MCP 服务器现在在 VS Code 中被视为一等资源，类似于用户任务和其他特定于配置文件的配置。这代表了与之前将 MCP 服务器存储在用户设置中的方法相比的重大架构改进。这一变化使 MCP 服务器管理更加健壮，并为您的通用 VS Code 设置和 MCP 服务器配置提供了更好的关注点分离。当您安装或配置 MCP 服务器时，它们会自动存储在适当的特定于[配置文件](https://code.visualstudio.com/docs/configure/profiles)的位置，以确保您的主设置文件保持整洁和专注。

- **专用存储**：MCP 服务器现在存储在每个配置文件内的专用 `mcp.json` 文件中，而不是弄乱您的用户设置文件
- **特定于配置文件**：每个 VS Code 配置文件都维护自己的一组 MCP 服务器，使您能够为不同的工作流或项目拥有不同的服务器配置
- **设置同步集成**：MCP 服务器通过[设置同步](https://code.visualstudio.com/docs/configure/settings-sync)在您的设备之间无缝同步，并可以精细控制同步的内容

#### MCP 迁移支持

随着 MCP 服务器成为一等资源及其配置的相关更改，VS Code 为从先前 MCP 服务器配置格式升级的用户提供了全面的迁移支持：

- **自动检测**：自动检测 `settings.json` 中现有的 MCP 服务器，并将其迁移到新的特定于配置文件的 `mcp.json` 格式
- **实时迁移**：当您将 MCP 服务器添加到用户设置时，VS Code 会立即迁移它们，并提供一个有用的通知来解释这一变化
- **跨平台支持**：迁移在所有开发场景中无缝工作，包括本地、远程、WSL 和 Codespaces 环境

此迁移确保您现有的 MCP 服务器配置可以继续工作，无需任何手动干预，同时提供了新架构的增强管理功能。

#### 开发容器对 MCP 配置的支持

开发容器配置 `devcontainer.json` 和开发容器功能配置 `devcontainer-feature.json` 在路径 `customizations.vscode.mcp` 支持 MCP 服务器配置。当创建开发容器时，收集到的 MCP 服务器配置会写入到远程 MCP 配置文件 `mcp.json` 中。

配置 Playwright MCP 服务器的示例 `devcontainer.json`：

```json
{
    "image": "mcr.microsoft.com/devcontainers/typescript-node:latest",

    "customizations": {
        "vscode": {
            "mcp": {
                "servers": {
                    "playwright": {
                        "command": "npx",
                        "args": [
                            "@playwright/mcp@latest"
                        ]
                    }
                }
            }
        }
    }
}
```

#### 访问 MCP 资源的命令

为了使使用 MCP 服务器更加方便，我们添加了命令来帮助您管理和访问您的 MCP 配置文件：

- **MCP: Open User Configuration** - 直接访问您的用户级 `mcp.json` 文件
- **MCP: Open Remote User Configuration** - 直接访问您的远程用户级 `mcp.json` 文件

这些命令提供了对您的 MCP 配置文件的快速访问，使您可以轻松地直接查看和管理您的服务器配置。

### 快速管理 MCP 身份验证

您现在可以从 MCP 齿轮菜单和快速选择框中登出或断开帐户连接。

- MCP 视图齿轮菜单：![显示在 MCP 视图齿轮菜单中的“断开帐户连接”操作的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/mcp-view-signout.png)
- MCP 编辑器齿轮菜单：![显示在 MCP 编辑器齿轮菜单中的“断开帐户连接”操作的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/mcp-editor-signout.png)
- MCP 快速选择框：![显示在 MCP 快速选择菜单中的“断开帐户连接”操作的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/mcp-qp-signout.png)

当帐户被其他 MCP 服务器或扩展使用时，会显示**断开连接**操作；而当帐户仅被该 MCP 服务器使用时，会显示**登出**操作。登出操作会从 VS Code 中完全移除该帐户，而断开连接仅从该 MCP 服务器移除对该帐户的访问权限。

## 辅助功能

### 从编辑器内部保留所有编辑

以前，要接受所有编辑，焦点必须在聊天视图中。现在，当焦点在编辑器中时，您可以运行命令**保留所有编辑 (Keep All Edits)** (`⌥⌘Y`)。

### 需要用户操作的声音提示

我们微调了辅助功能信号，以指示聊天何时需要用户操作，并将默认值设置为 `auto`，因此屏幕阅读器用户将听到此信号。您可以使用 `accessibility.signals.chatUserActionRequired` 设置来配置此行为。

### 聊天中发生渲染错误时发出警报

以前，当聊天发生渲染错误时，屏幕阅读器用户不会收到警报。现在，用户会收到此信息的警报，并且还可以通过键盘将焦点移至其上。

## 代码编辑

### 中键点击滚动

**设置**：`editor.scrollOnMiddleClick`

只需单击或按住鼠标中键（滚轮）并移动，即可滚动编辑器。

激活后，光标会变为平移图标，向上或向下移动鼠标即可在该方向上平滑滚动编辑器。滚动速度由您将鼠标移离初始点击点的距离决定。释放中键或再次单击它即可停止滚动并返回标准光标。



**已知冲突**

启用此功能可能会干扰与中键相关的其他操作。例如，如果您启用了列选择 (`editor.columnSelection`)，按住中键会选择文本。同样，在 Linux 上，选择剪贴板 (`editor.selectionClipboard`) 会在单击中键时粘贴剪贴板中的内容。

为避免这些冲突，请一次只启用其中一个设置。

### 暂停代码补全

您现在可以使用新的**暂停**功能暂时停止内联建议和下一次编辑建议 (NES)。当您希望专注而不受建议干扰时，这非常有用。

要暂停建议，请选择状态栏中的 Copilot 仪表盘，或从命令面板运行**暂停内联建议 (Snooze Inline Suggestions)** 命令，然后从下拉菜单中选择一个持续时间。在暂停期间，不会出现任何内联建议或 NES。

![显示 Copilot 仪表盘的屏幕截图，底部有暂停按钮。](https://code.visualstudio.com/raw/images/1_102/nes-snooze.png)

您还可以分配一个自定义快捷键，通过将所需持续时间作为参数传递给命令来快速暂停建议。例如：

```json
{
  "key": "...",
  "command": "editor.action.inlineSuggest.snooze",
  "args": 10
}
```

## 编辑器体验

### Windows 主题色

**设置**：`window.border`

如果 Windows 设置中启用了“在标题栏和窗口边框上显示主题色”，Windows 上的 VS Code 现在支持使用该主题色作为窗口框架边框。

![显示带有红色主题色边框的 VS Code 窗口的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/window-accent.png)

新的 `window.border` 设置允许您控制窗口边框的颜色。使用 `default` 来使用 Windows 主题色，`off` 来禁用边框，或提供一个特定的颜色值来使用自定义颜色。

**注意**：该边框仅在相关的 Windows 设置启用时可见。目前还不能按工作区设置，但我们正在努力支持该功能。

### 设置搜索建议（预览版）

**设置**：`workbench.settings.showAISearchToggle`

本里程碑中，我们修改了“设置”编辑器中的闪烁切换按钮，使其充当 AI 和非 AI 搜索结果之间的切换。AI 设置搜索结果是语义上相似的结果，而不是基于字符串匹配的结果。例如，当您搜索“增加文本大小”时，`editor.fontSize` 会作为 AI 设置搜索结果出现。

该切换按钮仅在有 AI 结果可用时启用。我们欢迎关于 AI 设置搜索未找到预期设置时的反馈，并计划在下一次迭代中默认启用此设置。



## 任务

### 重新运行所有正在运行的任务

您现在可以使用新的 `任务：重新运行所有正在运行的任务` 命令快速重新运行所有当前正在运行的任务。这对于涉及多个并发任务的工作流非常有用，允许您一次性重启所有任务，而无需单独停止和重新运行每个任务。

### 重启任务会重新加载更新后的 tasks.json

**重启任务**命令现在会在重启前重新加载您的 `tasks.json`，确保任何最近的更改都得到应用。以前，重启任务时不会获取任务配置的更改，这可能导致混淆或过时的任务行为。

## 终端

### 终端建议（预览版）

我们对终端建议功能进行了重大改进。

#### 选择模式

新的设置 `terminal.integrated.suggest.selectionMode` 可帮助您了解默认情况下，`Tab`（而不是 `Enter`）接受建议。您可以在 `partial`、`always` 和 `never` 模式之间进行选择，以控制建议的选择和接受方式。

默认值为 `partial`，这意味着在发生导航之前，`Tab` 会接受建议。

![显示第一个终端建议可以用 Tab 接受的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/terminal-selection-mode.png)

#### 了解更多

终端建议控件状态栏中的**了解更多**操作 `(⇧⌘L)` 现在会在前 10 次或当控件显示 10 秒时高亮显示。这有助于您发现如何配置、禁用和了解建议控件。

![显示“了解更多”操作出现在终端建议控件状态栏中的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/terminal-suggest-discoverability.png)

#### 多命令支持

终端建议现在支持多命令。您可以使用 `;`、`&&` 和其他 shell 操作符链接命令，并获得行上所有命令的建议。

![显示 VS Code 终端的屏幕截图，其中包含 git commit 和 git push 的多命令行，以及终端建议小部件显示 pull、push 和其他 git 命令的建议。](https://code.visualstudio.com/raw/images/1_102/terminal-suggest-multi.png)

#### 符号链接信息

我们现在在建议详细信息控件中显示符号链接的真实路径，并为符号链接文件和文件夹提供独特的图标，以帮助将它们与其他建议区分开。

![显示终端建议显示符号链接的路径 -> 真实路径的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/terminal-symlink.png)

#### 改进的排序

我们在许多方面改进了排序，以便首先为您提供最相关的建议。例如，将 `main` 和 `master` 的优先级置于其他分支之上。

![显示终端建议将 main 和 master 的优先级提升到其他分支建议之上的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/terminal-suggest-sorting.png)

#### Git Bash 改进

我们现在正确支持 Git Bash 对文件夹和文件的路径补全。此外，我们还获取内置命令并将其作为建议呈现。

![显示 Git Bash 终端的屏幕截图，其中包含 cat、cp 和 curl 等内置函数的建议。](https://code.visualstudio.com/raw/images/1_102/terminal-git-bash.png)

## 对扩展的贡献

### GitHub Pull Requests

[GitHub Pull Requests](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) 扩展取得了更多进展，该扩展使您能够处理、创建和管理拉取请求和问题。

[GitHub Pull Requests](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) 扩展和 [Copilot 编码代理](https://docs.github.com/en/copilot/how-tos/agents/copilot-coding-agent)之间进行了更深入的集成，允许您直接从 VS Code 开始、查看和管理编码代理会话。

这些功能要求您的工作区打开到一个已[启用 Copilot 编码代理](https://docs.github.com/en/copilot/how-tos/agents/copilot-coding-agent/enabling-copilot-coding-agent)的仓库。

查看该扩展的 [0.114.0 版本更新日志](https://github.com/microsoft/vscode-pull-request-github/blob/main/CHANGELOG.md#01140)以了解该版本的所有内容。

#### 启动编码代理会话（预览版）

通过在聊天中调用 `#copilotCodingAgent` 工具，要求 Copilot 在后台继续本地更改。

此工具会自动将待处理的更改推送到远程分支，并从该分支以及用户的指令启动一个编码代理会话。

![显示将一个会话移交给 Copilot 编码代理的屏幕截图](https://code.visualstudio.com/raw/images/1_102/coding-agent-start.png)

**实验性功能：** 可以使用 `githubPullRequests.codingAgent.uiIntegration` 设置启用更深入的 UI 集成。启用后，对于启用了代理的仓库，聊天视图中会出现一个新的**委托给编码代理**按钮。

#### 状态跟踪

我们改进了通知功能，并在**Copilot on my behalf**查询中显著显示编码代理拉取请求的状态。一个数字徽章现在表示有新的更改。

![显示多个编码代理拉取请求状态的屏幕截图](https://code.visualstudio.com/raw/images/1_102/coding-agent-status.png)

#### 会话日志

您现在可以直接在 VS Code 中查看编码代理会话的日志。这使您可以看到编码代理采取的操作历史，包括代码更改和工具使用情况。

![显示编码代理会话日志的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/coding-agent-session-log.png)

#### 对 `#activePullRequest` 工具的增强

`#activePullRequest` 工具返回有关拉取请求的信息，例如其标题、描述和状态，以供在聊天中使用，现在您还可以使用它来获取编码代理会话信息。

当打开通过编码代理体验创建的拉取请求时，此工具会自动附加到聊天中，这样您就可以保持上下文并在需要时继续处理该拉取请求。

### Python

#### Python Environments 扩展改进

[Python Environments 扩展](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-python-envs)在此版本中获得了多项改进：

- 我们优化了对 Poetry 2.0.0 以上版本的终端激活支持
- 您现在可以使用快速创建环境选项来设置多个虚拟环境，这些环境在同一工作区内具有唯一的名称
- 生成的 `.venv` 文件夹现在默认被 git-ignored
- 我们改进了环境删除过程

#### Python Environments 作为 Python 扩展的一部分包含

我们正在开始将 [Python Environments 扩展](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-python-envs)作为 Python 扩展的可选依赖项进行推广。这意味着您现在可能会开始看到 Python Environments 扩展与 Python 扩展一起自动安装，类似于 Python Debugger 和 Pylance 扩展。这种受控的推广使我们能够在正式发布前收集早期反馈并确保可靠性。

Python Environments 扩展包含了我们迄今为止引入的所有核心功能，包括：[使用快速创建一键设置环境](https://devblogs.microsoft.com/python/python-in-visual-studio-code-may-2025-release/#python-environments-quick-create-command)，自动终端激活（通过 "python-envs.terminal.autoActivationType" 设置），以及所有支持的[环境和包管理 UI](https://devblogs.microsoft.com/python/python-in-visual-studio-code-december-2024-release/)。

要在推广期间使用 Python Environments 扩展，请确保该扩展已安装，并将以下内容添加到您的 VS Code settings.json 文件中：

```json
"python.useEnvironmentsExtension": true
```

#### 禁用 PyREPL for Python 3.13

我们已为 Python 3.13 及以上版本禁用了 PyREPL，以解决交互式终端中的缩进和光标问题。有关更多详细信息，请参阅[为 >= 3.13 禁用 PyREPL](https://github.com/microsoft/vscode-python/issues/25164)。

#### Pylance MCP 工具（实验性）

[Pylance 扩展](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance)现在包含几个实验性的 MCP 工具，可用于访问 Pylance 的文档、导入分析、环境管理等。这些工具目前在 Pylance 预发布版本中提供，仍处于早期开发阶段。虽然它们提供了新功能，但我们知道目前直接调用它们可能具有挑战性。我们正在积极努力使这些工具在未来的更新中更易于使用和更有价值。在我们继续改进体验的过程中，欢迎您在 [pylance-release](https://github.com/microsoft/pylance-release/) 仓库中提供反馈。

### GitHub 身份验证

#### 全新的 GitHub 登录流程

本次迭代中，我们通过默认使用环回 URL 流程（而不是使用 `vscode://` 协议 URL 的流程）对 GitHub 登录流程进行了改造。此更改旨在提高登录流程的可靠性，并确保它在所有平台上都能正常工作，包括那些不支持自定义 URL 方案的平台。

当您使用 GitHub 登录时，您现在会被重定向到一个看起来像 `http://localhost:PORT/` 的环回 URL。这使得登录流程能够成功完成，而无需依赖自定义 URL 方案。话虽如此，一旦您到达环回 URL，您仍然会被重定向到 `vscode://` URL 以返回 VS Code，但这不需要解析成功即可完成登录流程。

换句话说，我们兼顾了两者的优点：一个在所有平台上都可靠的登录流程，以及一个使用 `vscode://` URL 方案返回 VS Code 的流程。

趁此机会，我们还为这个[登录页面换上了新装](https://code.visualstudio.com/raw/#revamped-github-sign-in-flow)。在未来的迭代中，我们会将这种新设计应用于其他登录体验。

![显示重新设计的身份验证登录页面的屏幕截图。](https://code.visualstudio.com/raw/images/1_102/auth-landing.png)

## 扩展创作

### 在使用 `vscode.openFolder` 命令时允许打开文件

调用 `vscode.openFolder` 命令的扩展现在可以传递 `filesToOpen?: UriComponents[]` 作为选项，以选择在打开的工作区窗口中要打开的文件。

示例：

```ts
vscode.commands.executeCommand('vscode.openFolder', <folder uri>, { filesToOpen: [ /* 要打开的文件 */]});
```

## 提案 API

## 工程

### 使用 `esbuild` 进行 CSS 压缩

VS Code 长期以来一直使用 `esbuild` 来打包和压缩 JavaScript 源代码。我们现在也使用 `esbuild` 来打包和压缩我们的 CSS 文件。

### 使用 `tsconfig.json` 进行严格的层检查

我们现在使用多个 `tsconfig.json` 文件来确保我们的源代码遵守我们的[目标环境规则](https://github.com/microsoft/vscode/wiki/Source-Code-Organization#target-environments)。我们的 CI 会运行 `npm run valid-layers-check`，如果例如一个仅存在于 `node` 运行时的类型被添加到了 `browser` 层中，构建将会失败。

### 用于健全性测试的 `vscode-bisect`

[`vscode-bisect`](https://github.com/Microsoft/vscode-bisect) 项目已经存在了很长时间，它允许在 VS Code 的构建中发现回归（就像 `git bisect` 对 `git` 所做的那样）。我们增加了一个新的 `--sanity` 选项，使我们能够快速完成我们在发布新构建之前必须进行的[健全性检查](https://github.com/microsoft/vscode/wiki/Sanity-Check)。
