> 原文：https://codeium.com/changelog

## 1.11.1-Kimi K2 Available

July 23, 2025

![](https://exafunction.github.io/public/changelog/windsurf/1.11.1/hero_img.jpg)

Windsurf now supports Kimi K2 model which costs 0.5 credits per prompt.

## 1.11.0 - 与 Cascade 语音对话

2025 年 7 月 17 日

![1.11.0 更新横幅](https://exafunction.github.io/public/changelog/windsurf/1.11.0/hero_img.jpg)

### 🎙️ 语音功能

现可通过语音与聊天对话，无需再手动输入。

### 💬 会话 @ 提及功能

可 @ 提及某个起始对话，让 Cascade 在为你生成测试时拥有完整上下文。

### 🌐 更深入的浏览器集成

可通过 @ 提及与 Cascade 讨论浏览器中已打开的标签页。

### 💻 JetBrains 支持增强

Cascade 在 JetBrains 中现已支持“规划模式”、“工作流”以及基于文件的规则。

### 🔧 其他改进

- 可 @ 提及终端，与 Cascade 协同操作。
- 增加“自动继续”设置 —— 当响应达到长度限制时，可自动继续输出。
- 集成新的 Streamable HTTP 传输方式（取代 SSE）和 MCP 身份验证机制（取代配置中的访问令牌或 API 密钥），提升了对更多 MCP 服务器的支持与安全性。
- 企业用户福利：如果在 `~/.codeium/` 目录中放置 `.codeiumignore` 文件，现在可以在多个代码仓库中统一应用忽略规则

## 1.10.8 - Linux 修复补丁

**发布日期：2025 年 7 月 15 日**

- 修复对 RHEL 8（Red Hat Enterprise Linux 8）的兼容问题

## 1.10.5 - 浏览器相关修复

**发布日期：2025 年 6 月 19 日**

- 修复未认证状态下缺失 CSRF Token 的问题

## v 1.10.3 Windsurf Browser

June 12, 2025

- 可将浏览器上下文共享给 Windsurf
- 支持共享代码块、选中文本、网页元素、截图和控制台日志
- 可将这些内容直接发送到 Cascade

## v 1.10.1 计划模式

2025年6月10日

![](https://p.ipic.vip/um84ce.png)

### 计划模式

- 可以在“计划模式”下向 Cascade 发消息，该模式允许 Cascade 在执行编辑操作前先进行规划
- Cascade 会创建一个名为 `plan.md` 的文件，列出其准备执行的操作
- 用户可编辑这个计划文件，Cascade 会识别用户的修改内容

### 终端优化

- Cascade 面板中新增原生终端
- 现可直接在 Cascade 面板的终端中输入指令

### 移除旧版模式

- 移除旧版模式，现仅保留“Write模式”和“Chat模式”

### 其他改进

- 在 @提及 中加入图标
- 代码块根据主题自动适配样式，界面焕新
- 改进 `.codeiumignore` 文件支持
- 新增菜单，可快速打开和切换之前的对话

## v 1.9.4 修复

2025年6月3日

- 合并了来自 VS Code 1.99.3 的最新更新

## v 1.9.2 支持自带 Anthropic 密钥

2025年5月22日

#### 自带密钥（BYOK）

- 现在你可以在 Cascade 中使用自己的 Anthropic API 密钥，支持 Claude 4 Sonnet、Claude 4 Sonnet（思考版）、Claude 4 Opus 和 Claude 4 Opus（思考版）模型
- 使用方法：前往 [提供 API 密钥](https://windsurf.com/subscription/provider-api-keys) 页面并输入你的密钥
- 输入后返回 Windsurf 并重新加载页面，即可使用这些新模型

## v 1.9.1 SWE-1 改进

2025年5月21日

### 多模态支持

- 为 SWE-1 增加了多模态支持，现已支持图像输入

## v 1.9.0 新一代 SWE-1 模型系列

2025年5月15日

### SWE-1

- Windsurf 推出了全新的 SWE-1 模型
- SWE-1 具备前沿级别的模型能力
- 专业用户限时免费使用

### SWE-1-lite

- 替代原有 Cascade Base 的新版本，性能大幅提升
- 所有用户免费用

### SWE-1-mini

- SWE-1-mini 是为 Windsurf 中的标签补全功能特别优化的轻量模型

## v 1.8.2 Cascade Customization

May 6, 2025

### 界面改进

- 重设计型选择器
- 增加了继续按钮以处理单个工具调用限制
- 优化会话关联的工作区打开方式
- 代码块接受/拒绝组件新增紧凑模式，减少占用空间。

### Cascade UX Improvements

- Improvements to commit message generation quality
- Commit message generation reads from global rules as context
- Ability to edit proposed terminal command

### 自定义工作流

- 用户可创建并保存“工作流”
- 通过斜杠命令调用
- Cascade支持创建和编辑这些工作流
- 文件保存于工作区的.windsurf/workflows目录下

### 基于文件的规则

- 允许创建细粒度的规则文件，可全局生效、被@提及、由Cascade请求，或附加到文件 glob 模式中
- 规则文件保存于.windsurf/rules目录

### 多任务处理

- Cascade支持在切换对话时持续运行
- 并新增了通过下拉菜单或键盘快捷键切换对话的功能

### Cascade插件

- Cascade新增MCP服务器管理面板
- 简化了一键安装和卸载流程
- 优化搜索体验
- MCP现支持资源管理和多模型响应
- 未来将推出更多MCP服务选项

## v 1.8.0 团队功能

May 6, 2025

### 代码审查

- 团队管理员可安装GitHub应用进行代码审查及PR标题/描述编辑
- 每月提供500次审查，适用于团队和企业SAAS用户

### 对话分享
- 团队成员可生成共享链接，仅团队内其他成员可访问
- 适用于团队和企业SaaS方案

### 知识管理
- 团队管理员可连接Google账户并整理相关文档
- 团队成员可以@提及这些文档以便Cascade检索
- 适用于团队和企业SAAS用户

### 部署功能
- 团队用户可通过Windsurf设置连接Netlify账户
- 直接将应用部署至Netlify团队
- 支持SSO、自定义域等通过Netlify控制台的团队专用设置

### 分析仪表盘
- 更新了团队分析仪表盘
- 增加Cascade相关数据，如消息数量、工具调用次数、模型使用情况等

### 系统升级
升级至VS Code 1.99.1版本。

## v 1.7.2 全新应用图标与免费套餐升级

2025年4月28日

### 全新应用图标

- Windsurf 现已启用全新设计的应用图标
- Windsurf.com 网站也同步更新了新版文字标志
- （Mac 系统）可自定义应用图标也采用新标识

### 免费套餐升级

- 免费用户的使用额度提升
- 现可在写作模式用 Cascade
- Cascade 提示额度从每月 5 条提升至 25 条
- Fast Tab 功能无限制使用
- Cascade Base 功能无限制使用
- 可访问预览功能
- 支持 1 个部署操作

### 性能优化

- 部署应用时的性能和稳定性提升
- 即使已有部署配置文件（yaml），仍可创建新的部署
- 部署 Web 应用工具中新增“检查部署状态”的调用功能
- 对远程扩展（WSL、SSH、Dev Containers）进行了稳定性改进
- 在大型差异区域中打字时的性能表现更好

### 其他更新

- Command新增GPT-4.1支持
- 升级至 VSCode 基础版本 1.98

## v 1.7.1

2025年4月24日

- 更新了 IDE 应用商店链接，现同步至 Open VSX 镜像平台

## v 1.7.0

April 21, 2025

### 更新后的简化定价方案

#### 我们将取消 Flow 操作点数（Flow Action Credits）

- 为了让定价模式更简单，我们将取消 Flow 操作点数
- 此变更将于 2025 年 4 月 21 日起生效
- 各订阅方案现在将包含「提示点数」，也可以额外购买点数包

#### 用户提示点数（User Prompt Credits）

- 所有订阅方案将提供提示点数，发送每条消息时会消耗点数，而不是每次工具调用时消耗
- 可额外购买点数包
- 用户可在个人资料中开启自动充值功能（可设置上限）

#### 现有订阅方案

- 所有现有订阅方案将自动迁移至新的定价模式
- 更多详情请访问 [定价页面](https://windsurf.com/pricing)

## v 1.6.5

2025年4月16日

### 全新 o4-mini 模型上线，限时免费使用

- Windsurf 现已支持 o4-mini medium 和 o4-mini high 两个模型，所有用户均可免费使用
- 使用时间限于 4 月 16 日至 4 月 21 日

## v 1.6.4

2025年4月14日

### 全新 GPT 4.1 模型上线，限时免费使用

- Windsurf 现已支持全新的 GPT 4.1 模型，所有用户均可免费使用
- 使用时间限于 4 月 14 日至 4 月 21 日

2025年4月9日

### Cascade 现已支持JetBrains

- 查看完整的 [JetBrains 更新日志](https://windsurf.com/changelog/jetbrains)
- 阅读 [官方公告](https://windsurf.com/blog/windsurf-wave-7)

### **Codeium 正式更名为 Windsurf**

- 公司名称更改为Windsurf，插件产品也将更名为 **Windsurf Plugin**。

- 自从推出 [Windsurf 编辑器](https://windsurf.com/editor) 以来，我们明确了真正的

- 目标：融合人类创造力与机器智能，打造强大却毫不费力的体验。

## v 1.6.3 网站部署支持编辑子域名

2025年4月7日

## v 1.6.1

### **2025年4月2日**

### **部署功能（测试版）**

- 使用一句提示将应用部署至 Netlify，使用 windsurf.build 域名
- 认领你的应用 URL 后，可持续部署更新至相同项目
- 想部署新站点或更换子域名？只需让 Cascade 部署到新子域即可
- 所有用户都可使用，付费计划有更多权限

### **提交信息生成（测试版）**

- 在源码控制面板中一键生成提交信息
- 付费用户可用，且不额外消耗积分

### **Cascade 记忆功能增强**

- 新增记忆标签页
- 支持编辑记忆标题、内容和标签
- 支持搜索 Cascade 生成的记忆
- 设置中可启用/关闭自动生成记忆的开关
- 开启后自动记录重要上下文；关闭后只会根据请求创建记忆

### **长对话改进**

- 引入用户消息目录，滚动时显示，支持快速定位历史内容

### **Windsurf Tab 改进**

- 支持 Jupyter Notebook
- 新增上下文信号，包括 IDE 内部搜索内容

### **新图标（Mac）**

付费用户可选两款新应用图标：Retro 和 Pixel Surf

### **其他更新**

- 新增 Cascade 工具栏，整合 MCP、预览、部署等工具
- Cascade 支持 JSON 配置中的 SSE MCP 服务器
- “打开新窗口时关闭 Cascade” 的设置现已生效
- 输入框内容在新建会话和活跃会话中保留
- 终端界面焕新，“打开终端”按钮更加醒目
- 链接支持点击跳转
- 可开启“运行结束播放提示音”（测试中）
- 修复 Remote - SSH 扩展问题，支持自定义 SSH 路径
- 合并 VS Code 1.97.0 相关更新

## **v 1.5.9**

### **2025年3月25日**

### **新模型：Gemini 2.5 Pro（测试版）**

- Gemini 2.5 Pro 正式进入测试阶段！
- 每条消息消耗 1 个用户提示积分，每次工具调用消耗 1 个流程操作积分
- 所有计划（包括免费）均可使用
- *当前需求量大，团队正在扩容中*

### **修复内容**

- 修复了 Remote - SSH 扩展及自定义 SSH 路径设置问题

## **v 1.5.8**

### **2025年3月24日**

### **修复内容**

- Cascade 更好地遵循用户定义的记忆
- 浏览器预览改进
- 修复 Cascade 图标相关布局问题

## **v 1.5.6**

### **2025年3月18日**

### **全新 Windsurf Tab 体验**

- 将 Autocomplete、Supercomplete、Tab to Jump、Tab to Import 整合为一体化体验
- 使用更大更高质量模型，提升上下文理解、速度与质量

### **上下文优化**

- 补全功能可利用浏览文件、终端命令、Cascade 会话等信号
- 剪贴板上下文（默认关闭，可在高级设置中启用）
- 上下文长度扩展，提升补全效果

### **质量提升**

- 自动选择插入补全 vs 编辑补全更加精准
- Tab to Jump 跳转范围翻倍
- 提高缩进与排版质量

### **性能提升**

- 预测触发补全，连贯完成多轮建议
- 增强服务器能力与推理速度
- 网络优化，延迟更低

### **Tab 使用体验升级**

- 接受的补全高亮为绿色（可关闭）

- Tab to Jump 与 Tab to Import 小部件视觉更清晰、可点击

### **其他改进**

- 自动修复 Lint 模式中信用扣除更准确
- 终端命令现可作为上下文
- Debug Console 中 Tab 键支持补全
- Cascade 差异审查体验优化
- 修复信用不足提示、补全速度设置等问题
- 快捷设置下拉菜单改进
- 提升 CPU 和内存使用效率
- 新增 Neon 数据库作为 MCP 模板

## **v 1.4.6**

2025年3月10日

### **修复内容**

- 修复 Sonnet 3.7 网页搜索问题
- 修复代理设置问题

## v 1.4.4 修复

2025年3月6日

- 重新添加设置项至设置面板（代理设置、索引大小）

## v 1.4.3

### **2025年3月5日**

### **Windsurf 预览、自动 Linter、新 MCP 服务**

#### **Windsurf 预览（测试版）**

- Cascade 支持在 IDE 或浏览器中预览本地网站
- 可选中 React 或 HTML 元素作为上下文提供给 Cascade
- 错误信息也可传递作为上下文
- 可通过“启动 Web 应用”或工具栏图标激活预览
- 支持在 Chrome、Arc、Chromium 浏览器中展示
- 可在设置中关闭

#### **Cascade 自动 Linter**

- Cascade 现在自动修复自己生成代码中的 lint 错误
- 每步中 lint 修复不计入积分消耗
- 示例：若某次修改包含4个 lint 错误，将自动尝试修复，无额外消耗
- 可在设置中关闭

#### **新 MCP 服务支持**

- 可将 Cascade 配置为调用可信 MCP 工具服务器
- Windsurf 设置页提供常用 MCP 服务器列表
- 新用户界面便于添加和配置 MCP
- 设置页中仍支持用户自定义 JSON
- 专业与终极专业计划可用，团队与企业计划即将支持

#### **Tab-to-Import**

- 自动补全缺失依赖导入
- 使用 Tab 接受建议即可完成导入

#### **建议操作**

- Cascade 提供任务相关建议操作，点击即可继续操作流程

#### **拖放文件作为上下文**

- 支持从文件资源管理器拖放文件进 Cascade
- 支持所有文件类型

#### **模型管理控制**

- 团队管理员可选择开放哪些模型供团队使用
- 成员默认仅见管理员启用的模型

#### **Claude Sonnet 3.7 支持**

- Cascade 支持 Claude Sonnet 3.7
- 工具调用与流程操作表现更优

#### **其他更新**

- 新版快速设置界面与高级设置视图

- 支持读取 .gitignore 文件

- 推出用户推荐系统

- Windsurf directory：官方精选规则集，帮助 Cascade 更好理解你与代码库

  - [查看规则目录](https://codeium.com/windsurf/directory)


## **v 1.3.10**

### **修复内容**

- 优化 Claude 3.7 Sonnet 的积分使用
- 更新后建议在新会话中运行所有后续操作
- 新增查看/编辑 .gitignore 文件的选项

## **v 1.3.9**

### **新模型：Claude 3.7**

- Cascade 现已支持 Claude 3.7 Sonnet（高级模型）

  

  - 每条消息消耗 1 积分，每次工具调用也消耗 1 积分
  - “思维模式”下使用该模型积分乘数为 1.5x

  

- [渐进推出] GPT-4.5 模型测试中

  

  - 因成本、速率限制与质量测试，将逐步放出


## **v 1.3.3**

2025 年 2 月 13 日

## **模型上下文协议、自定义应用图标，以及 Tab 跳转**

### **模型上下文协议 (Model Context Protocol)**

- Cascade 现在支持模型上下文协议（MCP）
- 你可以设置 Cascade 对话使用 MCP 服务器进行工具调用
- 可以通过点击 Cascade 输入栏的锤子图标设置 MCP
- 对所有个人用户计划开放
- 每次 MCP 工具调用消耗 1 个流动操作积分，无论执行结果如何

### **新增可自定义应用图标**

- 你现在可以更改 Windsurf 的应用图标（仅限 Beta & Mac）
- 付费用户可以选择 Classic、Blueprint、Hand-drawn、Valentine 等风格
- 更改图标后需重启系统以实现系统级别的图标更新
- 对所有付费用户计划开放
- Windows 和 Linux 平台即将上线该功能

### **补全功能改进**

- Tab to Jump 功能正式发布，可智能预测下一个编辑位置并通过 Tab 键跳转

### **Cascade Turbo 模式**

- Cascade Turbo 模式全面升级，可自动执行终端命令（除非被加入拒绝列表）
- 对所有个人用户计划开放

### **Cascade 拖拽图片支持**

- Cascade支持从系统文件或截图中拖拽图片到输入框

### **积分使用可视化**

- Cascade 现在可以显示每个操作消耗了多少积分
- 鼠标悬停在执行后的操作上可查看积分消耗

### **其他改进**

- 修复了 Cascade 终端命令的一些 Bug
- 命令步骤现在会显示自动执行的行为信息
- 修复了重新加载后 Cascade 面板总是自动打开的问题，即使用户已在设置中关闭
- 你可以使用 Cmd/Ctrl+L 快捷键 @提及 终端文字，新增了选择弹窗
- @docs 结果现在支持滚动查看，避免选项被截断
- @docs 支持更多服务，如 Vercel、Bun、Supabase 等

### **新增模型**

- Cascade 新增高级模型：Gemini 2.0 Flash

  

  - 每条消息消耗 0.25 用户提示积分，每次工具调用消耗 0.25 流动操作积分


## **v 1.2.4**

2025 年 1 月 31 日

## 新增模型：DeepSeek-R1、DeepSeek-V3 和 o3-mini

### 模型

- 新增高级模型：DeepSeek-R1、DeepSeek-V3 和 o3-mini÷
- DeepSeek-V3 和 R1 适用于 Pro 和 Pro Ultimate 用户
- DeepSeek-V3：每条消息消耗 0.25 用户提示积分，每次工具调用消耗 0.25 流动操作积分
- DeepSeek-R1：每条消息消耗 0.5 用户提示积分，每次工具调用消耗 0.5 流动操作积分
- o3-mini 适用于所有付费用户
- o3-mini：每条消息和每次调用均消耗 1 个积分


### 修复内容

- 增加拖拽图片至 Cascade 功能支持

## v 1.2.0 弹性积分系统上线，Cascade Base 开源！

2025 年 1 月 25 日

### 弹性积分系统

- Windsurf 现在引入 **弹性积分（Elastic Credits）** 系统

- 拆分为两类积分：

  - 用户提示积分（User Prompt Credits）
  - 流动操作积分（Fluid Action Credits）
  
- 模型对话、文件上传、API 工具调用、终端操作都将按实际用途计费，更透明可控

- 设置 > 积分详情中查看各模型和工具的积分消耗

- 套餐每月包含的积分数不变，仅计费方式调整

### Cascade Base 开源

- 作为本地运行的轻量 AI 助理，开源地址：https://github.com/exafunction/cascade
- 用户可以自定义工具链，连接任意本地服务或模型

### **新增模型**

- **Claude 3 Opus** 模型上线（适用于 Pro Ultimate 用户）
- 消耗：每条消息需 2 用户提示积分，每次工具调用需 2 流动操作积分

### **改进项**

- 文件上传功能更清晰，拖拽上传 UI 优化
- 文件上传后自动触发 @files 工具处理内容

## v 1.1.1

2025 年 1 月 19 日

### **修复内容**

- 修复了终端调用 API 返回乱码的问题
- 修复调用工具时出错未显示详细错误的 bug
- 修复选择文件时的路径错乱问题
- 修复长时间运行后模型响应变慢的问题

## v 1.1.0

2025 年 1 月 15 日

## 文件支持、自动调用工具，以及 Claude 3 模型上线！

### **文件功能（Beta）**

- 你现在可以上传文件至对话中，AI 可自动引用内容
- 支持的文件类型：.txt, .md, .pdf, .docx, .csv, .json 等
- 文件内容可以通过 @files 工具进行提问和搜索
- 文件将默认保存在本地，不会上传至云端

### 自动调用工具

- Cascade 现在支持 **自动调用工具**
- 在你与 AI 对话过程中，会自动触发相关工具（如终端、API、搜索等）
- 可在设置中手动启用或关闭自动调用

### 新增 Claude 3 模型

- 新增 Anthropic 的 Claude 3 Haiku 和 Claude 3 Sonnet 模型

  - Claude 3 Haiku：快速响应、低消耗
  - Claude 3 Sonnet：更强大，适用于复杂任务
  
- 默认对所有付费用户开放，Sonnet 限 Pro 及以上

## v 1.0.6

2024年12月6日

### 使用透明度

![1.0.6 更新日志横幅](https://exafunction.github.io/public/changelog/windsurf/1.0.6/106_hero_img.jpg)

#### 使用透明度与定价

- 推出 Windsurf 的全新使用和定价系统。详情请参见[定价](https://codeium.com/redirect/windsurf/learn-pricing)
- 快速设置面板现在会显示当前计划的使用情况，包括试用期到期时间、下一个刷新周期的信息以及升级链接
- Cascade 新增“Legacy Chat”模式，当用户的 Flow Credits 耗尽时激活。此模式功能有限，但无需使用 Flow Credits
- 在设置面板中查看 Cascade 的使用情况。更多信息请参见[此处](https://docs.codeium.com/windsurf/usage#viewing-your-usage)。

#### Cascade 图片上传

- Cascade 图片上传的大小限制取消为 1MB

#### 增强 Python 语言支持

- 通过 Windsurf Pyright 提供功能丰富的 Python 语言支持。Windsurf Pyright 是 Pylance 的替代方案

### 其他修复

- 从其他基于 VS Code 的 IDE 导入，可连同设置一起导入代码片段
- 快速设置面板中可查看并配置 AI 相关快捷键

## v 1.0.5 图片上传

2024年11月27日

#### 上传图片到 Cascade

- Cascade 现在支持在高级模型上传图片
- 上传图片后可以让 Cascade 构建或调整 UI

#### 新增快捷键

- 快捷键在 Cascade 差异视图中导航（默认是 ⌥/Alt + j 和 ⌥/Alt + k）
- 快捷键在具有 Cascade 差异的文件间导航（默认是 ⌥/Alt + h 和 ⌥/Alt + l）

### 其他修复

- 增加了控制 Cascade 是否自动打开创建/编辑文件的选项（默认启用）
- 修复了影响部分用户的自动补全设置问题
- 快速设置面板 UI 更新

## v 1.0.4 解释并修复问题

2024年11月21日

- Cascade 将尝试修复代码库中的问题
- 鼠标悬停在问题上时会出现选项

#### 从cursor导入

- 导入设置和扩展
- 可通过命令面板或重置引导流程获取

#### 新增快捷键

- 接受文件中所有活动差异的快捷键（默认是 ⌘/Ctrl + ⏎）
- 拒绝文件中所有活动差异的快捷键（默认是 ⌘/Ctrl + ⌫）
- ⌘/Ctrl + Shift + L 打开 Cascade 中的新对话，同时复制选中的终端/编辑器文本到新对话

### 改进与修复

####  改进的命令

- 改进了 Jupyter Notebook 中的命令体验

####  改进的差异视图

- 删除了 Cascade 删除文件中的差异显示
- 更清晰的光标指示，显示已删除文本的差异内容可被选中

###  其他修复

- Windsurf 快速设置面板在点击面板外部时会自动关闭
- 提高了某些主题中引导元素的可见性
- 修复了一些轻微的布局问题
- 增加了加入 Discord 社区的按钮
- 提高了通过 SSH 使用时 Cascade 面板的稳定性
- 由 Cascade 编辑/创建的文件将自动在后台打开。如果没有活动的编辑器，首个编辑/创建的文件将作为当前活动编辑器打开
- 在标题栏下拉菜单中新增了更新日志链接，同时在用户更新版本后显示更新日志提醒

## v 1.0.2 - Windsurf上线

2024年11月13日

![](https://exafunction.github.io/public/changelog/windsurf/1.0.2/102_hero_img.jpg)

- 正式发布！
- 使用 Cascade 与 Codeium 的完整代码库上下文聊天，支持多文件编辑
- 新模式 Supercomplete，可预测下一步意图
