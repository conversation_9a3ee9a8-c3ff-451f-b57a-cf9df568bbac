### 🎉 New in v3.20

- __Cerebras Provider Support:__ Enhanced performance with updated model selection (Qwen and Llama 3.3 70B only) and increased context window for Qwen 3 32B from 16K to 64K tokens.
- __Claude Code for Windows:__ Improved system prompt handling to fix E2BIG errors and better error messages with guidance for common setup issues.
- __Hugging Face Provider:__ Added as a new API provider with support for their inference API models.
- __Moonshot Chinese Endpoints:__ Added ability to choose Chinese endpoint for Moonshot provider and added Moonshot AI as a new provider.
- __Enhanced Stability:__ Robust checkpoint timeout handling, fixed MCP servers starting when disabled, and improved authentication sync across multiple VSCode windows.



- __Optimized for Claude 4:__ Cline is now optimized to work with the Claude 4 family of models, resulting in improved performance, reliability, and new capabilities.

- __Gemini CLI Provider:__ Added a new Gemini CLI provider that allows you to use your local Gemini CLI authentication to access Gemini models for free.

- __WebFetch Tool:__ Gemini 2.5 Pro and Claude 4 models now support the WebFetch tool, allowing <PERSON><PERSON> to retrieve and summarize web content directly in conversations.

- __Self Knowledge:__ When using frontier models, <PERSON><PERSON> is self-aware about his capabilities and featureset.

- __Improved Diff Editing:__ Improved diff editing to achieve record lows in diff edit failures for frontier models.

- __Claude 4 Models:__ Now with support for Anthropic Claude Sonnet 4 and Claude Opus 4 in both Anthropic and Vertex providers.

- __New Settings Page:__ Redesigned settings, now split into tabs for easier navigation and a cleaner experience.

- __Nebius AI Studio:__ Added Nebius AI Studio as a new provider. (Thanks @Aktsvigun!)

- __Workflows:__ Create and manage workflow files that can be injected into conversations via slash commands, making it easy to automate repetitive tasks.

- __Collapsible Task List:__ Hide your recent tasks when sharing your screen to keep your prompts private.

- __Global Endpoint for Vertex AI:__ Improved availability and reduced rate limiting errors for Vertex AI users.