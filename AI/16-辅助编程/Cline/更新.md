## v3.20

- __Cerebras 提供商支持：__ 通过更新的模型选择（仅限 Qwen 和 Llama 3.3 70B）和将 Qwen 3 32B 的上下文窗口从 16K 增加到 64K 令牌来增强性能。
- __Windows 版 Claude Code：__ 改进了系统提示处理以修复 E2BIG 错误，并为常见设置问题提供了更好的错误消息和指导。
- __Hugging Face 提供商：__ 添加为新的 API 提供商，支持其推理 API 模型。
- __Moonshot 中文端点：__ 为 Moonshot 提供商添加了选择中文端点的功能，并将 Moonshot AI 添加为新提供商。
- __增强稳定性：__ 强大的检查点超时处理，修复了禁用时 MCP 服务器启动的问题，并改进了多个 VSCode 窗口间的身份验证同步。
- __Gemini CLI 提供商：__ 添加了新的 Gemini CLI 提供商，允许您使用本地 Gemini CLI 身份验证免费访问 Gemini 模型。
- __WebFetch 工具：__ Gemini 2.5 Pro 和 Claude 4 模型现在支持 WebFetch 工具，允许 Cline 直接在对话中检索和总结网页内容。
- __自我认知：__ 使用前沿模型时，Cline 对自己的能力和功能集有自我认知。
- __改进的差异编辑：__ 改进了差异编辑，为前沿模型实现了创纪录的低差异编辑失败率。
- __Claude 4 模型：__ 现在支持 Anthropic Claude Sonnet 4 和 Claude Opus 4，在 Anthropic 和 Vertex 提供商中均可使用。
- __新设置页面：__ 重新设计的设置，现在分为选项卡以便更轻松的导航和更清洁的体验。
- __Nebius AI Studio：__ 添加 Nebius AI Studio 作为新提供商。（感谢 @Aktsvigun！）
- __工作流：__ 创建和管理可通过斜杠命令注入到对话中的工作流文件，使自动化重复任务变得容易。
- __可折叠任务列表：__ 在共享屏幕时隐藏您的最近任务，以保持提示的私密性。
- __Vertex AI 全球端点：__ 为 Vertex AI 用户改进了可用性并减少了速率限制错误。