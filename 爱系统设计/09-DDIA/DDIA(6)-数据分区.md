# DDIA(6)-数据分区
[TOC]
## 0 前言

对大数据集或非常高吞吐量，仅复制还不够，还需将数据拆分，成为分区（partitions），也称分片（sharding）[^i]。

[^i]: 一种故意将大型DB分解成小型DB的方式。和 **网络分区（network partitions, netsplits）** 无关，这是节点之间网络故障的一种。

> 分区 (partition)，对应MongoDB、ES中的shard，HBase 的Region，Bigtable的tablet，Cassandra的vnode，Couchbase的vBucket。但分区 (partitioning)更普遍。

### 0.1 定义

每条数据（或每条记录，每行或每个文档）属于且仅属于某特定分区。每个分区都能视为一个完整小型数据库，虽然数据库可能存在跨分区操作。

### 0.2 目的

提高可扩展性。不同分区可放在一个无共享集群的不同节点。这样的一个大数据集可分散在更多磁盘，查询负载也随之分布到更多处理器。

单分区查询时，每个节点对自己所在分区查询可独立执行查询操作，添加更多节点就能提高查询吞吐量。大型复杂查询尽管比较困难，但也能做到跨节点并行处理。

分区DB在1980s由Teradata、NonStop SQL等产品率先推出，最近因NoSQL和基于Hadoop的数仓重新被关注。有些系统是为事务处理而设计，有些则用于分析：这种差异会影响系统运作方式，但是分区基本原理都适用于这两种工作方式。

- 本文先介绍分割大型数据集的方法，并观察索引如何与分区配合
- 然后讨论rebalancing，若想添加、删除集群中的节点，则须rebalancing
- 最后，概述DB如何将请求路由到正确的分区并执行查询

## 1 分区与复制

分区一般和复制协作：每个分区的多个节点都有副本，即某条记录属于特定分区，而同样内容会存储在不同节点，以提高系统容错性。

一个节点可存储多个分区。如图-1，主从复制模型和分区组合时数据的分布情况。每个分区都有自己的主副本，如被分配给某节点，从库副本被分配给其他节点。所以，一个节点可能是某些分区的主副本，同时也是其他分区的从副本。

上一篇文中讨论的复制内容都适用于分区数据的复制。分区方案选择通常独立于复制，因此本文不再讨论复制。

![图-1 组合使用复制和分区：每个节点充当某些分区的主库，其他分区充当从库](https://img-blog.csdnimg.cn/3605ce39e0054885817a0a28dee6e9dc.png)

## 2 KV数据分区

海量数据想切分，如何决定在哪些节点存储哪些记录？

分区的目标：将数据和查询负载均匀分布在各节点。若每个节点平均分担数据和负载，则理论上10个节点能处理10 倍的数据量和10 倍于单节点的读写吞吐量（暂忽略复制）。

但若分区不均，则会导致某些分区节点比其他分区有更多数据量或查询负载，即倾斜，导致分区效率大幅下降。极端的，所有负载压在一个分区节点，其余9个节点空闲，系统瓶颈就是这最忙节点。这时的高负载分区即是系统热点。

### 2.1 避免热点

最简单的，将记录随机分配给所有节点：

- 这能在所有节点比较均匀分布数据
- 但缺点：试图读取特定数据时，不知保存在哪个节点，必须并行查询所有节点

可优化该方案。假设数据是KV，即总能通过K访问记录。如在一本百科全书，可通过标题查找一个条目；而所有条目按字母序排序，因此能快速找到目标条目。

### 2.2 Key Range分区

为每个分区指定一块连续的K范围（以min和max指示），如纸质百科全书的卷（图-2）。若知K区间边界，就能轻松确定哪个分区包含这些K。若你还知道分区所在节点，则可直接请求相应节点（就像从书架上选取正确书籍）。

![图-2 百科全书按K区间进行分区](https://img-blog.csdnimg.cn/c1a2c1e711ef41d7977cc7bfcc7ce745.png)

K区间不一定要均匀分布，因为数据本身可能就不均。如图-2中，1卷包含A、B开头的单词，但12卷则包含T、U、V、X、Y和Z开头单词。若只是简单规定每个卷包含两个字母，可能导致一些卷比其他卷大。为更均匀分布数据，分区的边界应适配数据本身的分布特征。

分区边界可由管理员手动确定或由DB自动选择。Bigtable及其开源版本HBase和2.4版本之前的MongoDB都采用该分区策略。

每个分区中，可按K排序保存。范围扫描就很简单，将K作为联合索引来处理，从而在一次查询中获取多个相关记录。假设有个程序存储网络传感器的数据，K是测量的时间戳（年月日-时分秒）。范围扫描此时很有用，可快速获取某月内的所有数据。

#### 缺点

某些访问模式会导致热点。 若K是时间戳，则分区对应于一个时间范围，如每天一个分区。 测量数据从传感器写入DB时，所有写入操作都集中在同一分区（即当天的分区），导致该分区在写入时处于高负载，而其他分区始终空闲。

为避免该问题，需要使用时间戳之外的内容作为K的第一项。 可考虑每个时间戳前添加传感器名称，这样首先按传感器名称，再按时间进行分区。假设多个传感器同时运行，则写入负载最终会均匀分布在多个节点。 当想要获取一个时间范围内、多个传感器的数据，可根据传感器名称，各自执行单独的范围查询。

### 2.3 根据键的Hash分区

由于数据倾斜和热点问题，许多分布式系统采用基于K散列函数来分区。

好的散列函数可处理倾斜数据并使其均匀分布。

数据分区目的的hash函数无需健壮的加密能力，如Cassandra 和 MongoDB 使用 MD5。许多编程语言也有内置的简单哈希函数（主要用于哈希表），但可能不适合分区：如Java 的 `Object.hashCode()`，同一K可能在不同进程中有不同哈希值。

确定合适的hash函数后，就能为每个分区分配一个hash范围（而不是直接就是K的范围），每个K通过hash散列落在不同分区，如图-3：
![图-3：基于K的hash来分区](https://img-blog.csdnimg.cn/4ba8c5a90c1940bba372622710f5f21c.png)

这种方案擅长在分区之间均匀分配K。分区边界可以是均匀间隔，也可以是伪随机选择（也称为一致性哈希）。

> #### 一致性哈希
>
> 一种平均分配自己负载的方法，最初用于CDN等互联网缓存系统。 采用随机选择的分区边界来规避中央控制或分布式共识。此处的一致性与副本一致性或ACID一致性无任何关联 ，它只描述了数据动态平衡的一种方法。
>
> 如“[分区再平衡](#分区再平衡)” 中所见，这种特殊分区方法对于DB实际效果并非很好，目前很少使用（虽某些DB文档仍用一致性哈希说法，但其实不准确）。 因为有可能产生混淆，所以最好避免用一致性哈希这术语，而只是把它称为 **散列分区（hash partitioning）**。

但通过hash分区，失去高效的执行范围查询的能力：即使相邻的K，经过hash后也会分散在不同分区。MongoDB中，若使用hash分区，则范围查询都必须发送到所有分区。而Couchbase或Voldemort干脆直接不支持K的范围查询。

Cassandra在两种分区策略之间采取折中。 Cassandra的表可使用由多个列组成的复合主键。键中只有第一部分可用于 hash 分区，而其他列则被用作 Casssandra 的 SSTables 中排序数据的联合索引。尽管不支持复合主键的第一列的范围查询，但若第一列已指定固定值，则可对其他列执行高效的范围查询。

联合索引为一对多关系提供一个优雅的数据模型。如社交网站，一个用户可能发布很多消息更新。若更新的K被设置为 `(user_id,update_timestamp)`，则能高效检索某用户在某时间段内，按时间戳排序的所有更新。不同用户可存储在不同分区，但对某一用户，消息会按时间戳顺序存储在同一分区。

### 2.4 负载偏斜与热点消除

##### 负载偏斜

hash分区可减少热点，但也无法完全避免：极端的，所有读/写操作都针对同K，则所有请求都会被路由到同一分区。如社交网站的坐拥百万粉丝的大V，每次发布一些热点事件，就会引发一场访问风，导致某博宕机。导致同一K的大量写操作（K可能是大V的用户ID或人们正在评论的事件ID）。此时，hash策略失效，因为两个相同ID的hash值仍相同。

##### 热点消除

大多数据系统无法自动消除这种高偏斜负载，只能通过应用层来减少倾斜。如若某K被确认为热点，简单方案就是在K的开始或结尾添加一个随机数。只要一个两位数的十进制随机数就能将主键再分散为100种不同的K，以存储在不同分区。

但之后的任何读取都需额外工作，须从所有100个K分布中读取数据，然后聚合。因此通常只对少量热点K附加随机数才有意义；对写吞吐量低的大多数K，这些都是不必要的开销。

还需额外的元数据来标记哪些K进行了特殊处理。

## 3 分区与二级索引

目前的分区方案都依赖KV数据模型。KV模型简单，都是通过K访问记录，自然可根据K确定分区，并将读写请求路由到负责该K的分区。

但若涉及二级索引，就很复杂。二级索引通常并不能唯一标识一条记录，而是一种加速特定值的查询，如查询用户JavaEdge的所有操作，查找包含词语 `java` 的所有博客等。

许多KV存储（如HBase）为了减少实现复杂度而放弃二级索引，但一些（如 Riak）已开始支持它们，二级索引也是 Solr 和 ES 等搜索服务器的根本。

二级索引的主要挑战是不能整齐地映射到分区。有两种方案支持对二级索引进行分区：

- 基于文档的分区（document-based）
- 基于关键词（term-based）的分区

### 3.1 基于文档的二级索引进行分区

二手车销售网（如图-4）。 每个列表都有个唯一的文档ID，以此对DB进行分区，如分区0 中的ID 0~499，分区1中的 ID 500~999。

![图-4：基于文档的二级索引进行分区](https://img-blog.csdnimg.cn/e5a44e2bba4844ee8987d01d14919496.png)

用户搜车，可按颜色和厂商过滤，所以需要在颜色和厂商设置二级索引（在文档DB中这些是字段（field），关系DB中这些是列（column））。每当将一辆红色汽车添加到DB，DB分区都会自动将其添加到索引条目 `color:red` 的文档ID列表。

[^ii]: 若DB仅支持KV模型，则你可能尝试在应用程序代码中创建从值到文档ID的映射来实现二级索引。 若沿着这条路，请确保你的索引与原 DB 系统保持数据一致。 竞争条件和中间写入失败（其中一些更改已保存，但其他更改未保存）都很容易导致数据不同步。

这种索引方法中，每个分区完全独立，各自维护自己的二级索引，且只负责自己分区内的文档，而不关心其他分区的数据。每当需要写DB（添加，删除或更新文档），只需处理包含你正在编写的目标文档ID的分区。因此，文档分区索引也被称为本地索引，而非全局索引。

但读时注意：除非对文档ID特别处理，否则不太可能将所有特定颜色或品牌的汽车放在同一分区。图-4中，红车出现在分区0、1。因此，若搜索红车，就需将查询发送到所有分区，然后合并所有返回的结果。


这种查询分区DB的方法有时称为分散/聚集（scatter/gather），显然这种二级索引的查询代价高昂。即使并行查询分区，分散/聚集也容易导致尾部读延迟显著放大。但它依旧被广泛使用：MongoDB，Cassandra，ES都直至基于文档分区的二级索引。大多DB供应商建议用户自己构建合适的分区方案，尽量由单个分区满足二级索引查询，但这并不总是可行，尤其是当查询中使用多个二级索引时（例如同时需按颜色、制造商两个条件查询）。

### 3.2 基于词条(Term)的二级索引分区

可对所有的数据构建全局索引，而非每个分区维护自己的二级索引（本地索引）。为避免成为瓶颈，不能将全局索引存储在一个节点，否则就破坏了设置分区均衡的目的。所以，全局索引也必须分区，但可以采用与K不同的分区策略。

如图-5，所有数据分区的红车收录在索引color:red，而索引本身也是分区的，如从 `a` 到 `r` 开始的颜色在分区 0，`s` 到 `z` 分区 1。类似的，汽车制造商的索引也被分区（两个分区的边界分别是 `f`、 `h`）。

![图-5：基于关键词对二级索引进行分区](https://img-blog.csdnimg.cn/a86f01cce08648b1b36bea88cf871392.png)

这种索引称为 词条分区（term-partitioned），以待寻找的关键词本身作为索引。如颜色：`color:red`。关键词（Term）这个名称源于全文索引（一种特殊的二级索引），term指文档中出现的所有单词集合。

可直接通过 关键词 本身来全局划分索引，或对其hash。根据关键词本身分区对范围扫描很有用（如对数值类的属性，e.g. 车报价），而对关键词hash分区可更均匀划分分区。

#### 全局的词条分区 V.S 文档分区索引

- 它使读更高效，即无需分散 / 收集对所有分区都执行一遍查询。相反，客户端只需向含词条的分区发出读请求
- 全局索引的缺点，写速度较慢且复杂，因为单个文档的更新是，可能影响多个二级索引，而二级索引的分区可能位于不同分区或不同节点，

理想情况下，索引应时刻保持最新，即写入的每个数据要立即反映在最新的索引。但对词条分区，这需要跨分区的分布式事务，写入速度将受到极大影响，所以现有 DB 都不支持同步更新二级索引。

实践中，对全局二级索引的更新都是异步（即若在写入后马上读索引，则更新可能尚未反映在索引中）。

## 4 分区再平衡（rebalancing）

随业务井喷，DB出现变化：

* 查询负载增加，需更多CPU处理负载
* 数据规模增加，需更多磁盘和内存来存储
* 节点可能故障，需要其他节点接管失效节点

所有这些更改都要求数据、请求可以从一个节点转移到另一个节点。 将负载从集群中的一个节点向另一个节点移动的过程称为 再平衡（rebalancing）。无论哪种分区策略，分区rebalancing通常至少要满足：

* rebalancing后，负载、数据存储、读写请求应在集群内更均匀分布
* rebalancing执行时，DB应能继续正常读写
* 避免不必要的负载迁移，以加速rebalancing效率，并尽量减少网络和磁盘 I/O 影响


### 4.1 再平衡策略

#### 4.1.1 反面教材：hash mod N

图-3提过，最好将hash值分成不同区间范围，然后每个区间分配给一个分区。

那为何不使用mod（Java中的%运算符）。如`hash(key) mod 10` 返回介于 0 和 9 之间的数字。若有 10 个节点，编号为 0~9，这似乎是将每个K分配给一个节点的最简单方法。

但问题是，若节点数量 N 变化，大多数K将需从一个节点移动到另一个。假设  hash(key)=123456 。最初10个节点，则该K一开始在节点6（因为123456 mod 10 = 6）：

- 当增长到 11 个节点，K需移动到节点 3（$123456\ mod\ 11 = 3$）
- 当增长到 12 个节点，K需要移动到节点 0（$123456\ mod\ 12 = 0$）

这频繁的迁移大大增加rebalancing的成本。

所以重点是减少迁移的数据。

#### 4.1.2 固定数量的分区

还好有个很简单的解决方案：创建比节点更多的分区，并为每个节点分配多个分区。如10 个节点的集群，DB可能会从一开始就逻辑划分为 1,000 个分区，因此大约有 100 个分区分配给每个节点。

若一个新节点加入集群，新节点可以从当前每个节点中窃取一些分区，直到分区再次达到全局平衡。过程如图-6。若从集群中删除一个节点，则会发生相反情况。

选中的整个分区会在节点之间迁移，但分区的总数不变，K到分区的映射关系也不变。唯一变的是分区所在节点。这种变更并非即时，毕竟在网络上传输数据总需要时间，所以在传输过程中，旧分区仍可接收读写操作。

![图-6：将新节点添加到每个节点具有多个分区的数据库集群](https://img-blog.csdnimg.cn/b21101e3a0ae4a01947e03da4035a220.png)

原则上，也可以将集群中的不同的硬件配置因素考虑进来：性能更强大的节点分配更多分区，从而能分担更多负载。在ES 、Couchbase中使用了这种动态平衡方法。

使用该策略时，分区数量通常在DB第一次建立时确定，之后不会改变。虽然原则上可拆分、合并分区，但固定数量的分区使得操作都更简单，因此许多固定分区策略的 DB 决定不支持分区拆分。因此，初始化时的分区数就是你能拥有的最大节点数量，所以你要充分考虑将来业务需求，设置足够大的分区数。但每个分区也有额外管理开销，选择过高数字也有副作用。

若数据集的总规模难预估（如可能开始很小，但随时间推移会变异常得大），此时，选择合适的分区数就很难。由于每个分区包含的数据量上限是固定的，因此每个分区的实际大小与集群中的数据总量成正比：

- 若分区里的数据量很大，则再平衡和从节点故障恢复的代价就很大
- 若分区太小，则会产生太多开销

分区大小应“恰到好处”，若分区数量固定了，但总数据量却变动很大，则难以达到最佳性能。

#### 4.1.3 动态分区

对于使用K范围分区的DB，若边界设置有问题，可能导致所有数据都挤在一个分区而其他分区基本为空，则设定固定边界、固定数量的分区将很不便：而手动去重新配置分区边界又很繁琐。

对此，K范围分区的DB，如HBase采用动态创建分区：

-   当分区的数据增长超过配置的阈值（HBase默认10GB），就会拆分成两个分区，每个承担一半数据量
- 相反，若大量数据被删除，并且分区缩小到某阈值以下，则将其与相邻分区合并

类似B树分裂过程。

每个分区分配给一个节点，而每个节点可承载多个分区，和固定数量的分区一样。大分区拆分后，可将其中一半转移到另一个节点，以平衡负载。HBase中，分区文件的传输通过 HDFS实现。

动态分区的一个优点，分区数量可自动适配数据总量：

- 若只有少量数据，少量分区就够，开销也很小
- 若有大量数据，每个分区的大小则被限制在一个可配的最大值

但一个空DB，因为没有确定分区边界的先验信息，所以会从一个分区开始。数据集开始时可能很小，直到达到第一个分区的分裂点前，所有写操作都必须由单节点处理，而其他节点则处于空闲状态。为解决该问题，HBase、MongoDB允许在一个空DB配置一组初始分区（预分割，pre-splitting）。在K范围分区策略下，预分割需要提前知道K的分布情况。

动态分区不仅适于K的范围分区，也适用于hash分区。MongoDB 2.4 开始同时支持范围和hash分区，且都支持动态分割分区。

#### 4.1.4 按节点比例分区

- 动态分区策略，分区数与数据集大小成正比，因为拆分、合并过程使每个分区的大小维持在固定的min和max之间
- 固定数量的分区方式，每个分区的大小与数据集大小成正比

两种情况下，分区数都和节点数无关。

Cassandra则采用第三种方案，使分区数与集群节点数成正比。即每个节点具有固定数量的分区。此时，每个分区的大小和数据集大小成正比，而节点数不变，但是当增加节点数时，分区将再次变小。由于较大数据量通常需大量节点来存储，因此这种方法也使每个分区的大小保持稳定。

当一个新节点加入集群时，它随机选择固定数量的现有分区进行拆分，然后拿走这些分区的一半数据量，将另一半数据留在原节点。随机选择可能产生不公平的分区分割，但平均分区数较大时（Cassandra默认每个节点有256个分区），新节点最终会从现有节点获得相当数量的负载。 Cassandra 3.0引入优化算法，可避免不公平的分割。

随机选择分区边界要求使用hash分区策略（可从hash函数产生的数字范围中设置边界）。这种方法也最符合一致性哈希的定义。

### 4.2 运维：手动  or 自动再平衡

动态是自动还是手动执行？

全自动的再平衡（即由系统自动决定，何时将分区从一个节点迁移到另一个节点，无须人工干预）和完全手动（即分区到节点的映射由管理员显式配置）之间有个权衡。如Couchbase会自动生成一个推荐的分区分配，但需管理员确认生效。

全自动再平衡更方便，正常维护之外操作工作很少，但可能不可预测。再平衡是个昂贵操作，因其需重新路由请求，并将大量数据从一个节点迁移到另一个节点。若出现异常，可能会使网络或节点的负载过重，并降低其他请求的性能。

自动平衡和自动故障检测相结合也可能存在风险。假设某节点过载，且对请求的响应暂时很慢，而其他节点得出结论：过载节点已失效，并自动平衡集群，转移其负载。客观上，这会加重该节点、其他节点和网络的负载，从而使情况更糟，甚至级联失效。

对此，再平衡过程中有人参与是更推荐做法。这比全自动响应慢一点，但可有效防止意外。

## 5 请求路由

现已将数据集分布多个节点，但当客户端要发送请求时，如何知道应该连接哪个节点？若分区再平衡，分区和节点的映射也随之变化。

对此，需要有一段逻辑知晓这些变化并负责客户端的连接：如若我想读/写K “foo”，需连接哪个IP地址和端口号？

这其实就是服务发现,任何通过网络访问的系统都有此问题，特别是当其目标高可用（在多台机器上有冗余配置）。该问题有多种方案，如图-7:

1. 允许客户端连接任一节点（如采用循环策略的负载均衡）。若该节点恰有请求的分区，则直接处理该请求；否则，将请求转发到下一个合适的节点，接收回复，并返回给客户端
2. 将所有客户端请求都发送到路由层，负责将请求转发到对应分区节点。路由层本身不处理任何请求，仅负责分区的负载均衡
3. 客户端感知分区和节点的分配关系。此时，客户端可直接连接到目标节点，而无需任何中介

不管啥方案，关键问题：作出路由决策的组件（可能是某个节点，路由层或客户端）如何知道分区和节点之间的对应关系及变化？

![图-7：将请求路由到正确节点的方案](https://img-blog.csdnimg.cn/e6a48fa9415e47a2a81ed8e16d37c59b.png)

这是个有挑战的问题，所有参与者都要达成共识，否则请求可能被发送到错误节点。 在分布式系统的共识协议，通常都难以正确实现。

许多分布式数据系统依赖独立的协调服务（如zk），跟踪集群内的元数据，如图-8： 每个节点在zk中注册，zk维护分区到节点的映射关系。其他参与者（如路由层或分区感知的客户端）可以向zk订阅此信息。 一旦分区发生变化或添加、删除节点，zk就会主动通知路由层，使路由信息保持最新状态。

![图-8：使用ZK跟踪分区分配给节点](https://img-blog.csdnimg.cn/8af99f1fa556454497583891e345cd83.png)

如LinkedIn的Espresso使用Helix进行集群管理（底层就是zk），实现如图-8所示的请求路由层。 HBase、Kafka也使用zk跟踪分区分配。MongoDB有类似设计，但它依赖自己的配置服务器（config server)实现和mongos守护进程作为路由层。

Cassandra采取不同方法：他在节点之间，使用gossip协议同步集群状态的变化。请求可发送到任一节点，该节点负责再将其转发到【包含所请求的分区】的目标节点（图-7中的方法1）。该方案增加了DB节点的复杂性，但避免了对zk这样的外部协调服务的强依赖。

Couchbase不支持自动再平衡，这简化了设计。通过配置一个moxi路由选择层，向集群节点学习最新的路由变化。

当使用路由层或向随机节点发送请求时，客户端仍需知道目标节点的 IP 地址。IP地址一般没有分区-节点变化那么频繁，采用DNS通常就够了。

### 5.1 执行并行查询

至此，只关注了读/写入单K的简单查询（对文档分区的二级索引，要求分散/聚集查询）。这也是大多数NoSQL分布式数据存储所支持的访问类型。

但对大规模并行处理（MPP，Massively parallel processing）这种主要用于数据分析的关系型数据库，查询类型方面复杂多了。典型数仓查询包含多个连接，过滤，分组和聚合操作。 MPP查询优化器将复杂查询分解成许多执行阶段和分区，以便在DB集群的不同节点上并行执行。尤其涉及全表扫描的查询，很受益于这种并行执行。

## 总结

本文探讨将大规模数据集划分成更小子集的各种方案。数据量太大，单台机器存储和处理就会成为瓶颈，因此要引入数据分区。

分区的目标：通过多台机器均匀分布数据和查询负载，避免出现热点。这需要选择合适的数据分区方案，在节点添加或删除时，重新动态平衡分区。

主要的分区方案：

* K范围分区

  先对K排序，每个分区只负责一段包含min到max K 范围的的一段K。排序的优势在于能支持高效的范围查询，但若应用程序经常访问某段K，就存在热点风险。这种方案，当分区太大时，通常将分区分成两个子分区，从而动态地再平衡分区。

* hash分区

  将hash函数应用于每个K，每个分区负责一定范围的hash值。这种方案破坏了K的顺序，范围查询效率低下，但能更均匀分配负载。

  采用该方案分区时，一般预创建好足够且固定数量的分区，让每个节点承载多个分区，当添加或删除节点时，将某些分区从一个节点迁移到另一个节点。也可支持动态分区。

两种方案混用也可行，如使用联合K：

- K的一部分来标识分区
- 另一部分记录排序后的顺序

分区和二级索引之间的相互作用。二级索引也需要分区，有如下方案：

* 基于文档分区二级索引（本地索引）。二级索引存储在与K相同的分区中，即写时只需要更新一个分区，但缺点是读二级索引时，需要在所有分区之间执行分散/收集
* 基于词条分区二级索引（全局索引）。基于索引的值而进行的独立分区。二级索引中的条目可能包含来自K的多个分区里的记录。写入时，不得不更新二级索引的多个分区；但读时，可以从单个分区直接快速提取数据

最后，讨论如何将查询请求路由到正确的分区，包括简单的分区负载平衡到复杂的并行查询执行引擎。

理论上，每个分区基本独立运行，这也是为何试图将分区数据库可伸缩到多台机器的原因。但若需跨多个分区，就很复杂了，如若写一个分区成功，但另一个失败，后续会咋样？
