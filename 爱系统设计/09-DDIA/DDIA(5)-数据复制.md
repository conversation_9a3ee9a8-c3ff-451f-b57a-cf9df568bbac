# 复制

[TOC]

复制，通过网络连接的多台机器上保留相同数据的副本。通过数据复制，期望达到目的：

- 数据与用户在地理上更接近，降低访问延迟
- 即使系统的一部分出现故障，系统也能继续工作，提高可用性
- 扩展至多台机器以同时提供数据访问服务，从而提高读吞吐量

本文假设数据集较小，集群的每台机器都能保存数据集的完整副本。放宽该假设，讨论单机无法容纳整个数据集的情况（即必须分片）。

若复制的数据一成不变，则复制就很简单：一次性将数据复制到每个节点。而复制的挑战在于处理那些持续变更的数据。

流行的变更复制算法：

- 主从复制
- 多主节点复制
- 无主节点复制

所有分布式数据库都使用这三种方法之一。

复制需要进行很多权衡：

- 同步复制还是异步复制？
- 如何处理失败副本？

这些通常是数据库中的配置选项，细节因数据库而异，但存在通用原则。

数据库复制自1970s的基本原则至今没无大变化，因为网络的基本约束没有质变。然而开发人员仍假设一个数据库只运行在单点。分布式数据库变为主流只是近年的事。因此，许多人对 最终一致性（eventual consistency）等问题存在许多误解。

## 1 主节点和从节点

副本（replica）：每个存储数据库完整数据集的节点。当存在多副本，不可避免产生问题：如何确保所有数据落在了所有副本？

每次向DB的写操作都需传播到所有副本，否则副本就会不一致。最常见的解决方案是基于主节点的复制（也称 主动/被动或主/从复制），如图-1。

### 1.1 主从复制原理

1. 指定某副本为主副本或称为主节点。当客户端向DB写时，必须将请求发给主副本，主副本会将新数据写入其本地存储
2. 其他副本称为从副本或只读副本，从节点，**热备（hot-standby）**[^i]。每当主节点将新数据写入本地存储后，也会将数据变更发给所有的追随者，称为**复制日志（replication log）** 记录或**变更流（change stream）**。每个从副本获得更改日志之后将其应用到本地，且严格保持与主副本相同的写入顺序
3. 客户端从DB读数据时，可在主节点或从节点上执行查询。 但只有主节点才能接受写请求。客户端角度，从节点都是只读）

[^i]: 不同的对 **热（hot）**，**温（warm）**，**冷（cold）** 备份服务器有不同的定义。 如PostgreSQL中，**热备（hot standby）** 指的是能接受客户端读请求的副本。而 **温备（warm standby）** 只是追随领导者，但不处理客户端的任何查询。 



![图-1 主从复制](https://img-blog.csdnimg.cn/e6bc60a519d54534b5fe0c90b0277cbb.png)

这种复制模式是许多关系数据库的内置功能，如PostgreSQL（9.0版本开始）、MySQL、Oracle Data Guard和SQL Server的AlwaysOn Availability Groups。 而一些非关系数据库如MongoDB、RethinkDB和Espresso也支持主从复制。 主从复制不仅限于DB：像Kafka、RabbitMQ这样的分布式消息代理也使用。

### 1.2 同步复制与异步复制

复制的重要可选项：

- 同步复制，synchronously

- 异步复制，asynchronously

关系型DB 中，这通常是个可配置项，而其他系统通常是硬性指定或只能二选一。

如图-1案例，网站用户更新个人头像图片的流程：

- 客户向主节点发送更新请求
- 主节点收到请求。某刻，主节点又将数据更新转发给从节点
- 最后，主节点通知客户更新完成

图-2显示了系统各模块间通信情况。请求或响应标记为粗箭头。

![图-2 主从复制：包括一个同步的从节点和一个异步的从节点](https://img-blog.csdnimg.cn/4a7c328b4ffe48e3b550614da9f15981.png)

图-2中：

- 从节点1是同步复制：主节点需等待直到从节点确认完成写，然后才通知用户报告完成，井将最新写入对其他客户端可见
- 从节点2异步复制：主节点发送完消息后立即返回，不等待从节点2完成确认

从节点2接收复制日志前存在一段长延迟。复制一般速度很快，大多DB系统能在1s内完成所有从节点更新。但并不保证复制耗时多久。有时，从节点可能落后主节点几min或更久，如从节点正在故障恢复或系统已接近最大设计上限或节点间存在的网络问题。

同步复制的

- 优点

  一旦向用户确认，从节点可明确保证完成和主节点的更新同步，数据已处最新版本。若主节点故障，可确信这些数据仍能在从节点找到。

- 缺点

  若同步的从节点无响应（比如它已崩溃或网络故障等原因），主节点的写操作就不能视为成功。主节点会阻塞后续所有写操作，直到同步副本再次可用确认完成。

因此，将所有从节点都设置为同步复制不切实际：任一同步节点的中断都会导致整个系统更新停滞。实践时，若DB启用同步复制，意味着其中某一从节点是同步的，而其他节点是异步模式。一旦同步的从节点不可用或性能降低，则将另一个异步的从节点提升为同步模式。这就保证至少有2个节点（主节点和一个同步从节点）拥有最新的数据副本。 这种配置有时也称为半同步（semi-synchronous）。

主从复制经常会被配置为全异步模式。 此时若主节点失效且不可恢复，则任何尚未复制到从节点的写请求都会丢失。那么，即使已向客户端确认成功，写入也不能保证数据的持久化。但全异步的优点是：不管从节点数据多么滞后，主节点也能总是继续响应写请求，系统吞吐量极高。

异步模式这种弱化的持久性听起来是个很不靠谱的trade off，但异步复制还是被广泛使用，尤其是从节点数量巨大或分布地理环境较广。

> ### 复制问题研究
>
> 异步复制系统，在主故障时可能丢数据。这是严重问题，因此在保证不丢数据前提下，人们尝试各种方案提高复制性能和系统可用性。 如链式复制，同步复制的一种变体，已在一些系统（如Microsoft Azure存储）实现。
>
> 多副本一致性与共识之间密切联系（即让多个节点对数据状态达成一致）。本文专注DB实践中常用的、相对简单的复制技术。

### 1.3 配置新的从节点

有时需考虑新增一个从节点：

- 提高容错
- 或替换失败的副本节点

#### 1.3.1 【新的从节点】和【主节点】数据一致

将数据文件从一个节点复制到另一个节点还不够。因为客户端仍不断向DB写数据，数据总在变化，常规文件拷贝方式会导致不同节点呈现不同时间点的数据。或许该考虑锁DB（使其不可写），使磁盘文件保持一致，但这又违背高可用设计。

幸好，可做到不停机、数据服务不中断前提下完成从节点设置：

1. 某时刻，获取【主节点】的一个一致性快照，避免长时间的锁DB

   DB都支持该功能，因其为系统备份必需。某些场景，可能需第三方工具，如MySQL的innobackupex

2. 将此快照复制到新【从节点】

3. 【从节点】连接到【主节点】，并请求快照之后发生的binlog

   因为在step1建快照时，快照与系统复制日志的某确定位置关联，该位置信息在不同系统不同叫法，PostgreSQL称其为log sequence number（日志序列号），MySQL称为binlog coordinates

4. 获得日志后，从节点应用这些快照点之后的所有数据变更，即追赶

   接下来，它可继续处理主节点上新的数据变化。并重复1~4

### 1.4 处理节点宕机

系统的任何节点都可能宕机，对运维而言，能在系统不中断服务情况下重启单个节点可太妙了。目标是即使个别节点失效，也能保持系统总体持续运行，并尽可能减小节点宕机影响。

### 1.5 主从复制实现高可用

#### 1.5.1 从节点失效：追赶恢复

【从节点】的本地磁盘都保存了副本收到的数据变更日志。若【从节点】崩溃重启或主、从节点之间网络中断，则比较容易恢复：从节点可从日志知道，在故障之前处理的最后一个事务。因此，从节点能连接到主节点，并请求在从节点断开连接时发生的所有数据变更。

当应用完所有这些变化后，就赶上了【主节点】，并能像以前一样继续接收数据变更流。

#### 1.5.2 主节点失效：故障切换（failover）

1. 选择某【从节点】提升为新的【主节点】
2. 重新配置客户端，以将它们之后的写请求发给新的【主节点】
3. 其他从节点开始接收来自新主节点的变更数据

故障切换可【手动】进行，如：

- 通知运维，【主节点】已宕机，请采取必要步骤创建新的主节点
- 或自动进行

#### 1.5.3 自动切换过程

1. 确认【主节点】失效。多种可能性：系统崩溃、停电或网络问题。没有方法能确切检测到底啥问题，所以大多系统都基于超时机制：节点间频繁互发心跳存活消息，若发现某节点在一段时间内（如30s）无响应，就认为它挂了（因为计划内的维护目的而故意下线主节点的场景不算）

2. 选一个新的【主节点】。可通过选举（剩余节点多数达成共识）或由之前选定的控制器节点（controller node）指定新主节点。最佳候选节点：拥有与原主节点的数据差异最小，以最小化数据丢失风险

3. 重新配置系统，以启用新的主节点

   客户端现在需将写请求发给新主节点。若原主节点重归，可能仍认为自己是主节点，没意识到其他节点已达成共识迫使其下台。这时，系统要确保老领导认可新领导，并降级为一个从节点

#### 1.5.4 故障切换的变数

##### 1.5.4.1 使用异步复制

则新【主节点】可能没收到老【主节点】宕机前的所有数据。选出新【主节点】后，若原主节点重新上线并加入集群，新【主节点】在此期间可能收到冲突的写请求，因为原主节点未意识到角色变化，还会尝试同步其他从节点，但其中的一个现在已接管成为新任主节点了。

常见解决方案：原主节点上未完成复制的写请求就此丢弃，但这可能违背数据更新持久化的承诺。

若DB需和其他外部存储协作，则丢弃写入内容很危险。如GitHub的事故，某个数据并非完全同步的MySQL从节点被提升为主，DB用自增计数器将主键分配给新建的行，但因新【主节点】计数器落后于原【主节点】（ 即二者并非完全同步），它重新使用已被原主节点分配出去的某些主键，而这些主键恰好已被外部Redis所使用，导致MySQL和Redis之间数据不一致，最后一些私有数据被错误地泄露给其他用户。

##### 1.5.4.2 脑裂

有时可能出现两个节点同时以为自己是主节点：两个主节点都可能接受写请求，且无冲突解决机制，最后数据就可能丢失或损坏。

###### 解决方案

当检测到两个主节点同时存在时，会强制关闭其中一个节点[^ii]，但设计粗糙的机制可能导致最后两个节点都被关闭了。

[^ii]:这种机制称为 **屏蔽（fencing）**，充满感情的术语是：**爆彼之头（Shoot The Other Node In The Head, STONITH）**。

得设置合适超时，来检测主节点失效：

主节点失效后，超时时间越长，意味着总体恢复时间也越长。但若超时设置太短，又可能会频繁出现不必要的故障切换，如：

- 临时负载峰值可能导致节点响应时间超时
- 或网络故障可能导致数据包延迟

若系统已是高负载或网络拥塞，则不必要的故障切换可能让情况变得更糟。

这些问题其实都无简易解决方案。因此，即使软件支持自动故障切换，不少运维团队还是更愿意手动执行。



节点故障、不可靠的网络、副本一致性，持久性，可用性和延迟的各种权衡正是分布式系统核心问题。

### 1.6 复制日志的实现

主从复制底层实现方案如何？

#### 1.6.1 基于语句的复制

最简单的，主节点记录下执行的每个写请求（操作语句），并将该操作语句作为日志发给从节点。对于关系型DB，这意味着每个`INSERT`，`UPDATE`或`DELETE`语句都会被转发给每个从节点，且每个从节点都会解析并执行这些SQL语句，就像从客户端收到的一样。

听起来很合理也不难，但很多不适用的场景：

* 任何调用非确定性函数的语句，可能会在每个副本生成不同值

  如`NOW()`获取当前日期时间或`RAND()`获取随机数

* 若语句使用自增列或依赖DB现有数据（如`UPDATE ... WHERE <某些条件>`），则必须在所有副本按完全相同的顺序执行，否则可能会产生不同结果。当有多个并发执行的事务，这会成为很大的限制

* 有副作用的语句（如触发器、存储过程或用户定义函数），可能会在每个副本产生不同副作用

可设法避免这些问题，如主节点可以在记录操作语句时，将非确定性函数替换为执行之后的确定的结果，这样所有节点直接使用相同结果值。但边界条件实在太多，现在首选其他的复制方案。

MySQL 5.1前使用基于语句的复制。因其逻辑紧凑，现在依然在用。但默认，若语句中存在任何不确定性操作，MySQL会切换到基于行的复制。 VoltDB使用基于语句的复制，但要求事务级别的确定性来保证复制的安全。

#### 1.6.2 基于预写日志（WAL）传输

通常，每个写操作都是追加写来写入日志：

* 日志结构存储引擎，日志是主要存储方式。日志段在后台压缩并支持 GC
* 对采用覆写磁盘的B树结构，每次修改会先预写日志（Write Ahead Log，WAL），若系统崩溃，通过索引更新，可迅速恢复到此前的一致状态

不管哪种情况，所有对数据库写入的字节序列都被记入日志。因此，可使用完全相同的日志在另一个节点上构建副本：除了将日志写盘，主节点还能通过网络将其发给从节点。

从节点收到日志进行处理，建立和主节点内容完全相同的数据副本。

PostgreSQL和Oracle等使用这种复制方案。

##### 主要缺点

日志记录的数据非常底层：一个WAL包含哪些磁盘块中的哪些字节发生更改。这使得复制方案和存储引擎紧耦合。若DB存储格式改为新版本，则系统通常无法支持主节点和从节点运行不同版本的数据库软件。

看上去这可能只是个微小实现细节，但对运维产生巨大影响：

- 若复制协议允许从节点使用比主节点更新的软件版本，则可实现先升级从节点，然后执行主节点切换，使升级后的节点成为新主，从而实现数据库软件的不停机升级
- 若复制协议不允许版本不匹配（传输WAL经常出现这种情况），则此类升级需要停机

#### 1.6.3 基于行的逻辑日志复制
另一种方法，复制和存储引擎使用不同的日志格式，这样复制日志就能从存储引擎内部分离。这种复制日志称为逻辑日志，以区分存储引擎的数据表示。

关系数据库的逻辑日志通常是指一系列以行的粒度描述对数据库表的写入的记录序列：

* 插入的行，日志包含所有相关列的新值
* 删除的行，日志有足够信息唯一标识已删除的行。通常靠主键，但若表上未定义主键，则需记录所有列的旧值
* 更新的行，日志有足够信息唯一标识更新的行，及所有列的新值（或至少包含所有已更新列的新值）

涉及多行修改的事务，会生成多个这样的日志记录，后面跟着一条记录指出事务已提交。 MySQL的二进制日志binlog（配置为行复制）就使用该方法。

由于逻辑日志和存储引擎逻辑解耦，因此更容易保持向后兼容，从而使领导者和跟随者能够运行不同版本的数据库软件甚至不同的存储引擎。

对外部应用程序，逻辑日志格式也更易解析。若要将DB内容发到外部系统，如：

- 复制到数仓进行离线分析
- 或构建自定义索引和缓存

这就是数据变更捕获技术。

#### 1.6.4 基于触发器的复制
之前复制方法由DB系统实现，不涉及应用程序。这通常也是我们期望的，但有时，需更高灵活性，如只想：

- 复制数据的一个子集
- 或想从一种DB复制到另一种
- 或需订制、管理冲突解决逻辑

则需将复制控制移交给应用程序：

- 一些工具，如Oracle GoldenGate，可通过读取DB日志，使应用程序获取数据变更
- 另一种方法，使用许多关系DB自带功能：触发器和存储过程

触发器支持注册自己的应用层代码，使得当DB系统数据更改（写事务）时，自动执行这段自定义代码。触发器支持注册在DB系统中发生数据更改（写入事务）时自动执行的自定义应用程序代码。通过触发器，可将数据更改记录到一个单独表，然后外部处理逻辑访问该表，实施必要的自定义应用层逻辑，如将数据更改复制到另一个系统。

基于触发器的复制通常比其他复制方法开销更大，且比DB内置复制更易出错，很多限制。然而由于其灵活性，仍在用。

## 2 复制延迟的案例

容忍节点故障只是使用复制的一个原因。其它原因包括：

- 可扩展性，采用多节点处理更多请求
- 低延迟，让副本在地理位置上更接近用户

主从复制要求所有写请求都主节点处理，从节点只能处理。读多写少场景，这是不错的选择：创建多个从节点，将读请求分散到所有的从节点，从而减轻主节点的负载，并允许向最近的副本发送读请求。

这种可伸缩结构下，只需添加更多从节点，就能提高读请求的服务吞吐量。但这只适于异步复制，若试图同步复制到所有从节点，则单节点故障或网络中断将使整个系统无法写入。且节点越多，故障概率越高，所以完全同步的配置很不可靠。

### 2.1 最终一致性

若应用正好从一个异步的从节点读取时，而该从节点落后于主节点，它可能会看到过期数据，导致数据库中不一致：由于并非所有写入都反映在从节点，若同时对主、从节点发起相同查询，可能得到不同结果。这种不一致只是暂时的状态，若停止写DB，并等待一段时间，从节点最终会赶上并与主节点保持一致。不只有NoSQL数据库是最终一致的：关系型数据库中的异步复制追随者也有相同的特性。

“最终”一词故意含糊不清，理论上，副本落后的程度无限制。正常操作中，主节点和从节点上完成写操作之间的时间延迟（复制滞后）可能不足1s，这样的滞后，在实践中通常不会导致太大影响。但若系统在接近极限情况下运行或网络存在问题，延迟可轻松超过几秒甚至几分钟。

### 2.2 读己之写
许多应用让用户提交一些数据，然后查看提交内容。如客户DB中的记录或某主题的评论。提交新数据须发到主节点，但用户读数据时，可能从【从节点】读。这适用于读密集和偶尔写负载。

但异步复制有问题，如图-3：若用户在写后马上查看数据，则新数据可能尚未到达副本。用户就感觉好像是刚提交的数据丢了，就不高兴。
![图-3：用户发起写请求，然后从滞后的副本中读。此时需写后读(read-after-write)一致性来避免这种异常](https://img-blog.csdnimg.cn/15991735c0ed492e8f956162289d5e2d.png)

此时，需“写后读一致性（read-after-write consistency）”，也称读写一致性（read-your-writes consistency）。该机制保证：若用户重新加载页面，他们总能看到自己最近提交的更新。但对其他用户无任何保证，这些用户的更新可能会在稍后才能刷新看到。

#### 主从复制实现 写后读一致性

若用户访问：

- 可能会被修改的内容，读主

- 否则，读从

这要求查询前，就得考虑内容是否会被修改。如社交网络上的用户个人资料信息通常只由本人编辑。简单的规则：

- 【主节点】读自己的档案
- 【从节点】读其他用户的档案

若应用大部分内容都可能被用户编辑，则上面方案就没啥用，因为大部分内容都读主，导致丧失读扩展性。就得考虑其他标准决定是否读主。如跟踪最近更新时间，若更新后1min 内，则总是读主节点。并监控从节点的复制延迟程度，避免对任意比主节点滞后超过1min的从节点发出查询。

客户端还可记住最近更新时的时间戳，并附带在读请求，据此，系统可确保对该用户提供读服务时，都应该至少包含该时间戳的更新。若当前从节点不够新，可读另一个从节点或一直等待从节点直到收到最近的更新。时间戳可以是逻辑时间戳（指示写入顺序的日志序列号）或实际系统时钟。

若副本分布在多IDC（如考虑与用户的地理接近及高可用性），更复杂。须先把请求路由到主节点的IDC（该IDC可能离用户很远）。

若同一用户从多个设备请求服务，如桌面浏览器和移动APP，就更复杂了。这时，可能就需提供跨设备的写后读一致性，即若用户在某设备输入一些信息，然后在另一个设备查看，则应该看到刚输入的信息。此时，还需考虑：

* 记住用户上次更新时间戳的方法实现更困难，因为一台设备上运行的程序不知道另一台设备上发生啥。元数据需要一个中心存储，做到全局共享
* 若副本分布在多IDC，无法保证来自不同设备的连接会路由到同一IDC。如用户台式计算机使用家庭宽带连接，而移动设备使用蜂窝数据网络，则设备的网络路线可能完全不同。若方案要求必须读主，则首先要确保来自不同设备的请求路由到同一IDC。

### 2.3 单调读
前面异步复制读异常的第二个案例，出现用户数据向后回滚的怪状。

若用户从不同【从节点】多次读取，就可能这样。如图-4显示用户2345两次进行相同查询：

- 首先查询延迟很小的从节点
- 然后是延迟较大的从节点

若用户刷新网页，而每个请求被路由到一个随机的服务器，这种情况是很有可能的。

第一个查询返回最近由用户1234添加的评论，但第二个查询不返回任何东西，因为滞后的从节点还没有拉取写入的内容。效果上相比第一个查询，第二个查询是在更早的时间点来观察系统。

- 若第一个查询未返回任何内容，则问题不大，因为用户2345可能不知道用户1234最近添加了评论
- 但若用户2345先看见用户1234的评论，然后又看到它消失，则对用户2345，就会感觉头大

![图-4 用户首先从新副本读取，然后从旧副本读取。时光倒流。为了防止这种异常，我们需要单调的读取](https://img-blog.csdnimg.cn/3cf2804265e04906808ade9bd65a68b4.png)

单调读保证这种异常不会发生。这是比强一致性（strong consistency）弱，但比最终一致性强的保证。当读取数据时，可能会看到一个旧值；单调读取仅意味着若一个用户顺序多次读取，则不会看到时间后退，即若先前读取到较新的数据，后续读取不会得到更旧数据。

实现单调读取的一种方案：确保每个用户总从同一副本读取（不同用户可读不同副本）。如基于用户ID散列选择副本，而非随机选择副本。但若该副本失败，用户的查询将需重新路由到另一个副本。

### 2.4 一致前缀读

该案例违反因果律。 想象先生和夫人之间的对话：

> Mr
> Mrs，你能看到多远未来？
>
> Mrs
> 通常约10s，Mr. 

这两句之间有因果关系：夫人听到先生的问题并回答该问题。想象第三者老王在通过从节点听对话。 夫人说的内容是从一个延迟很低的从节点读取，但先生所说的内容，从节点的延迟要大的多，如图-5，于是该观察者会听到：

> Mrs
> 通常约十秒钟，Mr 
>
> Mr
> Mrs，你能看到多远未来？

对观察者来说，看起来好像夫人在先生发问前就回答了问题。

![图-5 若某些分区的复制速度慢于其他分区，则观察者在看到问题前，可能会看到答案](https://img-blog.csdnimg.cn/aeb67811f9d74147811593d58a119781.png)

防止这种异常，需要新类型的保证：一致前缀读（consistent prefix reads），若一系列写入按某个顺序发生，那么任何人读取这些写入时，也会看见它们以同样的顺序出现。

这是分片数据库中的特殊问题。若数据库总以相同顺序写入，则读总会看到一致的序列，不会发生这种异常。许多分布式数据库中，不同分片独立运行，因此不存在全局写入顺序。这就导致，当用户从DB读数据时，可能会看到DB某些部分处于较旧状态，某些处于较新状态。

##### 解决方案

确保任何具有因果顺序关系的写人都交给一个分片来完成，但该方案实际的实现效率大打折扣 。

### 2.5 复制延迟的解决方案

使用最终一致的系统时，若复制延迟增加到几min甚至几h，应用层的行为会是啥样？若“没问题”，那很好。但若结果对用户体验不好，则设计系统，就得考虑提供一个更强的保证是很重要的，例如**写后读**。明明是异步复制却假设复制是同步的，这是很多麻烦的根源。

如前所述，应用程序可以提供比底层数据库更强有力的保证，例如通过主节点进行某种读取。但在应用程序代码中处理这些问题是复杂的，容易出错。

若SE不必担心这么多底层的复制问题，而是假定数据库在“做正确的事情”，情况就简单多了。这就是事务存在的原因，事务是数据库提供更强保证的一种方式。

单节点事务技术已成熟。然而在分布式（即支持复制和分区）数据库时，许多系统却选择放弃事务，声称事务在性能和可用性上的代价过高，并断言在可扩展的分布式系统中最终一致性是无法避免的终极选择。有一定道理，但没这么简单，后文将深入讨论。

## 3 多主复制

之前都是单主的主从复制架构，主从复制有个明显缺点：只有一个主节点，而所有写都必须通过它[^iv]。万一和主节点之间的网络中断而导致无法连接到主节点，主从复制方案就影响所有DB写入操作。

[^iv]: 若DB还采用分区，不同分区可能将主副本放在不同节点，但对给定的某分区，则只有一个主节点

对主从复制模型进行扩展，则可配置多个主节点，每个主节点都能处理写，后面复制的流程类似：处理写的每个【主节点】都必须将该数据更改转发到所有其他节点 。这就是多主节点（也称为主-主，或主动/主动）复制。 此时，每个主节点还同时扮演其他主节点的从节点。

### 3.1 适用场景

在一个IDC内部使用多个主节点没啥大意义，因复杂性远超带来的好处。 但某些case，多活配置也合理：

#### 3.1.1 多IDC

为容忍整个IDC级别故障或更接近用户，可将DB的副本横跨多个IDC。而若使用常规的主从复制模型，主节点必须位于其中一个IDC，且所有写请求都须经过该IDC。

有了多主节点复制模型，则能在每个IDC都配置主节点，如图-6所示基本架构：

- 在每个IDC内，采用主从复制
- IDC之间，由各个IDC的主节点负责和其它IDC的主节点进行数据交换、更新

![图-6 跨多个数据中心的多主复制](https://img-blog.csdnimg.cn/a331f2cbc0034bc3bf3c750448bf798a.png)

比较多数据中心时，单主和多主：

***性能***

- 单活，每个写入须穿过互联网，进入主节点数据中心。这可能会大大增加写延迟，并违背设置多数据中心的初心（就近访问）
- 多活，每个写操作都能在本地IDC快速响应，然后采用异步复制将变化同步到其它IDC。因此，对上层应用有效屏蔽了IDC之间的网络延迟，使得终端用户所体验到的性能更好

***容忍数据中心停机***

主从复制下，若M所在IDC故障，必须切换至另一个IDC，将其中的1个从节点提升为M。多主模型中，每个IDC则可独立于其他IDC继续运行，发生故障的数据中心在恢复之后更新到最新状态。

***容忍网络问题***

IDC之间的通信通常经由广域网，大多不如IDC内的本地网络可靠。单主配置对这数据中心间的连接问题非常敏感，因为通过这个连接进行的写操作是同步的。采用异步复制功能的多活配置通常能更好地承受网络问题：临时的网络中断并不会妨碍正在处理的写入。

有些数据库默认情况下支持多主配置，但使用外部工具实现也很常见，如MySQL的Tungsten Replicator。

尽管多主复制有这些优势，但也有一个很大的缺点：两个不同IDC可能会同时修改相同的数据，写冲突必须解决（图-6中conflict resolution）。

由于多主复制在许多数据库中只是新增功能，所以还存在微妙的配置缺陷，与其他数据库功能如自增主键、触发器、完整性约束之间，交互有时会出现意外。因此，很多人觉得多主复制比较危险，应尽可能避免。

#### 3.1.2 需离线操作的客户端

应用在断网后仍需继续工作。

如手机、PC和其他设备上的日历应用。无论设备当前是否连网，都需随时查看：

- 当前会议日程（读请求）
- 添加新会议（写请求）

离线状态下进行的任何更改，会在设备下次上线时，与服务器和其他设备同步。

此时，每个设备都有一个充当M的本地DB（接受写请求），并在所有设备之间采用异步方式同步这些多M上的副本，同步滞后可能是几h或数天，具体时间取决于设备何时再联网。

架构上，这种设置类似IDC之间的多主复制，只不过每个设备都是个“IDC”，而它们之间的网络连接极不可靠。从日历同步功能的这些破烂实现也能看出，多主可以得到结果，但中间依旧很多未知数。

有一些工具就是为了使多主配置更容易，如CouchDB。

#### 3.1.3 协作编辑

实时协作编辑应用程序允许多人同时编辑文档。如Google Docs。通常不会将协作式编辑完全等价于数据库复制问题，但与前面提到的离线编辑案例类似。

当一个用户编辑文档时，所做更改将立即应用到本地副本（Web浏览器或客户端应用程序中的文档状态），并异步复制到服务器和编辑同一文档的任何其他用户。

若要保证不发生编辑冲突，则应用程序必须先锁定文档，然后才能编辑。若另一用户想编辑同一文档，必须等到第一个用户提交修改并释放锁。这种协作模式类似主从复制模型下在主节点执行事务。

为加速协作效率，期望将可编辑粒度设置很小，如一个按键甚至全程无锁。但同时也带来多主复制都有的挑战：解决冲突。

### 3.2 处理写冲突

多主复制的最大问题：可能发生写冲突，必须要解决。

如两个用户同时编辑wiki，如图-7：

- User 1将页面标题从A-》B
- 且User 2同时将标题从A-》C

每个用户的更改都成功提交到本地主节点。但异步复制到对方时，发现存在冲突。正常的主从复制则无此问题。

![图-7：两主节点同时更新同一记录导致写冲突](https://img-blog.csdnimg.cn/95d3a6cf84c2473fb8cddff4d2467d01.png)

#### 3.2.1 同步、异步冲突检测

若为主从复制DB，第二个写请求将：

- 被阻塞直到第一个写完成
- 或被中止，强制用户必须重试

而多主节点复制时，这两个写都成功，且只能在稍后时间点才能异步检测到冲突，那时再要求用户解决冲突，为时已晚！

理论上能做到同步冲突检测：等待写请求完成对所有副本的同步，再通知用户写成功。但这就失去多主的优点：允许每个主节点独立接受写请求。所以，若确需同步冲突检测，应考虑使用单主节点的主从复制！

#### 3.2.2 避免冲突

处理冲突的最理想策略：避免它们。若应用层能保证对特定记录的所有写请求都通过同一主节点，那就不存在冲突。实践中，由于很多主节点复制模型所实现的冲突解决方案很辣鸡，因此直接避免冲突才是推荐方案。

若用户需编辑自己的数据，可确保特定用户的请求始终路由到特定IDC，并使用该IDC的主节点读、写。不同用户可能对应不同主IDC（如CDN），但用户角度，这基本等价于主从复制模型。

但有时可能需更改事先指定的主节点，可能因为：

- IDC故障，需将流量重新路由到另一个IDC
- 或用户已漫游到另一个位置，接近了不同IDC

此时，避免冲突的方案失效，必须要有方案应对不同主节点同时写入的可能。

#### 3.2.3 收敛至一致状态

主从复制模型的数据更新符合顺序性原则：若同一字段有多个更新，则最后一个写操作决定该字段的终值。

而多主复制模型不存在这样的写入顺序，所以终值不确定。图-7中：

- 主节点1中title，先更新为B，再更新为C
- 主节点2中，先更新为C，再更新为B

二者无法辨识谁“更正确”。

若每个副本都按其看到写入的顺序执行，则DB最终将处于不一致状态，如主1看到终值C，而主2看到B。这是不可接受的，所有复制模型至少须确保数据在所有副本中的最终状态一致。因此，DB必须以一种收敛方式解决冲突，即所有副本必须在所有变更复制完成时，所有副本的终值相同。

##### 3.2.3.1 收敛冲突解决方案

* 给每个写入一个唯一ID（如一个时间戳，一个长随机数，一个UUID或一个哈希），挑选最高ID的写入作为胜利者，并丢弃其他写入。若基于时间戳，这种技术称为最后写入胜利（LWW, last write wins）。虽然这种方法很流行，但很容易造成数据丢失。后文再详细讨论。
* 为每个副本分配一个唯一ID并制定规则，如ID编号更高的副本写入始终具有更高优先级。不过也可能数据丢失
* 某种方式将这些值合并，如按字母排序，然后连接（图-7，合并的标题可能类似“B/C”）
* 利用预定义好的格式记录和保留冲突相关的所有信息，然后依靠应用层逻辑，事后解决冲突 （可能会提示用户）

#### 3.2.4 自定义冲突解决逻辑

解决冲突最合适的可能还得仰仗应用层，所以多主节点复制模型都有工具，允许应用代码解决冲突，可在写或读时执行这些代码逻辑：

##### ① 写时执行

只要DB系统检测到复制变更日志时存在冲突，就调用冲突处理程序。如Bucardo允许编写一段Perl代码。该处理程序通常不能在线提示用户，只能在后台进程运行

##### ② 读时执行

检测到冲突时，所有冲突写入值都会被暂存。下次读时，会将数据的多版本返回给应用层。应用可能会提示用户或自动解决冲突，并将最后结果写回DB。如CouchDB。



冲突解决通常适用于单行或文档，而非整个事务。因此，若有一个原子事务包含多个不同写请求，每个写请求仍需分开考虑来解决冲突。

#### 3.2.5 什么是冲突？

有些冲突显而易见，如图-7的两个写操作并发修改同一条记录中的同一字段，并设为两个不同值。其他类型的冲突可能就微妙了。

如会议室预订系统，记录谁订了哪个时间段的哪个房间。应用需确保每个房间只有一组人同时预定（禁止相同房间的重复预订）。此时，若同时为同一房间创建两个不同预订，就冲突了。尽管应用在预订时会检查房间可用性，但若两次预订由两个不同主节点进行，则还是可能冲突。

#### 3.2.6 自动冲突解决

冲突解决规则可能会愈来愈复杂，且自定义代码易出错。亚马逊就是经典反例：有段时间，购物车上的冲突解决逻辑依靠用户的购物车页面（保存了所有的物品），但顾客有时发现之前已被拿掉的商品，再次出现在他们的购物车。

一些有趣的研究尝试自动解决由于数据并发修改引起的冲突：

- 无冲突复制数据类型（Conflict-free replicated datatypes，CRDT）
  可由多个用户同时编辑的集合，映射，有序列表，计数器等数据结构，它们以合理方式自动解决冲突。一些CRDT已在Riak 2.0实现

- 可合并的持久数据结构（Mergeable persistent data structures）
  显式跟踪历史记录，类似Git，并使用三向合并功能（而CRDT使用双向合并）

- 可执行的转换（operational transformation）

  Etherpad和Google Docs等合作编辑应用背后的冲突解决算法。专为同时编辑项目的有序列表而设计的，例如构成文本文档的字符列表

这些算法在DB中的实现还很年轻，但很可能将来它们将被集成到更多的复制数据系统中。自动冲突解决方案可以使应用程序处理多领导者数据同步更为简单。可惜没有现成答案。

### 3.3 多主复制拓扑

复制的拓扑结构描述了写请求从一个节点传播到另一个节点的通信路径。若有两个主节点，如图-7，只有一个合理拓扑结构：M1必须把他所有的写同步到M2，反之亦然。当有两个以上M，各种不同拓扑都有可能。如图-8：

![图-8：三个能设置多主节点的拓扑结构实例](https://img-blog.csdnimg.cn/35718dc6d332411180b1d22c18df403b.png)

最普遍的拓扑是全部到全部，即图-8 (c)，每个M将其写入给同步到其他所有M。

但也会使用更多受限拓扑：如MySQL仅支持环形拓扑（circular topology），其中每个节点接收来自前一个节点的写入，并将这些写入（加上自己的写入)转发给后序节点。

另一流行结构是星形形状[^v]。一个指定的根节点将写入转发给所有其他节点。星型拓扑可推广到树。

[^v]: 不要与星型模式混淆，其描述了数据模型的结构，而非节点之间的通信拓扑。

#### 3.3.1 环形、星形拓扑

写请求需通过多个节点才能到达所有副本，即中间节点需要转发从其他节点收到的数据更改。为避免无限循环，每个节点需赋予一个唯一标识符，在复制日志中的每个写请求都标记了所有已经过的节点的标识符。当某节点收到用自己的标识符标记的数据更改时，该数据更改将被忽略，避免重复转发。

##### 问题

若某节点故障，则可能会中断其他节点之间的复制消息流，导致它们无法通信，直到节点修复。拓扑结构可重新配置为在发生故障的节点上工作，但大多数部署中，这种重新配置工作须手动。更密集连接的拓扑结构（如全部到全部）的容错性更好，因为它允许消息沿不同路径传播，避免了单点故障。

#### 3.3.2 全部到全部拓扑
也可能有问题。特别当一些网络链接可能比其他网络链接更快（由于网络拥塞），结果一些复制消息可能“超过”其他复制消息，如图-9。

![图-9：多主复制，某些副本可能出现错误的写请求到达顺序](https://img-blog.csdnimg.cn/ce047e02a58c44a0b7395f4990308fcd.png)

- ClientA向L1表插入一行
- ClientB在L3更新该行

然而，L2能以不同顺序接收写入：可先接收更新（从它的角度来看，是对DB中不存在的行执行更新），之后接收L1的插入日志（本该在更新日志之前到达)。

这是个因果关系问题，类似“一致前缀读”中的：更新依赖先前完成的插入，所以需确保所有节点先接收插入，再处理更新。在每次的写日志里添加一个时间戳还不够，主要因为无法确保时钟完全同步，因而无法在L2上正确排序所收到的日志。

为正确【排序日志消息】，可使用版本向量。冲突检测技术在很多主节点复制系统中实现不够完善。如PostgreSQL BDR不提供写入的因果排序，Tungsten Replicator for MySQL甚至不尝试检测冲突。

## 4 无主复制

单、多主复制的思路都是：客户端向某主节点发写请求，而DB系统负责将写请求复制到其他副本。

- 主节点决定写顺序
- 从节点按相同顺序应用主节点发送的写日志

某些数据存储系统另辟蹊径：放弃主节点，允许副本直接接受客户端写。最早的复制数据系统就是无主节点（或称之为去中心复制、无中心复制），但后来在关系DB主导的时代，这想法被忘却。在亚马逊将其用于其内部的Dynamo系统[^vi]后，它再一次成为流行的DB架构。Riak，Cassandra和Voldemort都是由Dynamo启发的无主复制模型的开源数据存储，所以这类DB也被称为Dynamo风格。

[^vi]: Dynamo不适于Amazon以外用户。 令人困惑的，AWS DynamoDB托管数据库产品，使用完全不同的体系结构：它基于单领导者复制。

- 某些无主实现，客户端直接将写请求发到多副本
- 而另一些实现中，有个协调者（coordinator）节点代表客户端进行写入，但与主节点的DB不同，协调者不负责维护写入顺序

### 4.1 节点故障时写DB

假设三副本DB，其中一个副本当前不可用（或许正在重启以安装系统更新）：

- 主节点复制模型下，若要继续处理写，则需执行故障切换
- 而无主模型，则不存在这切换

图-10：User 1234将写请求并行发送到三副本，两可用副本接受写，而那不可用副本无法处理。假设这俩成功确认写，User 1234收到两个确定响应后，即可认为写成功。完全能忽略那一个副本无法写入的情况。

![图-10：法定写入，法定读取，并在节点中断后读修复](https://img-blog.csdnimg.cn/f55286c98d15483d9921b0769653df88.png)

等失效节点重新上线，而客户端开始读取它。节点失效期间发生的任何写入在该节点都尚未同步，因此可能读到过期数据。

为解决该问题，当某客户端从DB读数据时，它不是向1个副本发送请求，而是并行发送到多个副本。客户端可能会从不同节点获得不同响应，即来自一个节点的最新值和来自另一个节点的旧值。可利用版本号确定哪个值是更新的。

#### 4.1.1 读修复和反熵

复制模型应确保所有数据最终复制到所有副本。在一个失效节点重新上线后，如何追上错过的写入？Dynamo风格的数据存储系统常用机制：

##### ① 读修复（Read repair）

当客户端并行读取多副本时，可检测到过期的返回值。如图-10，User 2345获得来自R3的版本6，而从副本1、2得到版本7。客户端判断可知：副本3是过期值，然后将新值写入该副本。这适用于读密集场景。

##### ② 反熵过程（Anti-entropy process）

一些数据存储有后台进程，不断查找副本之间数据差异，将任何缺少的数据从一个副本复制到另一个副本。不同于基于主节点复制的复制日志，该反熵过程不保证任何特定顺序的复制写入，并且会引入明显的同步滞后。

并非所有系统都实现了这俩方案。如Voldemort目前无反熵过程。若无反熵过程，由于【读修复】只在发生读取时才可能执行修复，那些很少访问的数据可能在某些副本中已丢失而无法再检测到，从而降低写的持久性。

#### 4.1.2 读写quorum

图-10中，三副本若有两个以上完成处理，写即可认为成功了。若三副本中只有一个完成写入，会怎样？到底几个副本完成才能认为写成功？

成功写操作，要求三副本的至少两个完成，即至多有一个副本可能包含旧值。因此，在读取时需至少向两个副本发起读请求，通过版本号可确定一定至少有一个包含新值。若第三个副本停机或响应慢，则读取仍可继续并返回最新值。

若有n副本，写入须w个节点确认，至少为每个读取查询r个节点。只要 w + r > n，我们期望在读取时获得最新值，因为r个读取中至少有一个节点最新。遵循这些r值，w值的读写称为法定人数（quorum）读写。也可认为r和w是判定读、写是否有效的最低票数。

Dynamo风格DB的参数n，w和r一般可配置。常见选择是n为奇数（3或5）并设置 $w = r =（n + 1）/ 2$（向上取整）。但可按需更改数字。如设置$w = n$和$r = 1$的写入很少且读取次数较多的工作负载可能会受益。这使得读取速度更快，但具有只有一个失败节点导致所有数据库写入失败的缺点。

> 集群中可能存在多于n的节点。（集群的机器数可能多于副本数目），但任何给定的值只能存储在n个节点上。这允许对数据集进行分区，从而可以支持比单个节点的存储能力更大的数据集。
>

仲裁条件$w + r> n$定义了系统可容忍的失效节点个数：

* 若$w <n$，若节点不可用，仍能处理写入

* 若$r <n$，若节点不可用，仍能处理读取

* 对于$n = 3，w = 2，r = 2$，可容忍一个不可用节点

* 对于$n = 5，w = 3，r = 3$，可容忍两个不可用的节点。 案例如图-11

通常，读、写操作始终并行发送到所有n个副本。 w、r决定我们等待多少个节点，即在我们认为读、写成功前，有多少个节点需报告成功。

![图-11：若$w + r > n$，读取r个副本，至少有一个r副本必然包含了最近的成功写入](https://img-blog.csdnimg.cn/f98c1404a87e4f10af0c748d866aa076.png)

若少于所需的w或r节点可用，则写入或读取将返回错误。节点不可用原因：因执行操作的错误（由于磁盘已满而无法写），因为节点关闭（崩溃，关闭电源)，由于客户端和服务器节点之间的网络中断等。 只关心节点是否返回了成功响应，无需区分出错具体原因。

### 4.2 Quorum一致性的局限性

若有n个副本，且配置w和r，使得$w + r> n$，期望可以读到一个最新值。因为成功写入的节点集合和读取的节点集合必有重合，这样读取的节点中至少有一个具有最新值，如图-11。

一般设定r和w为简单多数（超过n/2）节点，即可确保 w + r> n，且同时容忍多达 n/2 个节点故障。但是，法定人数不一定必须是大多数，只是读写使用的节点交集至少需要包括一个节点。其他法定人数的配置是可能的，这使得分布式算法的设计有一定的灵活性。

你也可以将w和r设置为较小的数字，以使$w + r≤n$（即法定条件不满足）。在这种情况下，读取和写入操作仍将被发送到n个节点，但操作成功只需要少量的成功响应。

较小的w和r更有可能会读取过时的数据，因为你的读取更有可能不包含具有最新值的节点。另一方面，这种配置允许更低的延迟和更高的可用性：如果存在网络中断，并且许多副本变得无法访问，则可以继续处理读取和写入的机会更大。只有当可达副本的数量低于w或r时，数据库才分别变得不可用于写入或读取。

但即使$w + r> n$，也可能返回旧值。这取决于实现，可能的case：

* 若使用宽松的法定人数，w个写入和r个读取落在完全不同节点，因此r节点和w之间不再保证有重叠节点
* 如两个写同时发生，不清楚哪个先。唯一安全解决方案是合并并发写入（参阅处理写入冲突）。如根据时间戳（最后写入胜利）选出胜者，则由于时钟偏差，写入可能丢失
* 如写、读操作同时发生，写操作可能仅反映在某些副本。这时，不确定读取是返回旧值还是新值。
* 如果写操作在某些副本上成功，而在其他节点上失败（例如，因为某些节点上的磁盘已满），在小于w个副本上写入成功。所以整体判定写入失败，但整体写入失败并没有在写入成功的副本上回滚。这意味着如果一个写入虽然报告失败，后续的读取仍然可能会读取这次失败写入的值【47】。
* 如果携带新值的节点失败，需要读取其他带有旧值的副本。并且其数据从带有旧值的副本中恢复，则存储新值的副本数可能会低于w，从而打破法定人数条件。
* 即使一切工作正常，有时也会不幸地出现关于**时序（timing）** 的边缘情况

因此，尽管法定人数似乎保证读取返回最新的写入值，但在实践中并不那么简单。 Dynamo风格的数据库通常针对可以忍受最终一致性的用例进行优化。允许通过参数w和r来调整读取陈旧值的概率，但把它们当成绝对的保证是不明智的。

尤其是，因为通常没有得到“[复制延迟问题](#复制延迟问题)”中讨论的保证（读己之写，单调读，一致前缀读），前面提到的异常可能会发生在应用程序中。更强有力的保证通常需要**事务**或**共识**。我们将在[第七章](ch7.md)和[第九章](ch9.md)回到这些话题。

#### 4.2.1 监控旧值

运维角度，监视DB是否返回最新结果很重要。即使应用能容忍读旧值，也需了解复制的当前运行状况。若明显滞后，就是信号，需排查原因（如网络问题或节点超负荷）。

主从复制系统，DB通常会导出复制滞后的度量标准，可将其集成到监控系统。因为主、从节点的写都遵从相同顺序，而每个节点都维护了复制日志执行的当前偏移量。 通过对比主、从节点当前偏移值，即可衡量该从节点落后主节点程度。

无主复制系统，无固定写入顺序，因而监控也更难。且若数据库只使用读修复（无反熵过程），那么旧值的落后就无上限。例如若一个值很少被访问，则返回的旧值可能很老了！

衡量无主复制数据库的研究，根据参数n，w和r来预测旧值读取的预期百分比。不幸的是，这还不是常见做法，但将旧测量值包含在数据库的度量标准集中是好趋势。最终一致性是很模糊的保证，可操作性角度，能量化“最终”很有价值。

### 4.3 宽松的法定人数与提示移交

合理配置的法定人数可以使数据库无需故障切换即可容忍个别节点的故障。也可以容忍个别节点变慢，因为请求无需等待所有n个节点的响应，只需w或r节点响应即可。对需高可用、低延迟的场景，还能容忍偶尔读旧值，这些特性使无主复制数据库很迷人。

但法定人数不如期待那样有容错性。网络中断很容切断一个客户端到多个数据库节点的连接。尽管这些节点活着，且其他客户端也确实能正常连接，但对断掉连接的客户端，无疑等价于集群整体失效。此时，很可能无住满足最低的w 和 r要求的节点数，因此导致客户端无法满足quorum要求。

在一个大型集群中（节点数量明显多于n个），网络中断期间客户端可能仍能连接到一些数据库节点，但又不足以组成一个特定值的法定人数。在这种情况下，数据库设计人员需要权衡一下：

* 对于所有无法达到w或r节点法定人数的请求，是否返回错误是更好的？
* 或者我们是否应该接受写入，然后将它们写入一些可达的节点，但不在这些值通常所存在的n个节点上？

后者被认为是一个**宽松的法定人数（sloppy quorum）**【37】：写和读仍然需要w和r成功的响应，但这些响应可能来自不在指定的n个“主”节点中的其它节点。比方说，如果你把自己锁在房子外面，你可能会敲开邻居的门，问你是否可以暂时呆在沙发上。

一旦网络中断得到解决，代表另一个节点临时接受的一个节点的任何写入都被发送到适当的“主”节点。这就是所谓的**提示移交（hinted handoff）**。 （一旦你再次找到你的房子的钥匙，你的邻居礼貌地要求你离开沙发回家。）

宽松的法定人数对写入可用性的提高特别有用：只要有任何w节点可用，数据库就可以接受写入。然而，这意味着即使当$w + r> n$时，也不能确定读取某个键的最新值，因为最新的值可能已经临时写入了n之外的某些节点。

因此，在传统意义上，一个宽松的法定人数实际上不是一个法定人数。这只是一个保证，即数据存储在w节点的地方。但不能保证r节点的读取，直到提示移交已经完成。

在所有常见的Dynamo实现中，宽松的法定人数是可选的。Riak默认启用，Cassandra默认禁用。

#### 4.3.1 多IDC操作

无主复制也适用于多IDC操作，因其有更好的容忍并发写冲突、网络中断和延迟尖峰等。

Cassandra在其默认配置的无主模型都支持跨IDC操作：副本数量n包括所有IDC的节点，在配置中，可指定每个IDC中想拥有的副本数量。无论IDC如何，每个客户端写都会发到所有副本，但客户端通常只等待来自其本地IDC内的法定节点确认，从而不会受到跨IDC链路延迟和中断的影响。对其他IDC的高延迟写入通常被配置为异步发生，尽管配置有一定的灵活性。

Riak将客户端和DB节点之间的所有通信保持在一个IDC本地，因此n描述了一个数据中心内的副本数量。数据库集群之间的跨数据中心复制在后台异步发生，其风格类似于多领导者复制。

### 4.4 检测并发写入

Dynamo DB允许多客户端并发写K，即使使用严格quorum机制也可能冲突。类似多主复制，但Dynamo DB在读修复或数据回传期间也可能产生并发写冲突。

由于网络延迟不稳定或局部失效，请求在不同节点可能不同顺序到达。如图-12显示两个客户端A、B同时向K=X写：

* 节点1收到A写，但由于节点失效，没接收到B写
* 节点2先收到A写，然后接收B写
* 节点3先接收B写，然后是A写

![图-12:并发写入Dynamo数据存储：无明确定义的顺序](https://img-blog.csdnimg.cn/0b075e5598a6475580bd510c57e4bb63.png)

若节点每当接收到新写就简单覆盖原有K，则节点将永久不一致，如图-12，节点2认为X最终值B，而其他节点认为最终值A。

副本应收敛于相同值，才能最终一致。有人可能希望副本之间能自动处理，但很不幸，大多实现都很垃圾，若不想丢数据，就得知道很多有关DB内部冲突处理的机制：

#### 4.4.1 最后写入胜利（丢弃并发写入）

实现最终收敛的一种方案：每个副本总存储最新值，允许覆盖并抛弃旧值。假定每个写都最终同步到所有副本，只要确定哪个写最新，则副本就能最终收敛到相同值。

如何定义最新呢？图-12当客户端向DB节点写时，客户端都不知道另一个客户端，因此不清楚哪个先发生。争辩哪个先发生其实没啥意义， 我们说支持写并发，也就意味着它们顺序不确定。

即使无法确定写的“自然顺序”，也能强制任意排序。如为每个写附加一个时间戳，然后选择最新即最大的时间戳，丢弃较早时间戳的写。这就是最后写入胜利（LWW, last write wins），Cassandra唯一支持的冲突解决方法。

LWW实现最终收敛目标，但以牺牲持久性为代价：若同K有多个并发写，即使它们都给客户端通知成功（因为完成了写入w个副本），但最好也只有一个写入能存活，其他的将被静默丢弃。LWW甚至可能删除那些非并发写。

一些场景如缓存系统，覆盖写能接受。若覆盖、丢失数据不可接受，则LWW不是好选择。

要确保LWW安全的唯一方法：只写一次，然后视为不可变，避免对同一K并发更新。如Cassandra推荐使用UUID作为K，这样每个写操作提供唯一K。

#### 4.4.2 Happens-before关系和并发“此前发生”的关系和并发

咋判断两个操作是否并发？

如下图，两个写非并发：A插入先于B增量修改，因为B递增值基于A插入的值。即B操作建立在A基础，所以B后发生。B因果依赖于A

![](https://img-blog.csdnimg.cn/08e3b086c27f4f3c87251bcb9eea4288.png)

如下图中的两个写入是并发：每个客户端启动写操作时，并不知道另一个客户端是否也在执行操作同样的K。因此，操作之间不存在因果关系

![](https://img-blog.csdnimg.cn/1c1e219c5360442ba74c99090fd1d8bf.png)

若B知道A或依赖A或以某种方式基于A构建，则称操作A在操作B之前发生。在另一个操作之前，是否发生一个操作，是定义并发的关键。也可简单说，若两个操作都不在另一个之前发生，则两个操作是并发的（即，两个操作都不知道另一个）【54】。

因此，两个操作AB，有三种可能性：A在B前发生或B在A前发生或AB并发。我们一个算法告诉我们两个操作是否并发：

- 若一个操作先于发生另一个操作，则后面的操作可覆盖较早的操作
- 若这些操作并发，则存在需要解决潜在冲突问题

### 4.5 并发性，时间和相对性
若两个操作同时发生，则称为并发，但事实上，操作是否在时间上重叠并不重要。由于分布式系统复杂的时钟同步问题，现实中很难严格判断两个事件是否同时发生。

为更好定义并发性，并不依赖确切发生时间，即若两个操作并不需要意识到对方，就能说它们是并发操作，而不管它们发生的物理时间。

计算机系统中，即使光速快到允许一个操作影响另一个操作，但两个操作也可能并发。如网络拥塞或中断，可能就会出现两个操作由于网络问题导致一个操作无法感知另一个，因此二者成为并发。

#### 4.4.1 确定前后关系

来看确定两个操作是否为并发的算法。从只有一个副本的数据库开始。

图-13显示两个客户端同时向购物车添加商品。最初，购物车为空。然后两个客户端向DB发出五次写入操作：

1. 客户端1先将牛奶加入购物车。这是第一次写入该 K 的值，服务器成功存储并为其分配版本号1，最后将值与版本号一起返回给客户端1
2. 客户端2将鸡蛋加入购物车，此时不知客户端1已经添加了牛奶（客户端2认为它的鸡蛋是购物车中的唯一物品）。服务器为此写入分配版本号2，并将鸡蛋和牛奶存储为两个单独的值。最后将这两个值都反回给客户端 2 ，并附上版本号2
3. 同理，客户端1也不知道客户端2的写入，想要将面粉加入购物车，因此认为当前购物车应该是[牛奶，面粉]。将此值与服务器先前向客户端1提供的版本号1一起发送到服务器。服务器可从版本号中知道[牛奶，面粉]的新值写入要取代[牛奶]的先前值，但与[鸡蛋]值是并发的。因此，服务器将版本3分配给[牛奶，面粉]，并覆盖版本1的[牛奶]，但同时保留版本2的值[鸡蛋]，将二者的值同时返回给客户端1
4. 同时，客户端 2 想要加入火腿，不知道客端户 1 刚刚加了面粉。客户端 2 在最后一个响应中从服务器收到了两个值[牛奶]和[蛋]，所以客户端 2 现在合并这些值，并添加火腿形成一个新的值，[鸡蛋，牛奶，火腿]。它将这个值发送到服务器，带着之前的版本号 2 。服务器检测到新值会覆盖版本 2 [鸡蛋]，但新值也会与版本 3 [牛奶，面粉]**并发**，所以剩下的两个是v3 [牛奶，面粉]，和v4：[鸡蛋，牛奶，火腿]
5. 最后，客户端 1 想要加培根。它以前在v3中从服务器接收[牛奶，面粉]和[鸡蛋]，所以它合并这些，添加培根，并将最终值[牛奶，面粉，鸡蛋，培根]连同版本号v3发往服务器。这会覆盖v3[牛奶，面粉]（请注意[鸡蛋]已经在最后一步被覆盖），但与v4[鸡蛋，牛奶，火腿]并发，所以服务器保留这两个并发值

![图-13：捕获两个客户端之间的因果关系，同时编辑购物车](https://img-blog.csdnimg.cn/32ffaa7d3e794fe68fe026a370bdbddd.png)

图-13操作之间的数据流如图-14。 箭头表示哪个操作先于发生其他操作，即后面操作知道或依赖前面的操作。 该例中，客户端永远不会完全掌握服务器上的数据，因为总有另一个操作同时进行。 但新版本值最终会覆盖旧值，且不会发生已写入值的丢失。

![图-14 图-13中的因果依赖关系图](https://img-blog.csdnimg.cn/2522081570954d25a480fe9bdf1bec98.png)

服务器判断操作是否并发的依据主要依靠对比版本号，无需解释该新旧值本身（值能是任何数据结构）。算法工作流程：

* 服务器为每个K保留一个版本号，每次K新值写入时递增版本号，并将新版本号与写入的值一起保存
* 当客户端读取K时，服务器将返回所有（未覆盖的值）当前值及最新版本号。且要求写之前，客户端须先发送读请求
* 客户端写K时，写请求必须包含之前读的版本号、之前读取的所有值合并后的集合。 写请求的响应可以像读请求一样，返回所有当前值，这样就能像购物车示例那样将多个写入串联起来。）
* 当服务器接收到待有特定版本号的写入时，覆盖版本号或更低版本的所有值（因为知道这些值已被合并到新传入的值集合中），但必须保存更高版本号的所有值（因为这些值与当前的写是并发）

当写请求包含前一次读取的版本号时，意味着修改的是基于之前的状态。若一个写请求不包含版本号，他讲和所有其他写操作并发，不会覆盖任何已有值，其传入的值将包含在后续读请求的返回值列表当中。

#### 4.4.2 合并同时写入的值

该算法可确保不会发生数据丢弃，但客户端要做额外工作：若多个操作并发，则客户端必须通过合并并发写入的值来继承旧值。 

合并本质和多节点复制中的冲突解决类似，即处理写冲突。一个简单方案：基于版本号或时间戳（即最后写入胜利）选择一个值，但这意味着会丢失数据。所以，需要在应用程序代码中做额外工作。

如购物车，合理的合并并发值是包含新值和旧值。在图-14中，两个客户端最后的值是[牛奶，面粉，鸡蛋，熏肉]和[鸡蛋，牛奶，火腿]。虽然牛奶、鸡蛋在两个客户端都出现了，虽然只写入了一次。合并最终值应该是[牛奶，面粉，鸡蛋，培根，火腿]，其中去掉了重复值。

设想人们也可从他们的购物车删除商品，此时把并发值都合并起来可能导致错误结果：若合并两个客户端的值，且其中有一个商品被某客户端删掉，则被删除的项目会再次出现在合并的最终值。为防止该问题，项目在删除时不能简单从DB删除，系统须保留一个对应版本号以恰当的标记该项目需要在合并时被删除。这种删除标记称为墓碑（逻辑删除）。

考虑到应用程序代码中合并非常复杂且易出错，可设计一些数据结构自动执行合并。

#### 4.4.3 版本向量

图-13示例只有一个副本。若存在多个副本但无主节点，算法该如何修改？

图-13使用单个版本号来捕获操作之间的依赖关系，当多个副本同时接受写入时，这不够。因此，需要为每个K、每个副本都定义一个版本号。每个副本在处理写入时，增加自身版本号，并跟踪从其他副本中看到的版本号。通过这些信息指示要覆盖哪些值、保留哪些并发值。

所有副本的版本号集称为版本向量。与图-13版本号一样，当读数据时，版本向量会从数据库副本发送到客户端，随后写入值时需将版本信息包含在请求中一起发送回数据库。版本向量使得DB 可以区分应该覆盖写还是保留并发值。

就像单副本，应用程序仍需执行合并操作。版本向量可确保从某副本读取，随后写入到另一个副本。这些值可能导致在其他副本上衍生出新的兄弟值，但至少不会丢失数据且能正确合并所有并发值。

> #### 版本向量和向量时钟
>
> 版本向量有时也称为矢量时钟，但不完全相同，简而言之，需要比较副本状态时，应使用版本向量。
>

## 5 总结

复制或多副本技术的目的：

- 高可用

  即使某台机器（或多台机器，或整个IDC）故障，系统也能保持正常运行

- 连接断开与容错

  允许应用程序在网络中断时继续工作

- 低延迟

  将数据放置在距离用户较近地，以更快交互

- 可扩展性
采用多副本，大幅提高系统的读吞吐量

多台机器保留多份相同的数据副本，需仔细考虑并发和所有可能出错并处理。至少，需处理好：

- 节点不可用
- 网络中断

这里甚至不考虑更隐蔽的失效场景，如由于bug导致的无提示的数据损坏。

多副本方案：

- 主从复制

  所有客户端将写都发到单主节点，该节点将数据更改事件发送到其他副本（从节点）。每个副本都能接收读请求，但内容可能过期

- 多主复制
客户端发送每个写入到几个领导节点之一，任一都能接受写。领导者将数据更改事件流发送给彼此及所有跟随者节点
- 无主复制
客户端发送每个写到几个节点，并从多个节点并行读取，以检测和纠正具有陈旧数据的节点

每种方法都有优、缺点。单主复制很流行，因为易理解，无需担心冲突。出现故障节点，网络中断和延迟峰值时，多领导者、无领导者复制更稳健，但以更难推理并仅提供非常弱的一致性保证为代价。

复制可同步、异步，这在故障时对系统有深远影响。尽管系统平稳时异步复制很快，但复制滞后增加和服务器故障时要弄清楚会发生啥。若某领导者失败，且你提升了一个异步更新的追随者成为新的领导者，则最近提交的数据可能丢失。

一些可能由复制滞后引起的奇怪效应，也讨论了一些有助于决定应用程序在复制滞后时的行为的一致性模型：

- 写后读
用户应总看到自己提交的数据。
- 单调读
用户在某时间点看到数据后，不该在某更早时间点看到数据。
- 一致前缀读
用户应将数据视为具有因果意义的状态：如按正确顺序查看问题及其答复。



最后讨论多领导者、无领导者复制固有并发问题：因为他们允许多个写并发，这可能冲突。我们研究了一个DB可能使用的算法来确定：

- 一个操作是否发生在另一个操作之前
- 或它们是否同时发生

通过合并并发更新来解决冲突。
