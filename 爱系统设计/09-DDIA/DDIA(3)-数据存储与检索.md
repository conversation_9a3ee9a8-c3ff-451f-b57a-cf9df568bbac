从最基本层面看，数据库只需做两件事：

- 向它插入数据肘，它就保存数据
- 之后查询时，返回那些数据

本文讨论如何存储输入的数据，并在收到查询请求时，如何重新找到数据。

为何关注数据库内部的存储和检索呢？你不可能从头开始实现存储引擎，往往需要从众多现有的存储引擎中选择适合业务的存储引擎。 为针对特定工作负载而对数据库调优时，就得对存储引擎底层机制有所了解。

针对事务型工作负载、分析型负载的存储引擎优化存在很大差异。

事务处理与分析处理”和“面向列的存储”部分，分析型优化的存储引擎。

先讨论存储引擎，用于大家比较熟悉的两种数据库，传统关系数据库和NoSQL数据库。研究两个存储引擎家族 ，即日志结构的存储引擎和面向页的存储引擎，比如B-tree。

## 数据库核心：数据结构

最简单的数据库，由两个Bash函数实现：

```bash
#!/bin/bash

db_set() { 
	echo "$1,$2" >> database
}

db_get() {
	grep "^$1," database | sed -e "s／^$1,//" tail -n 1
}
```

这两个函数实现一个K.V存储。当调用 db_set key value，它将在数据保存你所输入的key  value key value几乎可以是任何内容。例如值可以是JSON文档。然后调用db_get key，它会查找与输入key相关联的最新值并返回。

例如：

```bash
$ db_set 123456 '{"name":"London", "attractions":["Big Ben","London Eye"]}'

$ db_set 42 '{"name":"San Francisco","attractions":["Golden Gate Bridge"]}'

$ db_get 42 {"name":"San Francisco", "attractions":["Golden Gate Bridge"]}
```

它底层的存储格式其实非常简单：一个纯文本文件。其中每行包含一个K.V，用逗号分隔（大致像一个csv文件，忽略转义问题）。每次调用db_set，即追加新内容到文件末尾，因此，若多次更新某K，旧版本值不会被覆盖，而需查看文件中最后一次出现的K来找到最新V（因此在db_get中使用tail -n 1）。

```bash
$ db_set 42 '{"name":"San Francisco", "attractions":["Exploratorium"]}'

$ db_get 42 {"name":"San Francisco", "attractions":["Exploratorium"]}

$ cat database
123456,{"name":"London","attractions":["Big Ben","London Eye"]} 42, {"name":"San Francisco","attractions":["Golden Gate Bridge"]}
42,{"name":"San Francisco","attractions":["Exploratorium"]}
```

简单情况，追加到文件尾部方式通常够高效了，因而db_set函数性能很好。类似db_set，许多数据库内部都使用日志（log），日志是个仅支持追加式更新的数据文件。虽然真正的数据库有很多更复杂问题要考虑（如并发控制、回收磁盘空间以控制日志文件大小、处理错误和部分完成写记录等），但基本原理相同 。

日志这个词通常指应用程序的运行输出日志，来记录发生了什么事情 。日志则是个更通用的含义，表示一个仅能追加的记录序列集合。它可能是人类不可读的，可能是二进制格式而只能被其他程序来读取。

另一方面，若日志文件保存大量记录，则db_get性能会很差。每次想查找一个K，db_get 必须从头到尾扫描整个数据库文件来查找K的出现位置。在算法术语中，查找开销是O（n） ，即若数据库记录条数加倍，则查找需要两倍时间。这一点并不好。

为高效查找数据库中特定K的V，需要数据结构：索引。它们背后基本想法都是保留一些额外元数据，这些元数据作为路标，帮助定位想要数据。若希望用几种不同方式搜索相同数据，在数据的不同部分，可能要定义多种不同的索引。

索引是基于原始数据派生而来的额外数据结构。很多数据库允许单独添加、删除索引，而不影响数据库内容，它只会影响查询性能。维护额外结构势必会引入开销，特别是在新数据写入时。对于写人，它很难超过简单追加文件方式的性能，因为那已经是最简单的写操作。由于每次写数据时，需更新索引，所以任何类型索引基本都会降低写速度。

这就是

### 存储系统中重要的trade off

合适的索引能加速查询，但每个索引都会降低写速度。默认情况下，数据库通常不会对所有内容索引 ，它需要SE或DBA基于对应用程序典型查询模式的了解，手动决定索引。就是为应用提供最有利加速同时，避免引入过多不必要的开销。

## 哈希索引

以KV数据的索引开始。KV类型并非唯一能索引的数据，但随处可见，而且是其他更复杂索引的基础。

KV存储与大多数编程语言所内置的字典结构类似，一般采用hash map或hash table实现。既然已有内存数据结构的hash map ，为何不用它们在磁盘上直接索引数据？

假设数据存储全部采用追加式文件组成，最简单的索引策略：保存内存中的hash map，将每个K一一映射到数据文件中特定的字节偏移量，这就能找到每个值的位置：

![图1](https://img-blog.csdnimg.cn/2b626cda58234c778d65cd745783d2e8.png)

每当在文件中追加新的KV对时，还要更新hashma来反映刚写入的数据的偏移量（包括插入新K与更新已有K）。当查找一个值时，使用hashmap找到文件中的偏移量，即存储位置，然后读取其内容。

听着简单，但的确可行。Bitcask（Riak默认的存储引擎）就是这么做的。 Bitcask提供高性能读写，但所有K必须能放入内存。而V则可以使用比可用内存更多的空间，只需一次磁盘寻址，就能将V从磁盘加载到内存，若那部分数据文件已在文件系统的缓存中，则读取根本不需要任何磁盘I/O。

Bitcask这种存储引擎适合每个K的V经常更新场景。如：

- K，视频的URL
- V，播放次数（每次有人点击播放按钮时递增）

在这种工作负载下，有大量写操作，但没有太多不同的K，即每个K都有大量写操作，但将所有K保存在内存中还是可行的。

至此，只追加写到一个文件，那如何避免最终用完磁盘空间？可将日志分为特定大小的段，当日志文件达到一定大小时就关闭，并开始写到一个新的段文件中。然后，就能压缩这些段：

![图2：压缩KV 更新日志文件，仅保留每个K的最新值](https://img-blog.csdnimg.cn/7660f8bc0e2340c98da0f34c5f5c0106.png)

压缩意味着在日志中丢弃重复K，只保留每个K的最近更新。

由于压缩经常会使段更小（假设K在一个段内被平均重写多次），也能在执行压缩时将个段合并：

![图3：同时执行段压缩和多段的合并](https://img-blog.csdnimg.cn/3e3222fb2bc04351a0d61076f19042c4.png)

由于段在写入后不会再被修改，所以合并的段会被写入一个新文件。对这些冻结段的合并和压缩过程可在后台线程完成，而在运行时，仍能继续使用旧的段文件继续正常的读写请求。合并过程完成后，将读请求切换到新的合并段上，旧的段文件就能安全删除了。

每个段现在都有自己的内存哈希表，将K映射到文件的偏移量。为找到键的值，先检查最新的段的 hashmap；若K不存在，则检查第二最新的段，依此类推。因为合并过程可维持较少的段数量，因此查找一般无需检查太多hashmap。

### 实现难点

#### 文件格式

CSV不是日志最佳格式，二进制格式更快，更简单。首先以字节为单位，记录字符串的长度，然后跟上原始的字符串（无需转义）。

#### 删除记录

若要删除一个K及其V，则必须在数据文件中中追加一个特殊的删除记录（有时称为逻辑删除）。合并日志段时，一旦发现逻辑删除标记，就会丢弃这个已删除键的所有值。

#### 崩溃恢复

若数据库重启，则内存的hashmap将丢失。原则上，可通过从头到尾读取整个段文件，记录每个键的最新值的偏移量，来恢复每个段的hashmap。但若段文件很大，这可能耗时久，这将使服务器重启很慢。 Bitcask通过存储加速恢复磁盘上每个段的哈希映射的快照，可以更快地加载到内存中。

#### 部分写入记录

数据库可能随时崩溃，包括将记录追加到日志的过程中。 Bitcask文件包含校验值，这样就能发现损坏部分并丢弃。

#### 并发控制

由于写是以严格的先后顺序追加到日志，所以常见实现是只有一个写线程。数据文件段是追加的且不可变，所以它们能被多线程同时读取。

追加的日志看起来很浪费：为何不更新文件，用新值覆盖旧值？

### 追加写设计的优势

* 追加和分段合并是顺序写，一般比随机写快得多，尤其是在旋转式磁盘。某种程度上，顺序写在基于闪存的 **固态硬盘（SSD）** 也很合适
* 若段文件是追加的或不可变的，则并发和崩溃恢复就简单得多。如不必担心在重写值时发生崩溃的情况，导致留下一个包含部分旧值和部分新值混杂的文件
* 合并旧段能避免数据文件随时间推移，数据文件出现碎片化问题

### 哈希索引的劣势

* 散列表必须能放进内存

  若你有很多键，那真是倒霉。原则上，可在磁盘上维护一个hashmap，但磁盘上的hashmap很难表现优秀，需大量随机访问I/O，当hash变满时，继续增长代价昂贵，并且哈希冲突需要复杂处理逻辑

* 范围查询效率不高

  如无法轻松扫描kitty00000到kitty99999之间的所有K，只能采用逐个查找的方式查询每 个K

## SSTables和LSM树

图3中，每个日志结构的存储段都是一组KV对序列。按它们写入的顺序排列，且对于日志中的同一个K，后出现的值优先于较早的值。此外，文件中KV对的顺序并不重要。

现在简单改变段文件的格式：要求KV对的顺序按K排序。乍一看，似乎打破顺序写规则。

这种格式称为**排序字符串表（Sorted String Table）**，简称SSTable。它要求每个K只在每个合并的段文件中出现一次（压缩过程确保的）。

相比于哈希索引的日志段

### SSTable的优势

#### 合并段更简单、高效

即使文件＞可用内存。类似归排，如图4：

![图4：合并多个SSTable段，仅保留每个K的最新值](https://img-blog.csdnimg.cn/6811feed4af34f338a285f83b811d93e.png)

并发读取多个输入段文件，查看每个文件中的第一个K，复制最小的K（根据排序顺序）到输出文件，并重复该过程。这会产生一个新的按K排序的合并段文件。

若几个输入段中出现相同K咋办？每个段都包含在某段时间内写入数据库的所有值。这意味着一个输入段中的所有值，肯定比另一个段中的所有值更加的新（假设总是合并相邻的段）。当多个段包含相同K时，可保留最新段的值，并丢弃旧段中的值。

#### 在文件中查询特定K时，无需保存内存中所有K的索引

如图5：

![图5：SSTable及其内存中的索引](https://img-blog.csdnimg.cn/fa21dccf41084f8bb302be0bb0ce6298.png)

假设你正在内存中寻找K= `handiwork`，但不知道段文件中该K的确切偏移量。但若知 `handbag` 和 `handsome` 的偏移量，考虑到根据K排序的特性，可知 `handiwork` 必在这二K之间。这意味着可以跳到 `handbag` 的偏移位置，并从那里开始扫描，直到找到 `handiwork`（若该文件中无此K，则找不到）。所以，仍需一个内存索引来记录某些K的偏移量，但它可以是稀疏的：因为可以很快扫描几千字节，对于段文件中每几千字节，只需要 个键就够[^注1]。


3. 由于读请求往往需扫描请求范围内的多个KV对，可考虑将这些记录保存到一个块中，并在将其写磁盘前压缩（如图5中的阴影区） 。然后，稀疏内存索引的每个条目都指向压缩块的开头。除节省磁盘空间外，压缩还可减少I/O带宽占用。

[^注1]: 若所有KV都是定长，则可在段文件上二分查找，并完全避免内存索引。然而实践中KV都是可变长，因此若无索引，就很难知道每个记录的分界点。

#### 构建和维护SSTables

考虑到写入可能以任意顺序，如何让数据按K排序呢？

在磁盘上维护有序结构可行（如“[B树]”），但在内存保存更容易。内存排序有很多著名树状数据结构，如红黑树或AVL树。使用它们，可按任意顺序插入K，并按排序后的顺序读取。

### 存储引擎的工作流程

* 写入时，将其添加到内存中的平衡树数据结构（如红黑树）。这内存树有时称内存表（memtable）
* 当**内存表**＞某阈值（通常几兆字节）时，将其作为SSTable文件写盘。这可高效完成，因为树已维护了按K排序的KV对。新的SSTable文件成为数据库的最新部分。当SSTable被写盘时，写入可以继续添到一个新的内存表实例
* 为处理读请求，先尝试在内存表中找到K，然后在最新的磁盘段文件中，接着在次新的磁盘段文件
* 后台进程周期性地执行段合并和压缩过程，以合并多个段文件，并丢弃那些被覆盖或删除的值

这方案很好。但它还存在一个问题：若数据库崩溃，则最近的写入（在内存表中，但尚未写盘）将丢失。为避免该问题，可在磁盘上维护一个单独的日志，每个写入都会立即追加到磁盘。该日志文件无需按K排序，这并不重要，因为其唯一目的是在崩溃后恢复内存表。每当将内存表写入SSTable，相应日志都可被丢弃。

#### 从SSTables到LSM树

这里描述的算法本质上是LevelDB、RocksDB中使用的KV存储引擎库，被设计嵌入到其他应用程序。

LevelDB可在Riak中用作Bitcask的替代品。HBase使用类似存储引擎，这两种引擎都受到了Google的Bigtable文档（引入了SSTable和memtable）启发。

最初这种索引结构是由Patrick O'Neil等人描述的，且被命名为日志结构合并树（或LSM树），它是基于更早之前的日志结构文件系统而构建。基于这种合并和压缩排序文件原理的存储引擎通常被称为LSM存储引擎。

Lucene是ES、Solr使用的一种全文搜索的索引引擎，它使用类似的方法来存储它的词典。全文索引比键值索引复杂得多，但是基于类似的想法：在搜索查询中给出一个单词，找到提及单词的所有文档（网页，产品描述等）。这是通过键值结构实现的，其中键是单词（**关键词（term）**），值是包含单词（文章列表）的所有文档的ID的列表。在Lucene中，从术语到发布列表的这种映射保存在SSTable类的有序文件中，根据需要在后台合并。

#### 性能优化

大量细节使得存储引擎在实践中表现良好。如当查找数据库中不存在的键，LSM树算法可能很慢：须检查内存表，然后将这些段一直回到最老的（可能必须从磁盘读取每一个），然后才能确定键不存在。为优化这种访问，存储引擎通常使用额外的Bloom过滤器。 （布隆过滤器是用于近似集合内容的内存高效数据结构，它可以告诉你，DB中是否出现K，从而为不存在的K节省许多不必要的磁盘读操作。)

还有不同的策略来确定SSTables如何被压缩和合并的顺序和时间。最常见选择：

- size-tiered

  较新和较小的SSTables相继被合并到较旧的和较大的SSTable中。如HBase

- leveled compaction

  key范围被拆分到较小的SSTables，而较旧的数据被移动到单独的层级（level），这使得压缩（compaction）能够更加增量地进行，并且使用较少的磁盘空间。LevelDB和RocksDB（LevelDB因此得名）

Cassandra同时支持。

即使有许多微妙的东西，LSM树的基本思想 —— 保存一系列在后台合并的SSTables，简单有效。即使数据集比可用内存大得多，它仍能继续正常工作。由于数据按排序顺序存储，因此可以高效地执行范围查询（扫描所有高于某些最小值和最高值的所有键），并且因为磁盘写入是连续的，所以LSM树可以支持非常高的写入吞吐量。

### B树

刚才讨论的日志结构索引正处在逐渐被认可，但并非最常见。最多使用的索引结构在1970年诞生，此后便“无处不在”，即B树。

类似SSTables，B树保持按K排序的KV对，这可实现高效的KV查找和范围查询。但相似也就这些：B树有很不同的设计理念。

- 之前的日志结构索引将数据库分解为可变大小的段，通常几兆字节或更大，且按顺序写入段
- 而B树将数据库分解成固定大小的块或页，一般为4KB，页是内部读/写的最小单元。这种设计更接近底层硬件，因为磁盘也是固定大小的块排列。

每个页面都能使用地址或位置来标识，这就能让一个页面引用另一个页面，就像指针，不过是指向磁盘而非内存。可使用这些页面引用来构建一个页面树：

![图6：使用B树索引查找一个K](https://img-blog.csdnimg.cn/602140ef04bd41a88587a23f4cdaf19b.png)

某一页被指定为B树根；每当在索引中查找一个K，就从根开始。该页包含若干K和对子页的引用。每个子页负责一段连续范围的K，相邻引用之间的K指明了这些范围之间的边界。

图6正寻找K=251 ，所以需要沿着200~300之间的页引用，到达类似的页，它进一步将 200~300范围分解成子范围。最后，到达一个包含单个K的页面（叶子页），该页包含:

- 每个内联K的值
- 或包含能找到值的页的引用

B树的一个页所包含的子页引用的数量称为分支因子。图6中，分支因子为6 。实践中，分支因子取决于存储页面引用和范围边界所需的空间总量，通常为几百个。

- 若更新B树中已有K的值，则搜索包含该K的叶子页，更改该页中的值，并将该页写回磁盘（对该页的任何引用仍有效） 
- 若添加一个新K，找到范围包含新K的页，并将其添加到该页。若页已无可用空间保存新K，则将其分成两个半满的页，并更新父页以包含分裂后的新的K范围，如图7：

![图7：插入B树时分裂页](https://img-blog.csdnimg.cn/e891130980ed4b7085ada410e23ebfe8.png)



该算法确保树保持平衡：具有 n 个K的B树总是 O(logn) 深度。大多数DB可放入一个3~4层的B树，所以无需追踪很深的页面层次即可找到所需页面（分支因子为500的4KB页的四级树可存储256TB ）。

#### 让B树更可靠

B树的基本底层写操作是用新数据覆盖磁盘上的旧页。假设覆盖不改变页的磁盘存储位置：即当页被覆盖时，对该页的所有引用保持不变。这与日志结构索引（如LSM树）完全不同，后者仅追加更新文件（并最终删除过时文件），但从不会修改文件。

可认为硬盘上的页覆盖为实际的硬件操作。在磁性硬盘驱动器上，这意味着将磁头先移动到正确位置，然后旋转盘面，最后用新数据覆盖对应扇区。由于SSD必须一次擦除并重写很大的存储芯片块，具体更复杂。

某些操作需覆盖多个不同页。如因为插入导致页溢出而需要页分裂，则得写已拆分的两个页，并覆盖其父页以更新对两个子页的引用，这是个危险操作，因为若数据库在完成部分页写入后崩溃，最终会导致索引破坏（可能有个孤儿页，没有被任何其他页指向）。

为使数据库从崩溃中恢复，B树一般有个额外的磁盘数据结构：**预写日志（WAL, write-ahead-log）**，也称**重做日志（redo log）**。仅支持追加写，每个B树的修改须先更新WAL，再修改树的页。当数据库崩溃后需恢复时，该日志就能被用来使B树恢复到最近一致的状态。

#### 原地更新页的另一个难点

若多线程同时访问B树，需注意并发控制，否则线程可能看到树处于不一致状态。一般使用**锁存器（latches）**（轻量级锁）保护树的数据结构来完成。对此，日志结构化更简单，在后台执行所有合并，而不会干扰前端传入的查询，且不时用新段原子地替换旧段。

#### B树的优化

* 一些数据库（如LMDB）使用写时复制，而非覆盖页并维护WAL进行崩溃恢复。修改的页被写到不同位置，树中父页的新版本被创建，并指向新位置。这种方法对于并发控制也很有用。
* 保存键的缩略信息，而非完整的K，这样就能节省页空间。特别是在树中间的页，K只需能提供足够信息来充当K范围的边界。这样就能将更多K压入页，让树有更高的分支因子，极大减少层数。
* 页一般能放在磁盘任何位置；没谁要求相邻K就得放在磁盘的相邻位置。若查询需按序扫描大段的K范围，考虑到每个读取的页都可能要磁盘 I/O，所以这种逐页存储的布局可能效率低下。因此，许多B树实现尝试布局树，以便相邻的叶子页能按序保存在磁盘。但随树增长，维持该顺序很困难。相比之下，由于LSM树在合并过程中一次次重写大量存储段，所以它们更容易让连续的K在磁盘上相互靠近
* 添加额外的指针到树。如每个叶子页可能会左右引用同级的兄弟页，这无需跳回父页，就能顺序扫描K
* B树的变体如分形树，借用一些日志结构的思想来减少磁盘寻道（而且它们与分形无关）

### B树 V.S LSM树

尽管B树实现比LSM树更成熟，但LSM树由于其性能特点也很迷人。据经验，通常LSM树写更快，而B树读更快。 LSM树读一般较慢，因为它们必须在不同压缩阶段检查多个不同的数据结构和SSTable。

但基准测试通常不太确定， 且取决于很多工作负载具体细节。最好测试特定工作负载 ，这样才是有效比较 。本文将简要讨论测量存储引擎性能时值得考虑的一些要点。

#### LSM树优点

B树索引至少写两次数据：

- 写入WAL
- 写入树的页本身（还可能页分裂）

即使该页只有几个字节更改，也得承受一次写整个页的开销。有些存储引擎甚至会覆盖同一个页两次，以免在电源故障时导致出现部分更新的页。

##### 写放大（write amplification）

由于反复压缩和合并SSTables，日志结构索引也会重写数据。这种影响（在数据库的生命周期内，由于一次数据库写入请求导致的多次磁盘写）称为**写放大**。SSD寿命在覆写有限次数后就会耗尽，更关注该指标。

写密集的应用程序，性能瓶颈多半是数据库写盘速度。这时，写放大会导致直接的性能代价：存储引擎写入磁盘的次数越多，可用磁盘带宽内的每秒写入次数越少。

LSM树通常比B树支持更高写吞吐量：

- 部分是因为它们有时具有较低的写放大（尽管这取决于存储引擎配置和工作负载）
- 部分是因为它们以顺序方式写入紧凑的SSTable文件，而不必重写树中的多个页。这种差异对磁盘驱动器尤其重要，因为磁盘顺序写比随机写快得多

##### 支持更好的压缩

因此常比B树在磁盘上产生的文件小很多。 B树存储引擎会由于碎片而留下一些无法使用的磁盘空间：当页分裂或某行内容不能放入现有页时，页中某些空间就无法使用了。由于LSM树不是面向页，且定期重写SSTables以消除碎片化，所以具有较低的存储开销，特别是当使用分层压缩（leveled compaction）时。

SSD固件内部使用日志结构化算法，将随机写入转变为顺序写入底层存储芯片，因此存储引擎写入模式的影响不太明显【19】。但是，较低的写入放大率和减少的碎片对SSD仍然有利：更紧凑地表示数据可在可用的I/O带宽内提供更多的读取和写入请求。

#### LSM树缺点

日志结构存储的缺点是压缩过程有时会干扰正在进行的读写操作。尽管存储引擎尝试增量执行压缩且不影响并发访问，但磁盘的并发资源有限，所以当磁盘执行昂贵的压缩操作时，易发生读写请求等待的情况。这对吞吐量和平均响应时间的影响通常很小，但在更高百分比的情况下（参看第一篇的“[描述性能]”），日志结构化存储引擎的查询响应时间有时很长，而B树的响应延迟则相对更具可预测性。

##### 高写入吞吐量

磁盘的有限写入带宽需要在初始写入（记录和刷新内存表到磁盘）和在后台运行的压缩线程之间共享。写入空数据库时，可以使用全部的磁盘带宽进行初始写入，但数据库越大，压缩所需的磁盘带宽越多。

若写吞吐量很高，压缩又没仔细配置，就可能压缩跟不上写速率。这时，磁盘上未合并段的数量不断增加，直到磁盘空间不足，因为它们需检查更多段文件，读取速度也会减慢。一般即使压缩无法跟上，基于SSTable的存储引擎也不会限制写入速率，所以需额外监控及时发现这种情况。

B树优点则是每个K 都恰好唯一对应索引中的某个位置，而日志结构化的存储引擎可能在不同段中有相同K的多个副本。这使得B树在提供强大事务语义的数据库中更迷人：在许多关系数据库中，事务隔离是通过在K范围的锁来实现，在B树索引中，这些锁可直接定义到树中。

新的数据存储中，日志结构化索引越来越流行。没有快速容易的规则来确定哪种类型存储引擎对你更好，所以还是得实际测试才知道。

### 其他索引结构

目前只讨论KV索引，就像关系模型中的**主键（primary key）** 索引。主键唯一标识：

- 关系表中的一行
- 或文档数据库中的一个文档
- 或图形数据库中的一个顶点

数据库中的其他记录可通过其主键（或ID）引用该行/文档/顶点，且该索引用于解析此类引用。

二级索引也很常见。在关系数据库中，可使用 `CREATE INDEX` 命令在同一表上创建多个二级索引，且这些索引通常对高效执行连接至关重要。

二级索引可容易从一个KV索引来构建。主要区别是K不唯一。即可能有许多行（文档，顶点）具有相同K。这能通过两种方式来解决：

- 使索引中的每个值，成为匹配行标识符的列表（如全文索引中的posting list）

- 追加一些行标识符来使每个K变得唯一


无论哪种方式，B树和日志结构索引都能用作二级索引。

#### 在索引中存储值

索引中的K是查询搜索的对象，而其V能是如下之一：

- 上述的实际行（文档，顶点）

- 是对其他地方存储的行的引用

  行被存储的地方被称为**堆文件（heap file）**，且存储数据无特定顺序（可以是追加或记录被删除的行以便用新数据在之后覆盖它们）。堆文件方法很常见，这样当存在多个二级索引时，可避免复制数据，即每个索引只引用堆文件中的位置信息，实际数据仍保存在一个位置

在不更改K情况下更新V，堆文件方法就很高效：只要新V的字节数不大于旧V，记录就能直接覆盖。若新V更大，则更复杂，因为它可能需要移动数据以得到一个足够空间的新位置。此时：

- 所有索引都得更新，以指向记录的新的堆位置
- 或在旧堆位置保留一个间接的指针

在某些情况下，从索引到堆文件的额外跳转，对读取来说性能损失太大，因此可能希望将索引行直接存储在索引，即聚集索引。如在MySQL的InnoDB存储引擎，表的主键总是一个聚集索引，二级索引则引用主键（而不是堆文件中的位置）。

**聚集索引（clustered index）** （在索引中存储整行数据）和 **非聚集索引（nonclustered index）** （仅存储对数据的引用）之间的折中设计称为 **包含列的索引（index with included columns）** 或**覆盖索引（covering index）**，它在索引中保存一些表的列值。它可以支持只通过索引即可响应某些简单查询（这种情况下，称索引覆盖了查询）。

与任何类型的数据冗余一样，聚集和覆盖索引可加快读取速度，但它们需额外存储，且增加写开销。数据库还需更多努力来保证事务，这样应用程序就不会因为数据冗余而得到不一致结果。

#### 多列索引

至今讨论的索引只是将一个K映射到一个V。若需同时查询一个表中的多列（或文档中的多个字段），这显然还不够。

最常见的多列索引称为 **联合索引（concatenated index）** ：将一列追加到另一列后，将多个字段简单组合成一个K（索引的定义指定了字段连接的顺序）。

#### 多维索引（multi-dimensional index）
**多维索引（multi-dimensional index）** 是一种查询多个列的更通用方法，对地理空间数据尤为重要。如餐厅搜索网站的数据库，包含每个餐厅的经、纬度。当用户在地图上查看餐馆时，网站需搜索用户正在查看的矩形地图区域内的所有餐馆，这就得有个二维范围查询：

```sql
SELECT * 
FROM restaurants
WHERE latitude > 51.4946
	AND latitude < 51.5079
	AND longitude > -0.1162
	AND longitude < -0.1004;
```

但标准B树或LSM树索引无法高效响应这种查询，它只能返回：

- 一个纬度范围内的所有餐馆（但经度可能是任意值）
- 或返回在同一经度范围内的所有餐馆（但纬度可能是北极和南极之间的任一地方）

但不能同时满足。

###### 解决方案

- 使用空间填充曲线将二维位置转为单数字，然后使用常规B树索引
- 更常见的，使用专业的空间索引，如R树

一个有趣想法是，多维索引不仅能用于地理位置。如电商网站使用颜色维度（红色，绿色，蓝色）上的三维索引来搜索特定颜色范围内的产品，或在天气观测数据库中搜索二维（日期，温度）的指数，以便高效搜索2013年的温度在25至30°C之间的所有观测值。

而使用一维索引，就不得不扫描2013年所有记录（无论温度），然后通过温度过滤。

二维索引则能同时通过时间戳、温度缩小查询范围。该技术被HyperDex使用。

#### 全文搜索和模糊索引

目前的索引都假定有确切数据，并允许查询K的确切值或排序的K的值范围。他们不支持你搜索类似K，如拼写错误的单词。这种模糊查询需要不同技术。

如全文搜索引擎通常支持：

- 对一个单词的所有同义词进行查询，并忽略单词的语法变体
- 在同一文档搜索彼此接近的单词的出现
- 并支持各种依赖语言分析的其他高级功能

为处理文档或查询中的拼写错误，Lucene能在一定编辑距离内搜索文本（编辑距离1表示已添加、删除或替换了一个字母）。

Lucene为其词典使用类似SSTable的结构。需要一个小的内存索引来告诉查询，为了找到一个K，需要排序文件中的哪个偏移量。在LevelDB中，这个内存中的索引是一些K的稀疏集合，但在Lucene中，内存中的索引是K中的字符序列的有限状态自动机，类似于trie树。该自动机可转换成Levenshtein自动机，支持在给定的编辑距离内有效搜索单词。

其他的模糊搜索技术正朝着文档分类和机器学习方向发展了。

## 内存数据库

目前讨论的数据结构都是为适应磁盘限制。与内存相比，磁盘更难处理。磁盘和SSD，若想在读取和写入时获得良好性能，则需精心安排磁盘上的数据布局。但这些工作值得，因为磁盘有显著优点：

- 数据保存持久化
- 每GB的成本比内存低太多

随内存变便宜，每GB成本更低。许多数据集不是那么大，可将它们全部保存在内存或分布在多机。催动内存数据库发展。

某些内存中的KV存储（如Memcached）主要用于缓存，即使机器重启造成数据丢失也能接受。但其他内存数据库旨在实现持久性，可通过：

- 特殊硬件（如电池供电的内存）
- 或将更改记录写入磁盘日志
- 或定时快照写盘
- 或将内存中的状态复制到其他机器

内存数据库重启时，需从磁盘或通过网络从副本（除非使用特殊的硬件）重新加载数据。尽管写盘，但磁盘仅为持久化的追加日志，读取还是全靠内存服务。写盘还有运维优势：磁盘文件可容易通过外部工具执行备份，检查和分析。

如VoltDB，MemSQL和Oracle TimesTen等产品是具有关系模型的内存数据库，供应商称通过消除与管理磁盘上的数据结构相关的所有开销，他们可提供巨大性能提升。 RAM Cloud，开源内存KV存储器，具有持久性（对内存及磁盘上的数据使用日志结构）。 Redis通过异步写盘提供较弱持久性。

###  内存数据库的优势

#### 性能

其实并非因为无需从磁盘读。即使是基于磁盘的存储引擎，也可能永远无需从磁盘读取，因为os在内存中已缓存最近使用的磁盘块。相反，更快原因其实在于避免使用写盘的格式对内存数据结构进行编码的开销。

#### 数据模型

提供基于磁盘索引难以实现的某些数据模型。如Redis为各种数据结构（如优先级队列和集合）都提供类似数据库的访问接口。由于所有数据都保存在内存，所以实现也更简单。

内存数据库体系结构可以扩展到支持比可用内存更大的数据集，而不会导致以磁盘为中心架构的开销。所谓的 **反缓存（anti-caching）** 方法：当无足够内存时，通过将最近最少使用的数据从内存写盘，并在将来再次被访问时再加载到内存。这与os对虚拟内存和交换文件的操作类似，但数据库可以在记录级别而非整个内存页的粒度工作，因而比os更有效地管理内存。但这种方法仍需索引能完全放入内存。

若 **非易失性存储器（NVM）** 技术得到更广泛应用，可能还需进一步改变存储引擎设计，值得关注。

# 事务处理 or 分析处理

早期，数据库写入通常与一笔商业交易（commercial transaction）相对应：如销售、订单等。虽然随数据库发展到不涉及金钱交易的领域，术语 **交易/事务（transaction）** 仍保留下来，指一组读写操作构成的一个逻辑单元。

事务不一定具备ACID。事务处理只是意味着允许客户端进行低延迟读取和写入，而不是只能周期运行（如每天一次）的批量处理作业。

即使数据库开始被用于许多不同类型的数据，如博客评论，游戏的动作，通讯录的联系人等，但基本访问模式仍类似处理商业交易。应用程序通常使用索引通过某K查找少量记录。根据用户输入新增或更新记录。由于这些应用程序是交互式的，这种访问模式被称为 **在线事务处理（OLTP, OnLine Transaction Processing）** 。

但数据库也越来越多用于数据分析，它们有着很不同的访问模式。通常，分析查询需扫描大量记录，每个记录只读取几列，并计算汇总统计信息（如计数，总和或平均值），而非将原始数据返给用户。例如，若数据是个销售交易表，则分析查询可能包含：

* 一月份每个商店的总收入？
* 在最近的推广活动中多卖了多少香蕉？
* 哪个牌子的婴儿食品最常与X品牌的尿布同时购买？

这些查询通常由业务分析师编写，并提供给帮助管理层做更好决策（商业智能）的报告。为将这种使用数据库的模式和事务处理区分，称为**在线分析处理（OLAP，OnLine Analytice Processing）**。

表1：事务处理 V.S 分析系统

|     属性     |        事务处理 OLTP         |         分析系统 OLAP          |
| :----------: | :--------------------------: | :----------------------------: |
| 主要读取模式 |    查询少量记录，按K读取     |       在大批量记录上聚合       |
| 主要写入模式 |   随机访问，写入要求低延时   |    批量导入（ETL）或事件流     |
|   适用场景   |    终端用户，通过Web应用     | 内部数据分析师，微决策提供支持 |
|   数据表征   | 数据的最新状态（当前时间点） |      随时间推移的历史事件      |
|   数据规模   |           GB ~ TB            |            TB ~ PB             |

数据库OLTP V.S 数据仓库OLAP：

![](https://img-blog.csdnimg.cn/2b8dc6f4f9624196a5db3b8a72cd7953.png)

起初，相同数据库可同时用于事务处理和分析查询。 SQL在这方面证明是非常灵活：可同时胜任OLTP及OLAP类型查询。但1980s末和1990s初期，公司放弃使用OLTP系统用于分析，而是在单独数据库上运行分析：**数据仓库**。

### 数据仓库（data warehouse）

企业可能有几十个不同交易处理系统：面向终端客户的网站，控制实体店的收银系统，跟踪仓库库存，规划车辆路线，供应链管理，员工管理等。这些系统中每个都很复杂，需专人维护，所以系统最终都是彼此独立运行。

这些OLTP系统往往对业务运作至关重要，因而通常要求**高可用** 与处理事务时 **低延迟**。所以DBA会密切关注他们的OLTP数据库，DBA一般不愿意让业务分析人员在OLTP数据库上运行临时分析查询，因为这些查询通常开销巨大，会扫描大量数据集，这会损害并发执行事务的性能。

相比之下，数据仓库是个独立数据库，分析人员可查询他们想要的内容而不影响OLTP操作。数据仓库包含公司各种OLTP系统的只读副本。从OLTP数据库（使用周期数据转储或连续更新流）中提取数据，转换成适合分析的模式，清理并加载到数据仓库中。将数据存入仓库的过程称为“**提取-转换-加载（Extract-Transform-Load，ETL）**”：

图8：数据仓库和简化的ETL过程：

![](https://img-blog.csdnimg.cn/b3800cd1ff02480a9941894da69e1385.png)

![](https://img-blog.csdnimg.cn/a1122afbbf8e40fab47097138cdffd41.png)

大厂几乎都有数仓，但小厂却少闻。可能是因为小厂没那么多不同OLTP系统，一般只有少量数据，完全可以在传统SQL数据库中直接查询分析，甚至可以在Excel分析。而在大厂，做一些在小厂很简单的事，往往需大量繁重工作。

使用单独的数仓，而非直接查询OLTP系统进行分析，一大优势是数仓能针对分析访问模式进行优化。之前讨论的索引算法对OLTP工作效果很好，但不擅长应对分析查询。

### OLTP数据库 V.S 数据仓库

数仓的数据模型通常是关系型，因为SQL通常很适合分析查询。有许多GUI数据分析工具可生成SQL查询，可视化结果，并允许分析人员探索数据（通过下钻，切片和切块等操作）。

表面上，数仓和关系OLTP数据库相似，因为它们都有SQL查询接口。但系统内部很不同，它们针对迥然不同的查询模式，各自进行了优化。许多数据库供应商都专注支持事务处理或分析工作负载，而不是同时支持。

一些数据库（如Microsoft SQL Server和SAP HANA）支持在同一产品中支持事务处理和数仓。但它们正在日益成为两个独立的存储和查询引擎，这些引擎恰好能通过一个通用SQL接口进行访问。

大量基于Hadoop的SQL项目出现，虽还年轻，但与商业数仓系统竞争，如Hive，Spark SQL，Cloudera Impala，Facebook Presto。

### 星型和雪花型的分析模式

根据应用需要，事务处理领域使用多种不同数据模型：

- 分析型业务的数据模型则少得多
- 许多数仓都以相当公式化的方式使用，即星型模式（也称维度建模）

图9显示食品零售商处找到的数仓。模式的中心是个事实表（`fact_sales`）。事实表每行表示特定时间发生的事件（此处即客户购买的产品）。若分析网站流量而非零售量，则每行可能代表一个用户PV或点击量：

图9：用于数据仓库的星型模式

![](https://img-blog.csdnimg.cn/866cbafd33ce45e8ad9fd08ecff87baf.png)

一般事实被捕获为单独事件，之后分析中就能获得最大灵活性。但也意味着事实表可能很大。像苹果数仓可能有几十PB交易历史，大部分保存在事实表。

事实表中的列是属性，如产品销售的价格和从供应商处购买的成本（可计算利润率），其它列是对其他表（称为维度表）的外键引用。由于事实表中的每行都表示一个事件，因此这些维度代表事件的发生地点，时间，方式和原因。

如图9，其中一维是销售的产品：

- `dim_product` 表中的每行代表一种出售产品，包括**库存单位（SKU）**，说明，品牌名称，类别，脂肪含量，包装尺寸等
- `fact_sales` 表中的每行都使用外键表示在特定交易中销售了哪些产品。 （为简单起见，如果客户一次购买几种不同产品，则它们在事实表中被表示为单独行）

日期和时间通常使用维度表，因为这允许对日期的附加信息（如公共假期）进行编码，从而允许查询区分假期和非假期的销售。

## 列式存储

若事实表中有万亿行和PB级数据，则高效存储和查询就成为难题。维度表通常小得多（数百万行），所以主要关注事实表的存储。



虽然事实表通常超过百列，但典型数仓查询一次一般只访问其中的4或5个（ "SELECT *"可是很少用于分析查询的哦）。如下查询为例：它访问大量行（在2013年每次购买水果或糖果），但只需访问`fact_sales`表的三列：`date_key, product_sk, quantity`：

```sql
# 案例1：分析人们是否更倾向于购买新鲜水果或糖果，这取决于一周中的哪一天
SELECT
  dim_date.weekday,
  dim_product.category,
  SUM(fact_sales.quantity) AS quantity_sold
FROM fact_sales
  JOIN dim_date ON fact_sales.date_key = dim_date.date_key
  JOIN dim_product ON fact_sales.product_sk = dim_product.product_sk
WHERE
  dim_date.year = 2013 AND
  dim_product.category IN ('Fresh fruit', 'Candy')
GROUP BY
  dim_date.weekday, dim_product.category;
```

如何高效执行该查询SQL？

大多数OLTP数据库中，存储都是行布局：表的一行所有值彼此相邻存储。文档数据库也类似：整个文档通常存储为一个连续的字节序列。

为处理像案例1这样的查询，可以在 `fact_sales.date_key`和/或`fact_sales.product_sk`上使用索引，告诉存储引擎在哪查找特定日期或特定产品的所有销售。但行式存储引擎仍需将所有行（每个包含超过100个属性）从磁盘加载到内存并解析它们，并过滤掉那些不符要求的属性，这就可能耗时很长。

列式存储思想简单：不要将一行的所有值存储在一起，而是将每列的所有值存储在一起。若每列存储在一个单独文件，查询只需读取和解析查询中使用的那些列，能节省大量工作：![图10：列式存储关系型数据，而不是行](https://img-blog.csdnimg.cn/de16bdca2fb24f32be208a2b8a75b7bd.png)

列存储在关系数据模型中最容易理解，但其实也适用于非关系数据。

列式存储布局依赖于一组列文件，每个文件以相同顺序保存数据行。 因此，若需重新组装整行，可从每个单独的列文件中获取第23项，并将它们放在一起形成表的第23行。

### 列压缩

除了仅从磁盘加载查询所需的列，还能通过压缩数据进一步降低对磁盘吞吐量的需求。恰好，列式存储很适合压缩。

看图10中每列的值序列：它们看起来有很多重复，这就是压缩的好兆头。根据列中具体数据模式，可采用不同压缩技术。在数仓中特别有效的一种技术是位图编码：

![](https://img-blog.csdnimg.cn/735f0353081a415ab7fea8cacd27c3c5.png)

一般，列中不同值的数量<行数（如零售商可能有数十亿的销售交易，但只有100,000个不同产品）。现在可以拿个有 n 个不同值的列，并将其转换成n个单独的位图：一个位图对应一个不同的值，一个位对应一行。若该行具有该值，则该位为 1 ，否则为 0 。

- 若n很小（如国家的列可能有大约200个不同的值），则这些位图可每行存储一位
- 但若n更大，大多数位图中将有很多零（它们很稀疏）。这时，位图也能进行游程编码，如图11的底部。这使得列的编码紧凑。

这些位图索引非常适合数仓中常见的各种查询。如：

```sql
WHERE product_sk IN（30，68，69）
```

加载 `product_sk = 30` ,  `product_sk = 68` ,  `product_sk = 69` 的三个位图，并计算三个位图的按位或，这可以非常有效地完成。

```sql
WHERE product_sk = 31 AND store_sk = 3
```

加载 `product_sk = 31` 和 `store_sk = 3` 的位图，并逐位计算AND。 这是因为列按照相同的顺序包含行，因此一列的位图中的第 k 位对应于与另一列的位图中的第 k 位相同的行。

对于不同种类的数据，还有各种其它压缩方案。

#### 面向列的存储和列族
HBase列族概念，继承自Google Bigtable。但称为面向列则有误导性：在每个列族中，它们将一行中的所有列与行主键一起存储，且不使用列压缩。因此，Bigtable模型仍主要面向行。

#### 内存带宽和向量处理

需扫描数百万行的数仓查询，从磁盘加载数据到内存的带宽是一大瓶颈。这还不是唯一瓶颈。分析数据库的开发也关心咋高效将内存的带宽用于CPU缓存，避免CPU指令处理流水线中的分支错误预测和泡沫，并利用现代CPU的SIMD指令。

除减少需要从磁盘加载的数据量，列式存储布局也有助于高效利用CPU周期。如查询引擎可将一大块压缩的列数据放在CPU的L1缓存，并以紧凑循环（即无函数调用）进行迭代。对于每个被处理的记录，CPU能比基于很多函数调用和条件判断的代码更快执行这种循环。列压缩使得列中更多行能加载到L1缓存。按位“与”、“或”运算符可被设计成直接对这样的压缩列数据块进行操作。这种技术称为矢量化处理。

### 列存储中的排序顺序

列式存储中，行的存储顺序并不重要。最简单的即按插入顺序保存，这样插入一个新行只需追加到每个列文件。也能选择强制某个顺序，就像SSTables，并将其用作索引。

但注意，单独排序每列无意义，因为那样就不知列中的某项属于哪一行。因为知道某列中的第k项与另一列中的第k项一定属于同一行，基于这种约定，才能重建出完整的行。

相反，即使数据按列存储，也得一次对整行进行排序。DBA可自行选择表应该被排序的列。如若查询以日期范围为目标，如上个月，则将 `date_key` 作为第一个排序K。这样查询优化器就只扫描上个月的行，这比扫描所有行快得多。

当第1列排序出现相同值，可指定第2列继续进行排序。如若 `date_key` 是图10中的第一个排序K，则 `product_sk` 可指定为第二个排序K，这样同一天的同一产品的所有销售在存储中被分组在一起。有助于在特定日期范围内按产品对销售进行分组或过滤的查询。

排序的另一好处是可帮助压缩列。若主排序列没有很多不同的值，则排序后，它将具有很长的一个序列，其中相同的值在一行中重复多次。一个简单的游程编码，如图11中的位图，即使该表列压缩到几千字节 —— 即使表中有数十亿行。

基于第一个排序K的压缩效果一般最好。第2和第3个排序键会使得情况更复杂，通常也不会有太多相邻的重复值。排序优先级更低的列基本会呈现随机的顺序，所以一般无法被压缩。总体来说，对前几列排序仍有不错收益。

#### 几种不同的排序

C-Store最早引入一种改进思路。考虑到不同的查询受益于不同的排序，那为何不以多种不同方式存储相同的数据？无论如何，数据需要复制到多机，这样若一台机器故障，也不会丢失数据。不妨就存储不同方式排序的冗余数据，以便在处理查询时，能选择最适合的查询模式的排序版本。

列式存储有多个排序顺序，类似行式存储中有多个二级索引。但最大区别在于：

- 行式存储将每行保存在一个位置（堆文件或聚簇索引中），二级索引只包含指向匹配行的指针
- 列式存储通常没有任何指向别处数据的指针，只有包含值的列

### 列式存储的写操作

这些优化对数仓中很有意义，因为大多数负载其实是分析人员运行的大型只读查询。列式存储、压缩和排序都有助于加速读取查询。但缺点都是写入更困难。

使用B树的原地更新，对压缩的列是不可能的。若想在排序表的中间插入一行，很可能必须重写所有的列文件。因为各行是由它们在列中的位置标识的，因此插入操作必须始终一致地更新所有列。

##### 解决方案

LSM树。所有的写操作先进入内存存储，添加到已排序的结构中，再准备写盘。内存中的存储是面向行还是列的都不重要。当累积足够的写入，它们将和磁盘上的列文件合并，并批量写入新文件。

查询时，得检查磁盘上的列数据和内存中最近的写入，并结合这两者。而查询优化器对用户隐藏了这些细节。在数据分析师角度，插入，更新或删除的数据可立即反映在后续查询。

## 聚合：数据立方体和物化视图

并非每个数仓都是列式存储，但对于临时分析查询，列式存储快得多，所以才迅速普及。

数仓的另一个有趣点：物化聚合。数仓查询通常涉及聚合函数，如SQL中的COUNT、SUM、AVG、MIN或MAX。若相同的聚合被许多不同的查询使用，每次都处理原始数据就显得很浪费。何不缓存查询最常使用的一些计数或总和？

创建这种缓存的一种方式：物化视图（Materialized View）。关系数据模型中，它通常被定义为标准（虚拟）视图：一个类似表的对象，其内容是一些查询结果。

不同在于：

- 物化视图是查询结果的实际副本，并被写盘
- 虚拟视图只是写入查询的快捷方式。从虚拟视图读取时，SQL引擎会将其动态扩展到视图的底层查询，然后处理扩展查询

当底层数据变化，物化视图也得更新，因为它是数据的非规范化副本。数据库可自动执行，但这样更新影响写性能，所以OLTP数据库中不经常使用物化视图。对读密集的数仓，物化视图更有意义。

物化视图的常见特例称为数据立方体或OLAP立方体，不同维度分组的聚合网格：图12：数据立方的两个维度，通过求和聚合

![](https://img-blog.csdnimg.cn/354c22a77794432f97775cbbbf818df1.png)

想象每个事实都只有两个维度表的外键，图12中即为日期和产品。每个单元格即是date-product组合的所有事实的属性（如`net_price`）的聚合（如`SUM`）。然后，沿每行或每列应用相同的聚合操作，得到一个维度减少的聚合（按产品销售额而不管日期，或按日期的销售额而不管产品如何）。

物化数据立方体的分析：

#### 优点

某些查询会很快，因为已被预先计算。如若想知道昨天每个商店的总销售额，只需查看对应维度的总和，而无需扫描数百万行。

#### 缺点

数据立方体缺乏像查询原始数据的那种灵活性。如无法直接计算成本超过100块的物品所占销售额比重，因为价格不是其中一个维度。因此，很多数仓试图保留尽可能多的原始数据，仅当数据立方体可以对特定查询显著提升性能时，才采用多维数据聚合。

## 总结

存储引擎分为：

- 针对事务处理（OLTP）优化的架构 
- 针对在线分析（OLAP） 的优化架构

访问模式很大区别：

* OLTP系统通常面向用户，即系统要处理大量请求。为处理负载，应用程序通常只访问每个查询中的少量记录。应用程序使用某种K来请求记录，而存储引擎使用索引来查找所请求K的数据。磁盘寻道时间往往瓶颈
* 数仓和类似的分析型系统就不那么知名，因为它们主要给业务分析师使用，而非终端用户。但每个查询要求严苛，需短时内扫描数百万条记录。磁盘带宽（注意不是寻道时间）通常瓶颈，列式存储是这种工作负载主流解决方案

OLTP的主要存储引擎：

- 日志结构

  只允许追加更新文件和删除过时文件，但不会修改已写入的文件。如Bitcask、SSTables、LSM树、LevelDB、HBase、Lucene等。

- 原地更新

  将磁盘视为一组可覆盖的固定大小的页。 如B树

日志结构的存储引擎是相对较新的方案，核心思想：系统地将磁盘随机访问写转为顺序写，由于硬盘驱动器和SSD性能特性，可大大提高写吞吐量。

从存储引擎的内部间接探索了典型数仓的架构。由此说明为何分析工作负载与OLTP不同：当查询需要在大量行中顺序扫描时， 索引关联性会显著降低。相反，最重要的是非常紧凑地编码数据，以尽量减少磁盘读取的数据量。列式存储就能有所帮助。

作为应用开发人员，掌握更多有关存储引擎内部的知识，可更好了解哪种工具最适合你的具体应用。若还需进一步调整数据库的可调参数，这些理解还能帮助开发者正确评估调整参数带来的影响。

本文不能让你成为某特定存储引擎调优专家，但能帮你获得足够知识和见解，以充分理解所选择的数据库。
