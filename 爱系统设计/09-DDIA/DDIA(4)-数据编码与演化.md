# 编码与演化

-------------------
[TOC]
业务迭代总是很频繁的。一般修改应用程序的功能，也得更改其存储的数据：

- 可能要使用新字段或记录类型
- 或以新方式展示已有数据

RDBMS通常假定DB中的所有数据都遵循一种模式：尽管可更改模式（通过模式迁移，即`ALTER`语句），但在任一时间点都只有一个有效模式。相比之下，读时模式，schema-on-read（无模式，schemaless）数据库不强制执行模式，因此数据库包含了不同时间写入的新旧数据的混合体。

当数据格式（format）或模式（schema）变化，一般也需调整应用代码（如给表添加新字段，然后应用开始读、写该字段）。对大型应用，代码变更一般可不简单：

*  服务端应用，执行滚动升级（也称为阶段发布），每次将新版本部署到少数几个节点，检查新版本是否运行正常，然后逐步在所有节点升级代码。这样部署新版本时就无需暂停服务，更好地支持频繁发布和可演化性。
* 客户端应用，就得看用户。用户可能相当长一段时间里都不会去升级，所以现在基本都是 H5 页面发布升级

于是，新旧版本代码及新旧数据格式，可能在系统内共存。系统想继续顺利运行，就得保证双向的兼容性：

- 向后兼容 (backward compatibility)

  新代码能读旧代码写的数据。实现简单，新代码作者清楚旧代码的数据格式，因此能明确处理旧数据（最简单的，保留旧代码来读旧数据）

- 向前兼容 (forward compatibility)

  旧代码能读新数据写的数据。很棘手，因为旧代码需忽略新代码所新增的部分

本文将介绍几种编码数据的格式，如JSON、XML、Protocol Buffers、Thrift和Avro。关注它们：

- 如何应对模式变化
- 如何支持新旧代码和数据共存的系统提供

然后讨论如何这些格式如何用于数据存储和通信：Web服务中，**表述性状态传输（REST）** 和**远程过程调用（RPC）**及**消息传递系统**（如Actor和MQ）

## 1 编码数据的格式

程序至少会使用如下形式的数据：

1. 在内存，数据保存在对象，列表，数组，哈希表，树等。 这些数据结构针对CPU的高效访问和操作进行了优化（通常使用指针）
2. 将数据写入文件或通过网络发送时，须将其编码（encode）为某种自包含的字节序列（如JSON）。 由于一个进程中的指针对任何其他进程都没意义，所以该字节序列表示与内存中使用的数据结构完全不同

在这两种表示之间需进行类型转化：

- 从内存中表示到字节序列的转换称为编码（或序列化）
- 反过程则为解码（或解析、反序列化）

> #### 术语
> 事务的上下文里，**序列化（Serialization）** 术语也出现了，而且具有完全不同的含义。尽管序列化可能是更常见的术语，为避免术语重载，本人坚持使用 **编码（Encoding）** 表达此含义

这是个常见问题，因此也有许多库和编码格式供选择。

### 1.1 语言特定的格式

许多语言内置将内存对象编码为字节序列。如Java的`java.io.Serializable`。编码库使用方便，只需很少代码即可实现内存对象的保存与恢复。但也有复杂问题：

* 编码通常和语言强绑定，其他语言就很难读取了。若以这类编码存储或传输数据，就得长期和该语言绑定，且很难将系统与其他语言的系统集成。
* 为恢复相同的对象类型的数据，解码过程需能实例化任意类。这会导致安全问题：若攻击者能让应用程序解码任意的字节序列，则他们就能实例化任意类，这会允许他们做可怕的事情，如远程执行任意代码
* 这些库中，多版本数据是次要问题，目标主要还是快速简便地编码数据，所以经常忽略向前向后兼容问题
* 效率（编码或解码的CPU耗时及编码结构的大小）也是次要问题。 如Java内置序列化由于糟糕性能和臃肿编码而被诟病

因此，除非临时使用，采用语言内置的编码一般是孬主意。

### 1.2 JSON，XML和二进制变体

可被多种编程语言读写的标准化编码，JSON、XML无疑最知名：

- XML经常被批评：冗长且复杂
- JSON流行主要因为Web浏览器内置支持（因为是JS的一个子集），相比XML更简单
- CSV，另一种流行的语言无关的格式，尽管功能较弱

以上都是文本格式，具有不错的可读性，还有微妙问题：

* **数值（numbers）** 编码多有很多歧义之处

  XML、CSV无法区分数字和字符串（除非引用外部模式）。 JSON虽然区分字符串与数值，但不区分整数和浮点数，也不能指定精度。这在处理大数时是个大问题：如大于$2^{53}$的整数无法使用IEEE 754双精度浮点数精确表示，因此在使用浮点数（如JS）的语言中分析时，会不准确。 Twitter有个大于$2^{53}$的例子，使用64位数字标识每条推文。 Twitter API返回的JSON包含两种推特ID，一种是JSON数值，另一种是十进制字符串，以解决JS程序无法正确解析数字的问题

* JSON和XML对Unicode字符串（即人类可读文本）支持良好，但它们不支持二进制数据（无 **字符编码(character encoding)** 的字节序列）

  使用Base64将二进制数据编码为文本来解决该限制。其特有的模式标识着这值应被解释为Base64编码的二进制数据。方案虽可行，但有点混乱，会增加三分之一的数据大小

* XML、JSON都有可选的模式支持。这些模式语言很强大，学习和实现很复杂。 XML模式的使用广泛，但许多基于JSON的工具并不局限于使用模式。对数据的正确解读（如数值和二进制字符串）取决于模式中的信息，因此不使用XML/JSON模式的应用程序可能需要对相应的编码/解码逻辑进行硬编码

* CSV无任何模式，因此每行和每列的含义都由应用自行定义。若应用更改添加新的行或列，则必须手动处理。 CSV是个相当模糊的格式（若一个值包含逗号或换行符，会发生啥）。尽管其转义规则已被正式指定，但并不是所有解析器都能正确实现它们

尽管存在这些缺陷，但JSON，XML和CSV对很多需求已足够好，特别是作为数据交换格式（将数据从一个组织发送到另一个组织）。这时，只要人们对格式达成一致，格式多美观或效率有多高效都不重要。

#### 二进制编码

对于仅在组织内部使用的数据，使用最小公约数式的编码格式压力较小。如选择更紧凑或更快的解析格式。达到TB级别数据时，数据格式选型会有巨大影响。

JSON比XML简洁，但与二进制格式相比还是占空间。催生出大量二进制编码版本JSON（MessagePack，BSON，BJSON，UBJSON，BISON和Smile等） 和 XML（例如WBXML和Fast Infoset）。这些格式已在各领域中被采用，但没有一个能像JSON和XML被广泛采用。

有些格式扩展了数据类型（如区分整数和浮点数或增加对二进制字符串的支持）。由于它们没有规定模式，所以需要在编码数据时包含所有的对象字段名称。即例-1中的JSON文档的二进制编码中，它们将需要在某处包含字符串`userName`，`favoriteNumber`和`interests`

```json
// 例-1 二进制编码示例
{
    "userName": "JavaEdge",
    "favoriteNumber": 1337,
    "interests": ["java", "go"]
}
```

看个MessagePack的例子，JSON的二进制编码。图-1使用MessagePack例-1中的JSON文档进行编码而得到的字节序列：

1. 第一个字节`0x83`表示接下来是**3**个字段（低四位= `0x03`）的**对象 object**（高四位= `0x80`）。 （若想知道当一个对象有15个以上的字段会咋样，4bit已存不下，会得到另一个不同的类型标识符，且字段的数量被编码为两个或四个字节）
2. 第二个字节`0xa8`，接下来是**8**字节长（低四位=0x08）的字符串（高四位= 0x0a）
3. 接下来八字节是ASCII字符串形式的字段名称`userName`。由于之前已指明长度，不需要任何标记来标识字符串的结束位置（或者任何转义）。
4. 接下来的七个字节对前缀为`0xa6`的六个字母的字符串值`Martin`进行编码，依此类推

二进制编码长度为66个字节，仅略小于文本JSON编码所取的81个字节（去除空格）。所有的JSON的二进制编码在这方面是相似的。空间节省了一丁点（以及解析加速）是否能弥补可读性的损失，谁也说不准。


![图-1 使用MessagePack编码的记录](https://img-blog.csdnimg.cn/496ca3172ca14131b9d4f370ebd51738.png)

后文将能达到比这好得多的结果，只用32字节完成对相同的记录编码。

### Thrift与Protocol Buffers

Apache Thrift和Protocol Buffers（protobuf）是相同原理的二进制编码库。 Protocol Buffers Google开发，Thrift Facebook开发，都2007~2008年开源。
Thrift和Protocol Buffers都需一个模式来编码数据。使用Thrift **接口定义语言（IDL）** 来描述模式：

```c
struct Person {
    1: required string       userName,
    2: optional i64          favoriteNumber,
    3: optional list<string> interests
}
```

Protocol Buffers的等效模式定义：

```protobuf
message Person {
    required string user_name       = 1;
    optional int64  favorite_number = 2;
    repeated string interests       = 3;
}
```

Thrift和Protocol Buffers各有代码生成工具，采用类似案例的模式定义，并生成支持各种编程语言的类。应用程序代码可调用生成的代码来对模式的记录进行编码或解码。

Thrift有两种不同的二进制编码格式[^iii]：BinaryProtocol和CompactProtocol。先看BinaryProtocol。使用这种格式的编码来编码例-1只需59字节，如图-2：

![图-2](https://img-blog.csdnimg.cn/546c4d0ea9d849a68f6a90876cb06cca.png)

与图-1类似，每个字段有个类型注释（指示是个字符串，整数，列表等），还可根据要指定长度（包括字符串的长度，列表中的项数） 。出现在数据中的字符串`(“Martin”, “daydreaming”, “hacking”)`也被编码为ASCII（准确说，UTF-8），与之前类似。

和图-1比，最大区别是无字段名`(userName, favoriteNumber, interests)`。相反，编码数据包含字段标签(1, 2和3)。这些是模式定义中出现的数字。字段标记就像字段的别名，指示当前的字段，但更紧凑，而不必拼出字段全名。

Thrift CompactProtocol编码在语义上等同BinaryProtocol，但如图-3，它只将相同的信息打包成只有34个字节。它通过将字段类型和标签号打包到单个字节中，并使用可变长度整数来实现。数字1337不是使用全部八个字节，而是用两个字节编码，每个字节的最高位用来指示是否还有更多的字节来。这意味着-64到63之间的数字被编码为一个字节，-8192和8191之间的数字以两个字节编码，等等。较大的数字使用更多的字节。

**图4-3 使用Thrift压缩协议编码的记录**

最后，Protocol Buffers（只有一种二进制编码格式）对相同的数据进行编码，如图-4，打包方式稍不同，但与Thrift的CompactProtocol类似。 Protobuf将同样的记录塞进33字节。

![图-4 使用Protobuf编码的记录](https://img-blog.csdnimg.cn/1c02417ffa214713a32769405897ec7d.png)前面的模式中，每个字段被标记为必需或可选，但这对字段如何编码无影响（二进制数据中没有任何字段指示是否需要字段)。不同的是，若未设置该字段，则所需的运行时检查将失败，这对捕获异常很有用。

#### 字段标签和模式演变

模式不可避免要随着时间改变，称为模式演化。 Thrift和Protocol Buffers如何处理模式更改，同时又能保持向后和向前兼容性？

可见，一条编码记录只是一组编码字段的拼接。每个字段由其：

- 标签号（示例模式中的数字1,2,3）标识
- 并用数据类型（如字符串或整数）注释

若未设置字段值，则将其从编码记录中直接省略。

可轻松更改模式中字段的名称，因为编码数据永远不会直接引用字段名称。但不能随意更改字段的标签，这会导致所有现有编码数据无效。

可添加新的字段到模式，只要给每个字段一个新的标签号码。若旧代码（不知道添加的新的标签号码）试图读取新代码写入的数据，包括一个新的字段，其标签号码不能识别，它可以简单忽略该字段。实现时，通过数据类型的注释来通知解析器跳过特定字节数，这就实现了向前兼容性，即旧代码可读取由新代码编写的记录。

向后兼容性呢？只要每个字段都有一个唯一的标签号码，新的代码总是可以读取旧的数据，因为标签号码仍然具有相同的含义。唯一的细节是，如果你添加一个新的字段，你不能设置为必需。如果你要添加一个字段并将其设置为必需，那么如果新代码读取旧代码写入的数据，则该检查将失败，因为旧代码不会写入你添加的新字段。因此，为了保持向后兼容性，在模式的初始部署之后 **添加的每个字段必须是可选的或具有默认值**。

删除一个字段就像添加一个字段，只是这回要考虑的是向前兼容性。这意味着你只能删除一个可选的字段（必需字段永远不能删除），而且你不能再次使用相同的标签号码（因为你可能仍然有数据写在包含旧标签号码的地方，而该字段必须被新代码忽略）。

#### 数据类型和模式演变

如何改变字段的数据类型？但存在值丢失精度或被截断风险。如将一个32位整数变成一个64位整数。新代码可轻松读取旧代码写入的数据，因为解析器可以用0填充任何缺失的位。但若旧代码读取由新代码写入的数据，则旧代码仍使用32位变量保存。若解码的64位值不适合32位，则它将被截断。

Protobuf没有列表或数组数据类型，而是有字段的重复标记（这是除必需和可选之外的第三个选项）。如图-4，重复字段的编码正如它所说的那样：同一个字段标记只是简单地出现在记录中。这具有很好的效果，可以将可选（单值）字段更改为重复（多值）字段。读取旧数据的新代码会看到一个包含零个或一个元素的列表（取决于该字段是否存在）。读取新数据的旧代码只能看到列表的最后一个元素。

Thrift有一个专用的列表数据类型，它使用列表元素的数据类型进行参数化。这不允许Protocol Buffers所做的从单值到多值的演变，但是它具有支持嵌套列表的优点。

### Avro

Apache Avro，另一种二进制编码格式，作为Hadoop的一个子项目在2009年开始，因为Thrift不适合Hadoop。

Avro也使用模式来指定正在编码的数据的结构：

- Avro IDL，用于人工编辑
- 基于JSON，更易于机器读取

我们用Avro IDL编写的示例模式可能如下所示：

```c
record Person {
    string                userName;
    union { null, long }  favoriteNumber = null;
    array<string>         interests;
}
```

等价的JSON表示：

```json
{
    "type": "record",
    "name": "Person",
    "fields": [
        {"name": "userName", "type": "string"},
        {"name": "favoriteNumber", "type": ["null", "long"], "default": null},
        {"name": "interests", "type": {"type": "array", "items": "string"}
    ] 
}
```

模式中没有标签号码。 编码例4-1，Avro二进制编码只有32字节，编码中最紧凑的。 编码字节序列的分解如图-5

如果你检查字节序列，你可以看到没有什么可以识别字段或其数据类型。 编码只是由连在一起的值组成。 一个字符串只是一个长度前缀，后跟UTF-8字节，但是在被包含的数据中没有任何内容告诉你它是一个字符串。 它可以是一个整数，也可以是其他的整数。 整数使用可变长度编码（与Thrift的CompactProtocol相同）进行编码。

![](img/fig4-5.png)

**图4-5 使用Avro编码的记录**

为了解析二进制数据，你按照它们出现在模式中的顺序遍历这些字段，并使用模式来告诉你每个字段的数据类型。这意味着如果读取数据的代码使用与写入数据的代码完全相同的模式，才能正确解码二进制数据。Reader和Writer之间的模式不匹配意味着错误地解码数据。

那么，Avro如何支持模式演变呢？

#### Writer模式与Reader模式

有了Avro，当应用程序想要编码一些数据（将其写入文件或数据库，通过网络发送等）时，它使用它知道的任何版本的模式编码数据，例如，模式可能被编译到应用程序中。这被称为Writer模式。

当一个应用程序想要解码一些数据（从一个文件或数据库读取数据，从网络接收数据等）时，它希望数据在某个模式中，这就是Reader模式。这是应用程序代码所依赖的模式，在应用程序的构建过程中，代码可能已经从该模式生成。

Avro的关键思想是Writer模式和Reader模式不必是相同的 - 他们只需要兼容。当数据解码（读取）时，Avro库通过并排查看Writer模式和Reader模式并将数据从Writer模式转换到Reader模式来解决差异。 Avro规范【20】确切地定义了这种解析的工作原理，如[图4-6](img/fig4-6.png)所示。

例如，如果Writer模式和Reader模式的字段顺序不同，这是没有问题的，因为模式解析通过字段名匹配字段。如果读取数据的代码遇到出现在Writer模式中但不在Reader模式中的字段，则忽略它。如果读取数据的代码需要某个字段，但是Writer模式不包含该名称的字段，则使用在Reader模式中声明的默认值填充。

![](img/fig4-6.png)

**图4-6 一个Avro Reader解决读写模式的差异**

#### 模式演变规则

使用Avro，向前兼容性意味着你可以将新版本的模式作为Writer，并将旧版本的模式作为Reader。相反，向后兼容意味着你可以有一个作为Reader的新版本模式和作为Writer的旧版本模式。

为了保持兼容性，你只能添加或删除具有默认值的字段。 （我们的Avro模式中的字段`favoriteNumber`的默认值为`null`）。例如，假设你添加了一个有默认值的字段，这个新的字段将存在于新模式而不是旧模式中。当使用新模式的Reader读取使用旧模式写入的记录时，将为缺少的字段填充默认值。

如果你要添加一个没有默认值的字段，新的Reader将无法读取旧Writer写的数据，所以你会破坏向后兼容性。如果你要删除没有默认值的字段，旧的Reader将无法读取新Writer写入的数据，因此你会打破向前兼容性。在一些编程语言中，null是任何变量可以接受的默认值，但在Avro中并不是这样：如果要允许一个字段为`null`，则必须使用联合类型。例如，`union {null, long, string} field;`表示field可以是数字或字符串，也可以是`null`。如果要将null作为默认值，则它必须是union的分支之一[^iv]。这样的写法比默认情况下就允许任何变量是`null`显得更加冗长，但是通过明确什么可以和什么不可以是`null`，有助于防止出错【22】。

[^iv]: 确切地说，默认值必须是联合的第一个分支的类型，尽管这是Avro的特定限制，而不是联合类型的一般特征。

因此，Avro没有像Protocol Buffers和Thrift那样的`optional`和`required`标记（但它有联合类型和默认值）。

只要Avro可以支持相应的类型转换，就可以改变字段的数据类型。更改字段的名称也是可能的，但有点棘手：Reader模式可以包含字段名称的别名，所以它可以匹配旧Writer的模式字段名称与别名。这意味着更改字段名称是向后兼容的，但不能向前兼容。同样，向联合类型添加分支也是向后兼容的，但不能向前兼容。

#### 但Writer模式到底是什么？

到目前为止，我们一直跳过了一个重要的问题：对于一段特定的编码数据，Reader如何知道其Writer模式？我们不能只将整个模式包括在每个记录中，因为模式可能比编码的数据大得多，从而使二进制编码节省的所有空间都是徒劳的。

答案取决于Avro使用的上下文。举几个例子：

* 有很多记录的大文件

  Avro的一个常见用途 - 尤其是在Hadoop环境中 - 用于存储包含数百万条记录的大文件，所有记录都使用相同的模式进行编码。 （我们将在[第十章](ch10.md)讨论这种情况。）在这种情况下，该文件的作者可以在文件的开头只包含一次Writer模式。 Avro指定了一个文件格式（对象容器文件）来做到这一点。

* 支持独立写入的记录的数据库

  在一个数据库中，不同的记录可能会在不同的时间点使用不同的Writer模式来写入 - 你不能假定所有的记录都有相同的模式。最简单的解决方案是在每个编码记录的开始处包含一个版本号，并在数据库中保留一个模式版本列表。Reader可以获取记录，提取版本号，然后从数据库中获取该版本号的Writer模式。使用该Writer模式，它可以解码记录的其余部分。（例如Espresso 【23】就是这样工作的。）

* 通过网络连接发送记录

  当两个进程通过双向网络连接进行通信时，他们可以在连接设置上协商模式版本，然后在连接的生命周期中使用该模式。 Avro RPC协议（请参阅“[服务中的数据流：REST与RPC](#服务中的数据流：REST与RPC)”）就是这样工作的。

具有模式版本的数据库在任何情况下都是非常有用的，因为它充当文档并为你提供了检查模式兼容性的机会【24】。作为版本号，你可以使用一个简单的递增整数，或者你可以使用模式的散列。

#### 动态生成的模式

与Protocol Buffers和Thrift相比，Avro方法的一个优点是架构不包含任何标签号码。但为什么这很重要？在模式中保留一些数字有什么问题？

不同之处在于Avro对动态生成的模式更友善。例如，假如你有一个关系数据库，你想要把它的内容转储到一个文件中，并且你想使用二进制格式来避免前面提到的文本格式（JSON，CSV，SQL）的问题。如果你使用Avro，你可以很容易地从关系模式生成一个Avro模式（在我们之前看到的JSON表示中），并使用该模式对数据库内容进行编码，并将其全部转储到Avro对象容器文件【25】中。你为每个数据库表生成一个记录模式，每个列成为该记录中的一个字段。数据库中的列名称映射到Avro中的字段名称。

现在，如果数据库模式发生变化（例如，一个表中添加了一列，删除了一列），则可以从更新的数据库模式生成新的Avro模式，并在新的Avro模式中导出数据。数据导出过程不需要注意模式的改变 - 每次运行时都可以简单地进行模式转换。任何读取新数据文件的人都会看到记录的字段已经改变，但是由于字段是通过名字来标识的，所以更新的Writer模式仍然可以与旧的Reader模式匹配。

相比之下，如果你为此使用Thrift或Protocol Buffers，则字段标签可能必须手动分配：每次数据库模式更改时，管理员都必须手动更新从数据库列名到字段标签的映射。（这可能会自动化，但模式生成器必须非常小心，不要分配以前使用的字段标签。）这种动态生成的模式根本不是Thrift或Protocol Buffers的设计目标，而是Avro的。

#### 代码生成和动态类型的语言

Thrift和Protobuf依赖于代码生成：在定义了模式之后，可以使用你选择的编程语言生成实现此模式的代码。这在Java，C ++或C＃等静态类型语言中很有用，因为它允许将高效的内存中结构用于解码的数据，并且在编写访问数据结构的程序时允许在IDE中进行类型检查和自动完成。

在动态类型编程语言（如JavaScript，Ruby或Python）中，生成代码没有太多意义，因为没有编译时类型检查器来满足。代码生成在这些语言中经常被忽视，因为它们避免了显示的编译步骤。而且，对于动态生成的模式（例如从数据库表生成的Avro模式），代码生成对获取数据是一个不必要的障碍。

Avro为静态类型编程语言提供了可选的代码生成功能，但是它也可以在不生成任何代码的情况下使用。如果你有一个对象容器文件（它嵌入了Writer模式），你可以简单地使用Avro库打开它，并以与查看JSON文件相同的方式查看数据。该文件是自描述的，因为它包含所有必要的元数据。

这个属性特别适用于动态类型的数据处理语言如Apache Pig 【26】。在Pig中，你可以打开一些Avro文件，开始分析它们，并编写派生数据集以Avro格式输出文件，而无需考虑模式。

### 模式的优点

正如我们所看到的，Protocol Buffers，Thrift和Avro都使用模式来描述二进制编码格式。他们的模式语言比XML模式或者JSON模式简单得多，而后者支持更详细的验证规则（例如，“该字段的字符串值必须与该正则表达式匹配”或“该字段的整数值必须在0和100之间“）。由于Protocol Buffers，Thrift和Avro实现起来更简单，使用起来也更简单，所以它们已经发展到支持相当广泛的编程语言。

这些编码所基于的想法绝不是新的。例如，它们与ASN.1有很多相似之处，它是1984年首次被标准化的模式定义语言【27】。它被用来定义各种网络协议，例如其二进制编码（DER）仍然被用于编码SSL证书（X.509）【28】。 ASN.1支持使用标签号码的模式演进，类似于Protocol Buffers和Thrift 【29】。然而，它也非常复杂，而且没有好的配套文档，所以ASN.1可能不是新应用程序的好选择。

许多数据系统也为其数据实现了某种专有的二进制编码。例如，大多数关系数据库都有一个网络协议，你可以通过该协议向数据库发送查询并获取响应。这些协议通常特定于特定的数据库，并且数据库供应商提供将来自数据库的网络协议的响应解码为内存数据结构的驱动程序（例如使用ODBC或JDBC API）。

所以，我们可以看到，尽管JSON，XML和CSV等文本数据格式非常普遍，但基于模式的二进制编码也是一个可行的选择。他们有一些很好的属性：

* 它们可以比各种“二进制JSON”变体更紧凑，因为它们可以省略编码数据中的字段名称。
* 模式是一种有价值的文档形式，因为模式是解码所必需的，所以可以确定它是最新的（而手动维护的文档可能很容易偏离现实）。
* 维护一个模式的数据库允许你在部署任何内容之前检查模式更改的向前和向后兼容性。
* 对于静态类型编程语言的用户来说，从模式生成代码的能力是有用的，因为它可以在编译时进行类型检查。

总而言之，模式进化允许与JSON数据库提供的无模式/读时模式相同的灵活性（请参阅“[文档模型中的模式灵活性](ch2.md#文档模型中的模式灵活性)”），同时还可以更好地保证数据和更好的工具。
