2018年10月17

- 引入Stream数据类型
- 新的Redis模块API，包括Timers、Cluster和Dictionary APIs。
- RDB现在存储LFU和LRU信息
- 新的排序集命令：ZPOPMIN/MAX及其阻塞变体。
- 改进HyperLogLog实现
- 改进了内存报告功能
- 改进了客户端频繁连接和断开连接时的性能
- 引入了CLIENT UNBLOCK和CLIENT ID命令
- 添加了LOLWUT命令

1. **Streams 数据结构的引入**：
   - Streams 是 Redis 自 2.8.9 版本引入 HyperLogLog 以来的第一个全新数据结构
   - Redis Stream 类似于日志，允许存储多个字段和字符串值，并自动按时间序列进行排序。
   - Streams 与其他 Redis 数据结构相似，如列表（List）、哈希（Hash）、有序集合（Sorted Set）和发布/订阅（Pub/Sub）。
   - Streams 独特的功能是消费者组，允许不同客户端以自己的进度消费流。
   - Streams 支持事件溯源或统一日志架构等新用途。
   - Streams 有 13 个命令与之交互，具体命令列表可在 [redis.io](https://redis.io/commands#stream) 查看。
2. **ZPOP 命令的新增**：
   - 新增了 ZPOPMAX 和 ZPOPMIN 命令，分别用于移除有序集合中得分最高和最低的成员。
   - 引入了阻塞变体 BZPOPMIN 和 BZPOPMAX，类似于列表的 BLPOP 命令。
3. **其他改进**：
   - 新的模块 API 功能。
   - HyperLogLog 实现的改进。
   - 多个子命令的 HELP 支持。
   - 内存管理和报告的增强。
   - RDB 存储键的频率和最近性信息（例如 LFU, LRU）。
   - Lua 副本和 AOF 的改进。
   - 网络和客户端连接管理的改进。
   - 客户端识别和阻塞管理的改进。
4. **LOLWUT 命令**：
   - 一个无实际技术目的但有趣的命令，用于生成随机元素和命令参数的计算机艺术。
   - 可以用于测试 Redis 5.0 是否正确运行。

 Redis 5.0[发布说明](https://raw.githubusercontent.com/antirez/redis/5.0/00-RELEASENOTES)。