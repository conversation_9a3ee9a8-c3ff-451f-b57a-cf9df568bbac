## 0 前言

一个K对应一个数据集合的场景：

- App每天的用户登录信息：一天对应一系列用户ID或客户端ID
- 商品的用户评论列表：一个商品对应了一系列的评论
- App上的签到打卡信息：一天对应一系列用户的签到记录
- 应用网站上的网页访问信息：一个网页对应一系列的访问点击

恰巧，Redis集合类型就是一个K对应数据集，先天适合存取这些数据。但现在除了记录信息，还需统计集合中的数据：
- 在移动应用中，需要统计每天的新增用户数和第二天的留存用户数
- 在电商网站的商品评论中，需要统计评论列表中的最新评论
- 在签到打卡中，需要统计一个月内连续打卡的用户数
- 在网页访问记录中，需要统计独立访客（Unique Visitor，UV）量

互联网应用的用户数量及访问量庞大，必须选择高效统计大量数据（例如亿级）的数据结构。想选择合适集合，就得先了解集合统计模式。

## 1 聚合统计

统计多个集合元素的聚合结果，包括：
- 统计多个集合的交集
- 把两个集合相比，统计其中一个集合的差集
- 统计多个集合的并集

### 统计App日增用户数和次日留存用户数
- 一个集合，记录所有登录过APP的用户ID
- 另一个集合，记录每一天登录过APP的用户ID

再对这俩集合做聚合统计，记录所有登录过App的用户ID，使用Set：
- K为`user:id`，记录的是用户ID
- value，Set集合，所有登录过APP的用户ID，累计用户Set

累计用户Set中无日期信息，因为不能直接统计每日新增用户。还需把每天登录的用户ID，记到一个每日用户Set，它有两个特点：
- key= `user:id:当天日期`，例如 `user:id:20200803`
- value=Set，记录当天登录的用户ID

统计每日新增用户时，只需计算每日用户Set和累计用户Set的差集。

假设APP在2021年10月1日上线，10月1日前没有用户。
此时：
- 累计用户Set为空
- 当天登录的用户ID会被记录到 key为`user:id:20211001`的Set。所以，`user:id:20211001`Set中的用户就是当日新增用户。

然后，计算累计用户Set、`user:id:20211001`Set的并集，保存在`user:id`累计用户Set。
```bash
SUNIONSTORE  user:id  user:id  user:id:20211001 
```
此时，`user:id`累计用户Set中就有了10月1日的用户ID。
等到10月2日再统计，把10月1日登录的用户ID记录到`user:id:20211002` 的Set。
执行SDIFFSTORE计算累计用户Set、`user:id:20211002` Set的差集，保存在`key=user:new`的Set：
```bash
SDIFFSTORE  user:new  user:id:20211002 user:id  
```
这个差集中的用户ID在`user:id:20211002` 的Set中存在，但不在累计用户Set中。所以，`user:new`这个Set中记录的就是10月2日的新增用户。

计算8月4日的留存用户时，只需计算`user:id:20200803` 和 `user:id:20200804`Set交集，就可以得到同时在这两个集合中的用户ID了，这些就是在8月3日登录，并且在8月4日留存的用户。执行的命令如下：
```csharp
SINTERSTORE user:id:rem user:id:20200803 user:id:20200804
```
需聚合计算多个集合时，Set很不错。不过，Set的差集、并集和交集的计算复杂度较高，数据量较大情况下，直接执行这些计算，会导致Redis实例阻塞。
可以从主从集群中选择一个从库，让它专门负责聚合计算，或把数据读取到客户端，在客户端完成聚合统计，避免阻塞主库实例和其他从库实例。
## 排序统计
最新评论列表包含所有评论中的最新留言，要求集合中的元素有序。常用集合类型中（List、Hash、Set、Sorted Set），List和Sorted Set属有序集合。
- List，按元素进入List顺序进行排序的
- Sorted Set根据元素权重排序，可自己决定每个元素的权重值
如根据元素插入Sorted Set的时间确定权重值，先插入元素权重小，后插入元素权重大

该怎么选择呢？
用List。每个商品对应一个List，这List包含对这个商品的所有评论，按评论时间保存这些评论，每来一个新评论，LPUSH插入List队头。

只有一页评论时，可以很清晰地看到最新评论，但实际网站一般会分页显示最新评论列表，List就可能会出现问题了。

假设当前的评论List是{A, B, C, D, E, F}（A是最新的评论），在展示第一页的3个评论时，我们可以用下面的命令，得到最新的三条评论A、B、C：

```csharp
LRANGE product1 0 2
1) "A"
2) "B"
3) "C"
```

然后，再用下面的命令获取第二页的3个评论，也就是D、E、F。

```csharp
LRANGE product1 3 5
1) "D"
2) "E"
3) "F"
```
但若在展示第二页前，又产生了一个新评论G，评论G就会被LPUSH命令插入到评论List的队头，评论List就变成了{G, A, B, C, D, E, F}。此时，再用刚才的命令获取第二页评论时，就会发现，评论C又被展示出来了，也就是C、D、E。
```csharp
LRANGE product1 3 5
1) "C"
2) "D"
3) "E"
```
因为List是通过元素在List中位置来排序的，当有一个新元素插入时，原先元素在List中的位置都后移一位，比如说原来在第1位的元素现在排在了第2位。所以，对比新元素插入前后，List相同位置上的元素就会发生变化，用LRANGE读取时，就会读到旧元素。

Sorted Set就不存在这个问题，因为它是根据元素的实际权重排序和获取数据。可按评论时间先后给每条评论设置权重值，然后再把评论保存。
Sorted Set的ZRANGEBYSCORE命令就可以按权重排序后返回元素。即使元素频繁更新，Sorted Set也能通过ZRANGEBYSCORE准确获取按序排列数据。

假设越新的评论权重越大，目前最新评论的权重是N，我们执行下面的命令时，就可以获得最新的10条评论：
```csharp
ZRANGEBYSCORE comments N-9 N
```
所以，最新列表、排行榜等场景时，如数据更新频繁或需分页显示，优先考虑Sorted Set。
## 二值状态统计
集合元素的取值就只有0和1两种。如签到打卡，只记录签到（1）、未签到（0）。
每个用户一天的签到用1位表示，一个月（假设是31天）签31bit即可，不用复杂集合类型。
就可选择Bitmap。用String类型作为底层数据结构实现的一种统计二值状态的数据类型。String类型是会保存为二进制的字节数组，所以，Redis就把字节数组的每个bit位利用起来，用来表示一个元素的二值状态。
可把Bitmap看作是一个bit数组。

Bitmap提供了GETBIT、SETBIT，使用一个偏移值offset对bit数组的某一个bit位进行读和写。
Bitmap的偏移量是从0开始算，即offset最小值是0。当使用SETBIT对一个bit位进行写操作时，这个bit位会被设置为1。Bitmap还提供了BITCOUNT操作， 统计这个bit数组中所有“1”的个数。

假设统计ID 3000的用户在2020年8月份的签到情况
第一步，执行下面的命令，记录该用户8月3号已签到。
SETBIT uid:sign:3000:202008 2 1 
第二步，检查该用户8月3日是否签到。
GETBIT uid:sign:3000:202008 2 
第三步，统计该用户在8月份的签到次数。
BITCOUNT uid:sign:3000:202008
就知道该用户在8月份的签到情况了。如果记录了1亿个用户10天的签到情况，你有办法统计出这10天连续签到的用户总数吗？

Bitmap支持用BITOP命令对多个Bitmap按位做“与”“或”“异或”的操作，操作的结果会保存到一个新的Bitmap中。以按位“与”为例，三个Bitmap对应bit位做“与”操作，结果保存到新Bitmap（“resmap”）：
![](https://img-blog.csdnimg.cn/cacaf0cc759c469a88075bac53d87a9b.png?x-oss-process=image/watermark,type_ZHJvaWRzYW5zZmFsbGJhY2s,shadow_50,text_SmF2YUVkZ2U=,size_20,color_FFFFFF,t_70,g_se,x_16)
在统计1亿个用户连续10天签到情况时，可把每天日期作为key，每个key对应一个1亿位Bitmap，每bit对应用户当天签到。
对10个Bitmap做“与”操作，得到的结果也是一个Bitmap。在这个Bitmap中，只有10天都签到的用户对应的bit位上的值才会是1。最后，我们可以用BITCOUNT统计下Bitmap中的1的个数，这就是连续签到10天的用户总数了。

计算记录了10天签到情况后的内存开销。每天使用1个1亿位Bitmap，大约12MB（10^8/8/1024/1024），10天Bitmap内存开销约120MB，不算太大。实际应用最好对Bitmap设置过期时间，让Redis自动删除不再需要的签到记录，以节省内存开销。

所以，如果只需统计数据的二值状态，例如商品有没有、用户在不在等，就可以使用Bitmap，因为它只用一个bit位就能表示0或1。在记录海量数据时，Bitmap能够有效地节省内存空间。

## 基数统计
统计一个集合中不重复的元素个数。如统计网页UV。

网页UV统计需要去重，一个用户一天内多次访问只能算一次。Set默认支持去重，所以看到有去重需求时，我们可能第一时间就会想到用Set类型。

有一个用户user1访问page1时，你把这个信息加到Set中：
SADD page1:uv user1
用户1再来访问时，Set的去重功能就保证了不会重复记录用户1的访问次数，这样，用户1就算是一个独立访客。当你需要统计UV时，可以直接用SCARD命令，这个命令会返回一个集合中的元素个数。

如果page1非常火爆，UV达到千万，一个Set就要记录千万用户ID。大促电商网站，这样页面可能成千上万，如果每个页面都用这样的Set，就会消耗很大内存。
当然，也可以用Hash类型记录UV。
可以把用户ID作为Hash集合的key，当用户访问页面时，就用HSET命令（用于设置Hash集合元素的值），对这个用户ID记录一个值“1”，表示一个独立访客，用户1访问page1后，我们就记录为1个独立访客，如下所示：
HSET page1:uv user1 1
即使用户1多次访问页面，重复执行这个HSET命令，也只会把user1的值设置为1，仍然只记为1个独立访客。当要统计UV时，我们可以用HLEN命令统计Hash集合中的所有元素个数。

当页面很多时，Hash类型也消耗很大内存空间。就要用到HyperLogLog。用于统计基数的数据集合类型，最大优势就在于，当集合元素数量非常多，它计算基数所需空间总是固定，且还很小。

在Redis中，每个 HyperLogLog只需花费 12 KB 内存，就可计算接近 2^64 个元素的基数。你看，和元素越多就越耗费内存的Set和Hash类型相比，HyperLogLog就非常节省空间。

在统计UV时，你可以用PFADD命令（用于向HyperLogLog中添加新元素）把访问页面的每个用户都添加到HyperLogLog中。

```csharp
PFADD page1:uv user1 user2 user3 user4 user5
```

接下来，就可以用PFCOUNT命令直接获得page1的UV值了，这个命令的作用就是返回HyperLogLog的统计结果。

```csharp
PFCOUNT page1:uv
```
HyperLogLog的统计规则是基于概率完成的，所以其统计结果有一定误差，标准误算率0.81%：使用HyperLogLog统计的UV是100万，但实际UV可能是101万。若你需精确统计结果，那就还是Set或Hash。

## 总结

![](https://img-blog.csdnimg.cn/bd558926d0e247f488ea247cba536190.png?x-oss-process=image/watermark,type_ZHJvaWRzYW5zZmFsbGJhY2s,shadow_50,text_SmF2YUVkZ2U=,size_20,color_FFFFFF,t_70,g_se,x_16)
Set和Sorted Set都支持多种聚合统计，不过，对于差集计算来说，只有Set支持。
