- 数据量太大查询慢怎么办？存档历史数据或者分库分表，这是数据分片
- 并发太高扛不住怎么办？读写分离，这是增加实例数
- 数据库宕机怎么办？增加从节点，主节点宕机的时候用从节点顶上，这是主从复制。但是这里面要特别注意数据一致性的问题。

这些方法不是MySQL特有的，对于几乎所有的存储系统，都适用。

如何构建一个生产系统可用的Redis缓存集群？

Redis3.0开始提供官方集群支持Redis Cluser，相比单节点Redis，能保存更多数据，支持更多并发，并且高可用。

为能保存更多数据，和MySQL分库分表的方式类似，Redis Cluster分片把数据分布到集群的多个节点。

Redis Cluster引入“槽（Slot）”，即哈希表中的哈希槽，Redis分片的基本单位，每个槽里包含一些Key。每个集群的槽数固定16384（16 * 1024）个，每个Key落在哪个槽中也是固定的

计算方法：

```bash
# 先计算Key的CRC值，然后把这个CRC之后的Key值直接除以16384，余数就是Key所在的槽
HASH_SLOT = CRC16(key) mod 16384
```

即哈希分片算法。

这些槽如何存放到具体Redis节点？映射关系保存在集群的每个Redis节点，集群初始化时，Redis自动平均分配这16384个槽，也可命令调整。分槽的分片算法：查表法。

客户端可连接集群的任意一个节点来访问集群的数据，当客户端请求一个Key的时候，被请求的那个Redis实例先通过上面的公式，计算出这个Key在哪个槽中，然后再查询槽和节点的映射关系，找到数据所在的真正节点，如果这个节点正好是自己，那就直接执行命令返回结果。如果数据不在当前这个节点上，那就给客户端返回一个重定向的命令，告诉客户端，应该去连哪个节点上请求这个Key的数据。然后客户端会再连接正确的节点来访问。

解决分片问题后，Redis Cluster即可水平扩容增加集群的存储容量，但每次往集群增加节点，要从集群那些老节点，搬运一些槽到新节点：

- 可手动指定哪些槽迁移到新节点
- 也可利用官方[redis-trib.rb](http://download.redis.io/redis-stable/src/redis-trib.rb)脚本来自动重新分配槽，自动迁移

分片解决Redis保存海量数据问题，提升Redis的并发能力和查询性能。但不解决高可用，每个节点都保存整个集群数据的一个子集，任一节点宕机，都会导致这宕机节点上的那部分数据无法访问。

## 高可用

**增加从节点，做主从复制**。Redis Cluster支持给每个分片增加一或多个从节点，每个从节点在连接到主节点后，先给主节点发送一个SYNC命令，请求一次全量复制，即把主节点的全部数据复制到从节点。全量复制完成后，进入同步阶段，主节点把刚全量复制期间收到的命令，及后续收到的命令持续地转发给从节点。

因为Redis不支持事务，复制比MySQL简单，Binlog省了，直接转发客户端发来的更新数据命令来实现主从同步。若某分片的主节点宕机，集群中的其他节点会在这分片的从节点中选个新节点作为主节点继续提供服务。新主节点选举后，集群中所有节点都会感知到，这样，若客户端请求Key落在故障分片，就会被重定向到新主节点。

## 高并发

Redis Cluster分片后，每个分片都会承接一部分并发的请求，加上Redis本身单节点性能就高，所以大部分情况下不需像MySQL那样做读写分离来解决高并发的问题。

默认，集群的读写请求都由主节点负责，从节点只热备作用。Redis Cluster也支持读写分离，在从节点读取数据。

![img](https://static001.geekbang.org/resource/image/c4/fd/c405e73a4fd797ca0cda82b383e46ffd.png)

https://rancher.com/blog/2019/deploying-redis-cluster/

Redis Cluster整体架构完全照抄MySQL集群那套。

搭建Redis Cluster以及相关的操作命令[Redis官方的这篇教程](https://redis.io/topics/cluster-tutorial)。

## 为何Redis Cluster不适超大规模集群

Redis Cluster易于使用。分片、主从复制、弹性扩容都可做到自动化，简单部署就可获得大容量、高可靠、高可用Redis集群，对应用近乎透明。

所以，**Redis Cluster是非常适合构建中小规模Redis集群**，大概几个到几十个节点这样规模的Redis集群。

但Redis Cluster不适合构建超大规模集群，它采用去中心化设计。Redis的每个节点上，都保存了所有槽和节点的映射关系表，客户端可访问任意一个节点，再通过重定向命令，找到数据所在节点。这映射关系表如何更新呢？如集群加入新节点或某主节点宕机，新主节点被选举出来，这些情况下，都要更新集群每个节点上的映射关系表。

Redis Cluster采用去中心化[流言(Gossip)协议](https://en.wikipedia.org/wiki/Gossip_protocol)来传播集群配置的变化。

所谓流言，就是八卦，比如说，我们上学的时候，班上谁和谁偷偷好上了，搞对象，那用不了一天，全班同学都知道了。咋知道的？张三看见了，告诉李四，李四和王小二特别好，又告诉了王小二，这样人传人，不久就传遍全班了。这个就是八卦协议传播原理。

好处是去中心化，传八卦不需要组织，吃瓜群众自发就传开了。这样部署和维护就更简单，避免中心节点单点故障。缺点就是传播速度慢，集群规模越大，传播越慢。如换成某两个特别出名的明星搞对象，即使是全国人民都很八卦，但要想让全国每一个人都知道这消息，还是要很长时间。在集群规模太大的情况下，数据不同步的问题会被明显放大，还有一定的不确定性，出现问题很难排查。

## Redis构建超大规模集群

所以很多大厂都自己去搭建Redis集群。一种是基于代理，在客户端和Redis节点间，增加一层代理服务。

### 代理服务的作用

#### 在客户端和Redis节点之间转发请求和响应

客户端只和代理服务打交道，代理收到客户端的请求之后，再转发到对应的Redis节点上，节点返回的响应再经由代理转发返回给客户端。

#### 监控集群中所有Redis节点状态

如果发现有问题节点，及时进行主从切换。

#### 维护集群的元数据

元数据主要就是：

- 集群所有节点的主从信息
- 槽和节点关系映射表

这架构用HAProxy+Keepalived来代理MySQL请求的架构是类似的，只是多了一个自动路由分片功能。

![](https://p.ipic.vip/wgfzl4.png)

开源Redis集群方案[twemproxy](https://github.com/twitter/twemproxy)和[Codis](https://github.com/CodisLabs/codis)，都这种架构。

这架构最大优点：对客户端透明，客户端视角，整个集群就是一个超大容量单节点Redis。由于分片算法是代理服务控制的，扩容也方便，新节点加入集群后，直接修改代理服务中的元数据就完成扩容。

缺点也突出：

- 增加一层代理转发，每次数据访问链路更长，性能损失
- 代理服务本身又是集群的一个单点，当然，可把代理服务做成一个集群解决单点问题，那样集群就更复杂

另外一种方式，不用代理服务，把代理服务的寻址功能前移到客户端。客户端发请求前，先查询元数据，就知道要访问哪个分片、哪个节点，然后直连对应Redis节点访问数据。

当然，客户端不用每次都查询元数据，因为这元数据不咋变化，客户端可自己缓存元数据，访问性能基本就和单机Redis一样。如某分片的主节点宕机，新主节点被选举出来后，更新元数据里面的信息。对集群的扩容操作也简单，除了迁移数据的工作必须要做，更新下元数据。

![](https://p.ipic.vip/rjueq8.png)

虽这元数据服务仍是单点，但它数据量、访问量都不大，相对易实现。可用zk、etcd甚至MySQL都满足。这方案最适合超大规模Redis集群方案，性能、弹性、高可用几方面表现都好。

缺点是整个架构复杂，客户端不能通用，需定制化Redis客户端，大厂才负担起。

## 为何要Redis Cluster？

- 并发量
redis 本身 10w/s，但的业务已经需要100 w/s 了
- 数据量
一个 redis 及其内存 16~256G，但我们的业务就是强，需要 500G 了

解决问题的办法不止于简单地加机器。

并发量大了 =》 主从复制解决 =》主从稳定性 =》哨兵解决 =》单节点的写能力、存储能力、动态扩容都很麻烦 =》集群Cluster解决。

Redis Cluster集群模式具有高可用、可扩展性、分布式、容错等特性，在 3.0 闪亮发布。

## Redis Cluster架构

### 单机架构

![](https://img-blog.csdnimg.cn/img_convert/0fccc482d9552f99376536881e26632a.png)

### 分布式架构

![](https://img-blog.csdnimg.cn/img_convert/e6a89dc63deb8b441d99372652bef7b5.png)

### Redis Cluster 架构

节点
meet
指派槽
复制

- 节点
  ![](https://img-blog.csdnimg.cn/img_convert/22af1adc6b0ff64c4dc325e43de3ff1b.png)

- meet
  ![](https://img-blog.csdnimg.cn/img_convert/216c778710f5b345285aec8592427607.png)
  ![](https://img-blog.csdnimg.cn/20210430221455380.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

- 指派槽![](https://img-blog.csdnimg.cn/img_convert/f64f423f44dc18771b2ef5369f18159d.png)
- 客户端与指派槽![](https://img-blog.csdnimg.cn/img_convert/c2aba7a4bdaf37f807b0e87e99b7624b.png)

Redis Cluster特性：
复制、高可用、分片

### Redis分布式服务端及客户端启动
修改两个redis的配置文件`redis.conf`
修改端口一个为6379，另一个为6380
通过配置文件启动redis-server
redis-server `${redis[0-1]}`的`${redis.conf}`

### 集群 V.S 分布式
- 分布式：不同的业务模块拆分到不同机器，解决高并发的问题。 工作形态 redis服务器各工作方式不同一般称为Redis分布式

- 集群（cluster）
同一个业务部署在多台机器上，提高系统可用性。是物理形态,一般称Tomcat集群。集群就是一组计算机，它们作为整体向用户提供一组网络资源， 这些单个的计算机系统就是集群的节点(node)。
集群可能运行着一个或多个分布式系统，也可能根本没有运行分布式系统；分布式系统可能运行在一个集群上，也可能运行在不属于一个集群的多台（2台也算多台）机器上。

集群提供了以下关键的特性：
- 可扩展
- 高可用
- 负载均衡
- 错误恢复

分布式与集群的联系与区别：
- 分布式即分开部署。集群指的是将几台服务器集中在一起，实现同一业务
- 分布式的每一个节点，都可以做集群，而集群并不一定就是分布式的

比如客户端有10个用户，同时分别发送了1个请求，若不是集群，则这10个请求需要并行在一台机器上处理，如果每个请求都是1s，则有一人要等待10s，有一个人等待9s...
而在集群下，10个任务并分发到10台机器同时进行，则每人等待时间都是1s。

## Redis Cluster 安装配置

两种安装：
### 原生命令安装

#### 理解架构

1.配置开启节点

2. meet
3.指派槽
4.主从
### 官方工具安装

1.配置开启节点

2. meet
3. 指派槽
4. 主从

配置开启Redis-1：
```bash
port ${port}
daemonize yes
dir "/opt/redis/redis/data/"
dbfilename "dump-${port}.rdb"
logfile "${port}.log"
cluster-enabled yes
cluster-config-file nodes-${port}.conf
cluster-node-tiemout 5000
```
配置开启Redis-2
```bash
redis-server redis-7000.conf
redis-server redis-7001.conf
redis-server redis-7002.conf
redis-server redis-7003.conf
redis-server redis-7004.conf
redis-server redis-7005.conf
```

![meet](https://img-blog.csdnimg.cn/img_convert/24c222f0c4cfabe8348026798c8467c9.png)
![Cluster节点主要配置](https://img-blog.csdnimg.cn/img_convert/558d1ad70f68a0e1e8fd94a19798aaac.png)
![分配槽](https://img-blog.csdnimg.cn/img_convert/ec5a3d8c03e03f4a2967ffa06bae9e7a.png)
![设置主从](https://img-blog.csdnimg.cn/img_convert/d02af90c98f14f3b10e4ce61d27478c5.png)

Redis 3.0后，节点之间通过去中心化，提供了完整的`sharding`、`replication`（复制机制仍使用原有机制，并且具备感知主备的能力)、failover解决方案，称为Redis Cluster。

即：将proxy/sentinel的工作融合到了普通Redis节点。

## Redis Cluster 集群架构

Redis Cluster采用无中心结构，每个节点都能保存数据和整个集群状态，每个节点都和其他所有节点连接。要让集群正常运作，至少需三个主节点，即Cluster至少6个（三主三从）才能保证组成完整高可用的集群：
- 主节点分配槽，处理客户端的命令请求
- 从节点可用在主节点故障后，顶替主节点

### 架构示意图



![](https://img-blog.csdnimg.cn/ea1d4cb0e176491f8eaac2ad3803cf21.png)

![](https://img-blog.csdnimg.cn/20210505153437313.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

集群架构为拓扑结构。一个Redis Cluster由多个Redis节点组成。不同的节点组服务的数据无交集，每个节点对应数据`sharding`的一个分片。

节点组内部分为主备2类，对应`master`和`slave`。两者数据准实时一致，通过异步的主备复制保证。

一个节点组有且仅有一个master，同时有0到多个slave。只有master对外提供写服务，读服务可由master/slave提供

## Redis Cluster结构



![](https://img-blog.csdnimg.cn/img_convert/0a18bd4b9a631b2744f56a4fae470403.png)
key-value全集被分成5份，5个slot（实际上Redis Cluster有 16384 [0-16383] 个slot，每个节点服务一段区间的slot，此处仅是为了举例)。
A、B为M节点，对外提供写服务。分别负责1/2/3和4/5的slot。A/A1和B/B1/B2之间通过主备复制以同步数据。

这5个节点，两两通过Redis Cluster Bus交互，相互交换如下的信息：
- 数据分片（slot）和节点的对应关系
- 集群中每个节点可用状态
- 集群结构发生变更时，通过一定的协议对配置信息达成一致。数据分片的迁移、主备切换、单点master的发现和其发生主备关系变更等，都会导致集群结构变化
- publish/subscribe（发布/订阅）功能，在Cluster版内部实现所需要交互的信息

Redis Cluster Bus通过单独的端口进行连接，由于`Bus是节点间的内部通信机制`，交互的是字节序列化信息。相对Client的字符序列化来说，效率较高。

Redis Cluster是一个`去中心化的分布式实现方案`，客户端和集群中任一节点连接，然后通过后面的交互流程，逐渐的得到全局的数据分片映射关系。

## Redis Cluster优点



- 去中心化
- 可扩展性
数据是按槽分配的，节点之间数据共享，节点动态的添加和删除
- 高可用性
部分节点不可用时，集群仍是可用的，因为从节点作为副本提供了数据的备份保障
- 自动故障转移
节点之间投票选举，从升级为主

## Redis Cluster缺点



![](https://img-blog.csdnimg.cn/20210505155543138.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)




- 数据通过异步复制，无法保证数据强一致性
- 集群环境搭建略微复杂。
## 2 配置的一致性

对于去中心化的实现，集群的拓扑结构并不保存在单独的配置节点上，后者的引入同样会带来新的一致性问题。
那么`各自为政的节点间，如何对集群的拓扑达成一致`，是Redis Cluster配置机制要解决的问题。
Redis Cluster通过引入2个自增的`epoch`变量，来使得集群配置在各个节点间最终达成一致。

### 2.1 配置信息数据结构

Redis Cluster中的每个节点(Node)都保存了集群的配置信息，并且存储在clusterState中，结构如下
![](https://img-blog.csdnimg.cn/img_convert/b9e01594ad4faf8f9ba85394a394871a.png)

- clusterState 记录了从集群中某个节点视角，来看集群配置状态
- currentEpoch 表示整个集群中最大的版本号，集群信息每变更一次，改版本号都会自增
- nodes 是一个列表，包含了本节点所感知的，集群所有节点的信息（clusterNode），也包含自身的信息。
- clusterNode 记录了每个节点的信息，其中包含了节点本身的版本 Epoch；自身的信息描述：节点对应的数据分片范围（slot）、为M时的R列表、为R时的M节点
- 每个节点包含一个全局唯一的NodeId
- 当集群的数据分片信息发生变更（数据在节点间迁移时），Redis Cluster 仍然保持对外服务
- 当集群中某个master宕机，Redis Cluster 会自动发现，并触发故障转移的操作。将master的某个slave晋升为新的 master。

由此可见，每个节点都保存着Node视角的集群结构。它描述了数据的分片方式，节点主备关系，并通过Epoch 作为版本号实现集群结构信息的一致性，同时也控制着数据迁移和故障转移的过程。
### 2.2 信息交互

节点间的内部通信机制

#### 基本通信原理

集群元数据的维护方式：

- 集中式

- Gossip 协议

  redis cluster 节点间采用 gossip 协议通信。

#### 集中式

将集群元数据（节点信息、故障等）几种存储在某节点。集中式元数据集中存储的一个典型代表storm，分布式的大数据实时计算引擎，集中式的元数据存储的结构，底层基于zk对所有元数据进行存储维护。

redis维护集群元数据采用gossip协议，所有节点都持有一份元数据，不同节点若出现了元数据的变更，就不断将元数据发送给其它的节点，让其它节点也进行元数据的变更。

##### 好处

元数据读取和更新，时效性好，一旦元数据变更，就立即更新到集中式存储，其它节点读取时，就可感知到

##### 不好

所有的元数据的更新压力全部集中在一个地方，可能会导致元数据存储较大压力。

#### gossip协议

##### 好处

元数据更新较分散，不集中单点，更新请求会陆续打到所有节点上去更新，降低单点压力

##### 不好

元数据更新有延时，可能导致集群中的一些操作会有一些滞后。

- 10000端口：每个节点都有一个专于节点间通信的端口，即提供服务的端口号+10000，比如 7001，那么用于节点间通信的就是 17001 端口。每个节点每隔一段时间都会往另外几个节点发送 `ping` 消息，同时其它几个节点接收到 `ping` 之后返回 `pong`

- 交换的信息：包括故障信息，节点的增删，hash slot 信息等。

gossip 协议包含多种消息，如`ping`,`pong`,`meet`,`fail` 等。

- meet：某个节点发送 meet 给新加入节点，让新节点加入集群，然后新节点就会开始和其它节点通信。

```bash
redis-trib.rb add-node
```

就是发了个 gossip meet消息给新节点，通知那个节点入群。

- ping：每个节点都会频繁给其它节点发送 ping，包含自己的状态和自己维护的集群元数据，互相通过 ping 交换元数据
- pong：返回ping 和meeet，包含自己的状态和其它信息，也用于信息广播和更新
- fail：某节点判断另一节点fail后，就发送 fail 给其它节点，通知其它节点某节点宕机

#### ping消息

ping要携带一些元数据，若很频繁，可能加重网络负担。

每个节点每s会执行10次ping，每次会选择 5 个最久没有通信的其它节点。若发现某节点通信延时达到 

cluster_node_timeout / 2

则立即发ping，避免数据交换延时过长，落后太久。如两个节点都 10 min未交换数据，则整个集群处严重元数据不一致，就有问题。所以 `cluster_node_timeout` 可调节，调得较大，则会降低ping频率。

每次ping会带上自己节点的信息，还有带上1/10其它节点的信息，发出去进行交换。至少包含 `3` 个其它节点的信息，最多包含 `总节点数减 2` 个其它节点的信息。

### 分布式寻址算法

- hash 算法（大量缓存重建）
- 一致性 hash 算法（自动缓存迁移）+ 虚拟节点（自动负载均衡）
- redis cluster的hash slot算法

#### hash算法

来个 key，计算 hash 值，然后对节点数取模。然后打在不同master节点。一旦某master节点宕机，所有请求过来，都会基于最新的剩余master节点数取模，去取数据。这会导致**大部分的请求过来，全部无法拿到有效缓存**，导致大量流量涌入DB。

#### 一致性hash算法

一致性 hash 算法将整个 hash 值空间组织成一个虚拟圆环，整个空间按顺时针方向组织，下一步将各个 master 节点（使用服务器的 ip 或主机名）hash。就能确定每个节点在其哈希环位置。

来个 key，计算 hash 值，并确定此数据在环上位置，从此位置沿环顺时针“行走”，遇到第一个master节点就是key所在位置。

若某节点宕机，受影响数据仅是此节点到环空间前一个节点（逆时针方向行走遇到的第一个节点）之间的数据，其它不受影响。

但是，一致性哈希算法在节点太少时，易因节点分布不均匀，造成缓存热点。为解决这种热点问题，一致性hash引入虚拟节点：对每个节点计算多个 hash，每个计算结果位置都放置一个虚拟节点。

![consistent-hashing-algorithm](/images/consistent-hashing-algorithm.png)

#### redis cluster 的 hash slot 算法

redis cluster 有固定`16384` 个 hash slot，对每个 `key` 计算 `CRC16` 值，然后对 `16384` 取模，可获取 key 对应 hash slot。

redis cluster中每个M都持有部分slot，如有3个M，则可能每个 master 持有 5000 多个 hash slot。hash slot 让 node 的增加和移除很简单，增加一个 master，就将其他 master 的 hash slot 移动部分过去，减少一个 master，就将它的 hash slot 移动到其他 master。移动 hash slot 的成本很低。客户端的 api，可对指定数据，让他们走同一hash slot，通过 `hash tag` 实现。

任一机器宕机，不影响另两个节点。因为key找的是 hash slot，而不是机器。

![hash-slot](/images/hash-slot.png)

## 高可用与主备切换原理

类似哨兵。

#### 判断节点宕机

跟哨兵几乎一样：sdown，odown：

- 若某节点认为另一个节点宕机，那就是 `pfail`，**主观宕机**
- 多节点都认为另一节点宕机，那就是 `fail`，**客观宕机**

在 `cluster-node-timeout` 内，某节点一直没有返回 `pong`，就被认为 `pfail`。

若一节点认为某节点 `pfail` ，则会在 `gossip ping` 消息中，`ping` 给其他节点，超过半数节点都认为 `pfail` ，就会变成 `fail`。

#### 从节点过滤

对宕机M，从其所有S中，选择一个切换成M。

检查每个S与M断开连接的时间，若超过

 `cluster-node-timeout * cluster-slave-validity-factor`

就无资格切换成 `master`。

#### 从节点选举

每个从节点，都根据自己对M复制数据的 offset，来设置一个选举时间，offset越大（复制数据越多）的从节点，选举时间越靠前，优先选举。

所有M开始S选举投票，给要进行选举的 slave 进行投票，若大部分 master node`（N/2 + 1）`都投票给某从节点，则选举通过，那个从节点可以切换成 master。

从节点执行主备切换，从节点切换为主节点。

#### V.S 哨兵

整个流程类似哨兵，所以redis cluster功能强大，直接集成replication和sentinel功能。



去中心化的架构不存在统一配置中心。Redis Cluster的配置信息交互通过Redis Cluster Bus完成（独立端口）。Redis Cluster Bus交互的信息数据结构如下：
![](https://img-blog.csdnimg.cn/img_convert/39bfd254ec30336cd212b6ee607d9502.png)
clusterMsg 中的`type`指明消息类型，配置信息的一致性主要依靠PING/PONG。每个节点向其他节点频繁的周期性发送PING/PONG。
对于消息体中的  `Gossip`部分，包含了sender/receiver 所感知的其他节点信息，接受者根据这些Gossip 跟新对集群的认识。

对大规模集群，若每次PING/PONG 都携带着所有节点的信息，则网络开销会很大。此时Redis Cluster 在每次PING/PONG，只包含了随机的一部分节点信息。由于交互比较频繁，短时间的几次交互之后，集群的状态也会达成一致。
## 2.3 一致性的达成
当Cluster 结构不发生变化时，各个节点通过gossip 协议在几轮交互之后，便可以得知Cluster的结构信息，达到一致性的状态
但是当`集群结构发生变化时（故障转移/分片迁移等）`，优先得知变更的节点通过`Epoch变量`，将自己的最新信息扩散到Cluster，并最终达到一致。

clusterNode 的Epoch描述的单个节点的信息版本；

clusterState 的`currentEpoch `描述的是集群信息的版本，它可以辅助Epoch 的自增生成
因为currentEpoch 是维护在每个节点上的，在集群结构发生变更时，Cluster 在一定的时间窗口控制更新规则，来保证每个节点的currentEpoch都是最新的
### 更新规则
- 当某个节点率先知道了变更时，将自身的`currentEpoch` 自增，并使之成为集群中的最大值
再用自增后的`currentEpoch` 作为新的`Epoch` 版本
- 当某个节点收到了比自己大的`currentEpoch`时，更新自己的`currentEpoch`
- 当收到Redis Cluster Bus 消息中的某个节点的`Epoch` > 自身的时，将更新自身的内容
- 当Redis Cluster Bus 消息中，包含了自己没有的节点时，将其加入到自身的配置中

上述规则保证了`信息的更新是单向的`，最终`朝着Epoch更大的信息收敛`。同时`Epoch`也随`currentEpoch`增加而增加，最终将各节点信息趋于稳定。
## sharding
不同节点分组服务于相互无交集的分片（sharding）
`Redis Cluster 不存在单独的proxy或配置服务器`
所以需要将客户端路由到目标的分片
### 1 数据分片（slot）
Redis Cluster 将所有的数据划分为16384 `[0-16383]` 个分片，每个分片负责其中一部分。
每条数据（key/value）根据K值，通过数据分布算法（`一致性哈希`）映射到16384 个slot中的一个。

数据分布算法
```bash
slotId = crc16(key) % 16384
```
客户端根据 slotId 决定将请求路由到哪个Redis 节点。
`Cluster 不支持跨节点的单命令`，如：sinterstore，若涉及的2个K对应的slot 在不同Node，则执行失败。

通常Redis的K带有业务意义，如：
- Product:Trade:20180890310921230001
- Product:Detail:20180890310921230001

当在集群中存储时，上述同一商品的交易和详情可能会存储在不同的节点上，进而对于这2个K `不能以原子的方式操作`。
为此，Redis引入`HashTag`，使得数据分布算法可以根据key 的某一部分进行计算，让相关的2 条记录落到同一个数据分片

```bash
商品交易记录key：Product:Trade:{20180890310921230001}
商品详情记录key：Product:Detail:{20180890310921230001}
```
Redis 会根据 {} 之间的字符串作为数据分布式算法的输入。
### 客户端的路由
Redis Cluster的客户端相比单机Redis 需要`具备路由语义的识别能力，且具备一定的路由缓存能力`。

当Client 访问的K不在当前Redis节点的slots中，Redis 会返回给Client一个moved命令。并告知其正确路由信息
![](https://img-blog.csdnimg.cn/img_convert/d3c3227dba6a3ec566232b2d5a575595.png)
Client接收到moved 后，再次请求新的Redis时，此时Cluster结构又可能发生了变化。此时有可能再次返回moved。
Client 会根据moved响应，更新其内部的路由缓存信息，以便后续操作直接找到正确节点，减少交互次数。

当Cluster在数据重新分布过程中时，可通过ask命令控制客户端的路由，如下所示：
![](https://img-blog.csdnimg.cn/img_convert/d591147dfd11b1b39e0d4111ca03105d.png)
​​​​上图中，slot1 需迁移到新节点，此时若客户端已完成迁移的K，节点将相应ask告知客户端想目标节点重试。
#### ask V.S moved
- moved 会更新Client数据路由
- ask 只是重定向新节点，但是后续相同slot仍会路由到旧节点

迁移的过程可能会持续一段时间，这段时间某个slot的数据，同时可能存在于新旧 2 个节点。
由于`move 操作会使Client 的路由缓存变更`，若新旧节点对迁移中的slot 所有key 都回应moved，客户端的路由缓存会频繁变更。`因此引入ask 类型消息，将重定向和路由缓存分离`。
### 3 分片的迁移
在一个稳定的Redis Cluster 中，每个slot 对应的节点都是确定的。在某些情况下，节点和分片需要变更：

新的节点作为master加入；

某个节点分组需要下线；

负载不均衡需要调整slot 分布。

此时需要进行分片的迁移，迁移的触发和过程控制由外部系统完成。Redis Cluster 只提供迁移过程中需要的原语，包含下面 2 种：

```
节点迁移状态设置：迁移前标记源/目标节点。
key迁移的原子化命令：迁移的具体步骤。
```

![image.gif](https://img-blog.csdnimg.cn/img_convert/3c77cb5d805f05b6259b6c1e4d40ac0c.png)
### slot 1 从节点A 迁移到B的过程
![image](https://img-blog.csdnimg.cn/img_convert/d78185f87b615ff07ead2b7bea1a2652.png)

1、向节点B发送状态变更命令，将B的对应slot 状态置为importing。

2、向节点A发送状态变更命令，将A对应的slot 状态置为migrating。

3、针对A上的slot 的所有key，分别向A 发送migrate 命令，告知A 将对应的key 迁移到B。

当A节点的状态置为migrating 后，表示对应的slot 正在从A迁出，为保证该slot 数据的一致性。A此时提供的写服务和通常状态下有所区别，对于某个迁移中的slot：

```
如果Client 访问的key 尚未迁出，则正常的处理该key；

如果key已经迁出或者key不存在，则回复Client ASK 信息让其跳转到B处理；
```

![image.gif](https://img-blog.csdnimg.cn/img_convert/64f2a0313b59f21c2141789d7cd1cd1c.png)

当节点B 状态变成importing 后，表示对应的slot 正在向B迁入。即使B 能对外提供该slot 的读写服务，但是和通常情况下有所区别：

```
当Client的访问不是从ask 跳转的，说明Client 还不知道迁移。有可能操作了尚未迁移完成的，处在A上面的key，如果这个key 在A上被修改了，则后续会产生冲突。

所以对于该slot 上所有非ask 跳转的操作，B不会进行操作，而是通过moved 让Client 跳转至A执行。
```

![image.gif](https://img-blog.csdnimg.cn/img_convert/432fc358be7198218ea93cb88dec68da.png)

这样的状态控制，保证了同一个key 在迁移之前总是在源节点执行。迁移后总是在目标节点执行，从而杜绝了双写的冲突。迁移过程中，新增加的key 会在目标节点执行，源节点不会新增key。使得迁移有界限，可以在某个确定的时刻结束。

单个key 的迁移过程可以通过原子化的migrate 命令完成。对于A/B的slave 节点，是通过主备复制，从而达到增删数据。

当所有key 迁移完成后，Client 通过 cluster setslot 命令设置B的分片信息，从而包含了迁入的slot。设置过程中会让Epoch自增，并且是Cluster 中的最新值。然后通过相互感知，传播到Cluster 中的其他节点。

failover

同Sentinel 一样，Redis Cluster 也具备一套完整的故障发现、故障状态一致性保证、主备切换机制。

1、failover的状态变迁

1）故障发现：当某个master 宕机时，宕机时间如何被集群其他节点感知。

2）故障确认：多个节点就某个master 是否宕机如何达成一致。

3）slave选举：集群确认了某个master 宕机后，如何将它的slave 升级成新的master；如果有多个slave，如何选择升级。

4）集群结构变更：成功选举成为master后，如何让整个集群知道，以更新Cluster 结构信息。

2、故障发现

Redis Cluster 节点间通过Redis Cluster Bus 两两周期性的PING/PONG 交互。当某个节点宕机时，其他Node 发出的PING消息没有收到响应，并且超过一定时间（NODE_TIMEOUT）未收到，则认为该节点故障，将其置为PFAIL状态（Possible Fail）。后续通过Gossip 发出的PING/PONG消息中，这个节点的PFAIL 状态会传播到集群的其他节点。

Redis Cluster 的节点两两通过TCP 保持Redis Cluster Bus连接，当对PING 无反馈时，可能是节点故障，也可能是TCP 链接断开。如果是TCP 断开导致的误报，虽然误报消息会因为其他节点的正常连接被忽略，但是也可以通过一定的方式减少误报。Redis Cluster 通过 预重试机制 排除此类误报：当 NODE_TIMEOUT/2 过去了，但是还未收到响应，则重新连接重发PING 消息，如果对端正常，则在很短的时间内就会有响应。

3、故障确认

对于网络分隔的情况，某个节点（B）并没有故障，但是和A 无法连接，但是和C/D 等其他节点可以正常联通。此时只会有A 将 B 标记为PFAIL 状态，其他节点认为B 正常。此时A 和C/D 等其他节点信息不一致，Redis Cluster 通过故障 确认协议 达成一致。

集群中每个节点都是Gossip 的接收者，A 也会接收到来自其他节点的Gossip 消息，被告知B 是否处于PFAIL 状态。当A收到来气其他master 节点对于 B 的PFAIL 达到一定数量后，会将B的PFAIL 状态升级为 FAIL 状态。表示B 已经确认为故障态，后面会发起slave 选举流程。

A节点内部的集群信息中，对于B的状态从PFAIL 到 FAIL 的变迁，如下图所示：

![image](https://img-blog.csdnimg.cn/img_convert/12ef69f9b621a96026da01ee0084d239.png)
## 4 slave选举
先看配置项，确认该从节点是否有资格能晋升主节点。
再看偏移量，越大说明数据越多，越会成为主节点。
如果相同，看 row_id，小的竞选成功。
上图中，B是A的master，并且B 已经被集群公认是FAIL态了，那么A 发起竞选，期望成为新的master。

如果B有多个slave （A/E/F）都认知到B 处于FAIL 状态了，A/E/F 可能会同时发起竞选。当B的slave 个数 >= 3 时，很有可能产生多轮竞选失败。为了减少冲突的出现，优先级高的slave 更有可能发起竞选，从而提升成功的可能性。这里的优先级是slave的数据最新的程度，数据越新的（最完整的）优先级越高。

slave 通过向其他master发送FAILVOER_AUTH_REQUEST 消息发起竞选，master 收到后回复FAILOVER_AUTH_ACK 消息告知是否同意。slave 发送FAILOVER_AUTH_REQUEST 前会将currentEpoch 自增，并将最新的Epoch 带入到FAILOVER_AUTH_REQUEST 消息中，如果自己未投过票，则回复同意，否则回复拒绝。

5、结构变更通知

当slave 收到过半的master 同意时，会替代B 成为新的master。此时会以最新的Epoch 通过PONG 消息广播自己成为master，让Cluster 的其他节点尽快的更新拓扑结构。

当B 恢复可用之后，它手续爱你仍然认为自己是master，但逐渐的通过Gossip 协议得知A 已经替代了自己，然后降级为A的slave。

可用性和性能

### Redis 集群的一致性保证（guarantee）
Redis 集群不保证数据的强一致性（strong consistency）： 在特定条件下， Redis 集群可能会丢失已经被执行过的写命令。
#### 异步复制
使用异步复制（asynchronous replication）是 Redis 集群可能会丢失写命令的其中一个原因。 
![](https://img-blog.csdnimg.cn/20210505155906190.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

1. 客户端向主节点 B 发送一条写命令
2. B 执行写命令，并向客户端返回命令回复
3. B 将刚执行的写命令复制给它的从节点 B1 、 B2 和 B3

主节点对命令的复制工作发生在返回命令回复之后，因为若每次处理命令请求都需要等待复制操作完成， 则主节点处理命令请求速度将极大降低，必须在性能和一致性之间取舍！
> 如果真的有必要， Redis 集群可能会在未来版本提供同步（synchronou）执行写命令的方法。

#### 网络分裂
Redis 集群另外一种可能会丢失命令的情况是， 集群出现网络分裂（network partition）， 并且一个客户端与至少包括一个主节点在内的少数（minority）实例被孤立。

假设集群包含 A 、 B 、 C 、 A1 、 B1 、 C1 六个节点：A 、B 、C 为主节点， A1 、B1 、C1 分别为三个主节点的从节点， 一个客户端 Z1 。

假设集群中发生网络分裂， 那么集群可能会分裂为两方， 大多数（majority）的一方包含节点 A 、C 、A1 、B1 和 C1 ， 而少数（minority）的一方则包含节点 B 和客户端 Z1 。

在网络分裂期间， 主节点 B 仍然会接受 Z1 发送的写命令：
- 若网络分裂时间很短， 则集群会继续正常运行
- 但若网络分裂出现时间长， 使得大多数一方将 B1 设置为新master， 并使用 B1 代替了原主节点 B ， 则 Z1 发送给主节点 B 的写命令将丢失。

在网络分裂出现期间， Z1 可以向主节点 B 发送写命令的最大时间是有限制的， 这一时间限制称为节点超时时间（node timeout）， 是 Redis 集群的一个重要的配置选项：
- 对于大多数一方来说， 如果一个主节点未能在节点超时时间所设定的时限内重新联系上集群， 那么集群会将这个主节点视为下线， 并使用从节点来代替这个主节点继续工作
- 对于少数一方， 如果一个主节点未能在节点超时时间所设定的时限内重新联系上集群， 那么它将停止处理写命令， 并向客户端报告错误

Redis Cluster 还提供了一些方法可以提升性能和可用性。

1、Redis Cluster的读写分离

对于读写分离的场景，应用对于某些读请求允许舍弃一定的数据一致性，以换取更高的吞吐量。此时希望将读请求交给slave处理，以分担master的压力。

通过分片映射关系，某个slot 一定对应着一个master节点。Client 通过moved 命令，也只会路由到各个master中。即使Client 将请求直接发送到slave上，也会回复moved 到master去处理。

为此，Redis Cluster 引入了readonly 命令。Client 向slave发送该命令后，不再moved 到master处理，而是自己处理，这成为slave的readonly 模式。通过readwrite命令，可以将slave的readonly模式重置。

2、master单点保护

假如Cluster 的初始状态如下所示：

![image](https://img-blog.csdnimg.cn/img_convert/5cc75d8dd375f8f6141ea2c4f0064dd0.png)

上图中A、B两个master 分别有自己的slave，假设A1 发生宕机，结构变为如下所示：

![image](https://img-blog.csdnimg.cn/img_convert/74c1be86ae072939e50eabe92f4d740f.png)

此时A 成为了单点，一旦A 再次宕机，将造成不可用。此时Redis Cluster 会把B 的某个slave （如 B1 ）进行副本迁移，变成A的slave。如下所示：

![image](https://img-blog.csdnimg.cn/img_convert/7114880887b8b1f37258af59b8b31cf8.png)

这样集群中每个master 至少有一个slave，使得Cluster 具有高可用。集群中只需要保持 2*master+1 个节点，就可以保持任一节点宕机时，故障转移后继续高可用。

## 总结

- 小规模的集群建议使用官方的Redis Cluster，在节点数量不多的情况下，各方面表现都不错。
- 再大一些规模的集群，可以考虑使用twemproxy或者Codis这类的基于代理的集群架构，虽然是开源方案，但是已经被很多公司在生产环境中验证过。
- 相比于代理方案，使用定制客户端的方案性能更好，很多大厂采用的都是类似的架构。

这几种集群方案对一些类似于“KEYS”这类的多KEY命令，都没法做到百分百支持。原因很简单，数据被分片了之后，这种多KEY的命令很可能需要跨多个分片查询。当你的系统从单个Redis库升级到集群时，可能需要考虑一下这方面的兼容性问题。

## FAQ

### redis 集群模式下，数据库个数怎么计算的？

在 Redis 集群模式下，讨论数据库个数并不是很有意义。因为 Redis 集群的主要作用是将数据分布到不同的节点上，以实现数据的高可用和高性能。在集群模式下，不同节点上的数据库是相互独立的，不能直接访问其他节点上的数据库。因此，数据库个数的数量并不会对集群的性能和可用性产生直接的影响。相反，更重要的是考虑集群中每个节点的配置和性能，以及如何合理地分配数据和负载，以达到最优的性能和可用性。
