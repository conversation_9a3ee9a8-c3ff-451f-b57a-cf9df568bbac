## 1 moved重定向

![](https://p.ipic.vip/aw0psz.png)

每个节点通信共享Redis Cluster中槽和集群中对应节点的关系。

1. 客户端向Redis Cluster的任一节点发送命令
2. 接收命令的节点再计算自己的槽和对应节点

如果保存数据的槽被分配给当前节点，则去槽中执行命令，并把命令执行结果返回给客户端：

![](https://p.ipic.vip/rkr468.png)

```bash
127.0.0.1:6379> cluster keyslot hello
(integer) 866   # 直接返回
```

键槽计算公式：

```bash
slot = CRC16(key) mod 16384
```

如果保存数据的槽不在当前节点的管理范围内，则向客户端返回moved重定向异常

![](https://p.ipic.vip/74papk.png)

3. 客户端接收到节点返回的结果，如果是moved异常，则从moved异常中获取目标节点的信息
4. 客户端向目标节点发送命令，获取命令执行结果

### 重定向流程

1. 客户端向节点6379发送 'set php best' 命令
2. 节点6379计算键'php'对应槽位9244
3. 槽位9244不在节点6379管理范围内(0~5460)
4. 返回MOVED重定向: 'moved 9244 127.0.0.1:6380'
5. 客户端根据重定向信息连接节点6380
6. 节点6380成功执行命令并返回OK

键槽信息：

```java
CRC16('php') mod 16384 = 9244
```

<mark>客户端不会自动找到目标节点执行命令，需要二次执行</mark>

## 2 ask重定向

集群伸缩时，需数据迁移。

当客户端访问某key，节点告诉客户端key在源节点，再去源节点访问时，却发现key已迁移到目标节点，就会返回ask。

![](/Users/<USER>/Library/Application Support/typora-user-images/image-20250710161135067.png)

1. 客户端向目标节点发送命令，目标节点中的槽已经迁移到其它节点
2. 目标节点会返回ask转向给客户端
3. 客户端向新节点发送Asking命令
4. 再向新节点发送命令
5. 新节点执行命令，把命令执行结果返回给客户端

### 为啥不用MOVED重定向？

虽然MOVED意味着我们认为哈希槽由另一节点永久提供，且应对指定节点尝试下一个查询，所以ASK意味着仅将下一个查询发送到指定节点。

需要这样做，是因为下一个关于哈希槽的查询可能是关于仍在A中的键，因此我们始终希望客户端尝试A，然后在需要时尝试B。由于只有16384个可用的哈希槽中有一个发生，因此集群层面的性能下降可接受。

## 3 moved V.S ask

虽然都是客户端重定向，但是：

- moved：槽已确定转移
- ask：槽还在迁移中

## 4 智能客户端

为追求性能。

### 4.1 设计思路

- 从集群中选一个可运行节点，用`Cluster slots`初始化槽和节点映射
- 将Cluster slots结果映射在本地，为每个节点创建JedisPool，之后即可进行数据读写操作

### 4.2 注意

- 每个JedisPool缓存了slot和节点node的关系
- key和slot：对key进行CRC16，hash后与16383取余得到的结果就是slot槽
- JedisCluster启动时，已知key、slot和node之间关系，可找到目标节点
- JedisCluster对目标节点发送命令，目标节点直接响应给JedisCluster
- 如果JedisCluster与目标节点连接出错，则JedisCluster会知道连接的节点是一个错误的节点
- 此时JedisCluster会随机节点发送命令，随机节点返回moved异常给JedisCluster
- JedisCluster会重新初始化slot与node节点的缓存关系，再向新的目标节点发命令，目标命令执行命令并向JedisCluster响应
- 若命令发送次数超过5次，抛"Too many cluster redirection!"

### 架构设计

基图：

![](https://p.ipic.vip/gr8861.png)

全图：

![](https://p.ipic.vip/1ytnb6.png)
