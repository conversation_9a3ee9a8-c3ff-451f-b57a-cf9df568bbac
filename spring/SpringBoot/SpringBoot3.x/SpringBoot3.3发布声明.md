来看如何从 Spring Boot 3.2 升级

### Jersey 可观测性

Micrometer 1.13 已弃用其 Jersey 支持,有利于其 `jersey-micrometer` 模块。如果您的应用程序使用 Jersey 度量标准,请在升级时添加对 `org.glassfish.jersey.ext:jersey-micrometer` 的依赖项。

为了支持 Jersey 的观测,`MetricsApplicationEventListener` 已被替换为 `ObservationApplicationEventListener`。如果您之前使用了 `JerseyTagsProvider` 来自定义标签,您现在需要实现一个 `JerseyObservationConvention` bean 来执行此操作。

### 删除了 Dropwizard 指标的依赖项管理

已删除了对 Dropwizard 度量标准的依赖项管理。Spring Boot 不直接依赖于 Dropwizard 度量标准,因此不需要特定版本。如果您的应用程序直接依赖于 Dropwizard 指标,请更新您的构建配置以指定满足其需求的版本。

### Prometheus Client 1.x

Spring Boot 3.3 包括对 [Prometheus Client 1.x] 的支持 (https://github.com/prometheus/client_java/releases/tag/v1.0.0)。客户端版本包含一些重大更改，如对导出的指标名称的更改。

在 Prometheus 维护者添加此支持之前，不支持使用 Prometheus Push 网关的 1.x 客户端。

如想继续用 0.x 版本 Prometheus 客户端，请从依赖项中删除 `io.micrometer:micrometer-registry-prometheus` 并添加 `io.micrometer:micrometer-registry-prometheus-simpleclient`。Spring Boot 包含对 simpleclient 的已弃用的自动配置,该配置将在 Spring Boot 3.5.0 中被删除。

### Git Commit ID Maven 插件

该插件已升级到 8.0.x。默认日期格式现为 `yyyy-MM-dd'T'HH:mm:ssXXX`。这提供与 Maven 的可重现构建功能的兼容性。

### 最低要求变化

#### 本机构建工具

如果您使用本机构建工具来使用 GraalVM 构建项目,请确保您使用的是至少 0.10.x 版本的插件。Maven 用户在使用 Spring Boot 父项目时应该会自动获得正确的版本。

Gradle 用户应该在 `plugins` 块中更新插件版本:

```
plugins {
  // ...
  id 'org.graalvm.buildtools.native' version '0.10.2'
  // ...
}
```

有关详细信息,请参阅 [#39068](https://github.com/spring-projects/spring-boot/issues/39068)。

## 新特性与注意事项

### 可观测性改进

现在可以通过属性为简单的、直接的和流式监听器以及 `RabbitTemplate` 启用观测。

已添加了对 Micrometer `@SpanTag` 注解的支持。

已添加了对 Brave 和 OpenTelemetry 的标记字段的支持。已为 Brave 添加了对本地字段的支持。

添加了一个 process `InfoContributor`,可以通过 `management.info.process.enabled=true` 启用。

如果没有明确设置应用程序名称,现在会为 OpenTelemetry 使用 `unknown_service`。这使 Spring Boot 的默认值与 OpenTelemetry 规范一致。

#### Spring for Apache Pulsar

属性 `spring.pulsar.listener.observation-enabled` 和 `spring.pulsar.template.observations-enabled` 将其默认值从 `true` 更改为 `false`。这样做是为了统一 `observation-enabled` 属性,所有这些属性现在都默认为 `false`。如果您依赖 Pulsar 观察并且没有明确启用它们,请将属性 `spring.pulsar.listener.observation-enabled=true` 和 `spring.pulsar.template.observations-enabled=true` 添加到您的配置中以恢复旧行为。

#### Brave 和 Zipkin

Brave 已更新到 6.0 版,Zipkin 已更新到 3.0 版。通过该升级,Spring Boot 中的 Zipkin 支持获得了新功能,例如指定将数据报告给 Zipkin API 的编码方式。有关详细信息,请参阅 [#39049](https://github.com/spring-projects/spring-boot/pull/39049)。

已实现了一个新的基于 JDK `HttpClient` 的 Zipkin 发送器。该发送器仅依赖于 JDK,并且将在 Spring Boot 3.5.0 中成为默认发送器,取代 `WebClient` 和 `RestTemplate` 发送器实现。

### Spring for Apache Pulsar 改进

现在有属性可以在 `spring.pulsar.client.failover` 命名空间下配置 Pulsar 的集群级故障转移。

### Spring Security 改进

如果设置了以下任一属性,现在会自动配置 `JwtAuthenticationConverter` (或 `ReactiveJwtAuthenticationConverter`)：

- `spring.security.oauth2.resourceserver.jwt.authority-prefix`
- `spring.security.oauth2.resourceserver.jwt.principal-claim-name` 
- `spring.security.oauth2.resourceserver.jwt.authorities-claim-name`

### 服务连接

#### 支持 Apache ActiveMQ Artemis

已添加了对 Apache ActiveMQ Artemis 的服务连接支持。Testcontainers 支持使用 `ArtemisContainer` 容器,Docker Compose 支持使用 `apache/activemq-artemis` 镜像。

#### 支持官方 ActiveMQ Classic 镜像

ActiveMQ 服务连接现在支持 `apache/activemq-classic` docker 镜像和 `ActiveMQContainer` testcontainer。

#### 支持 LDAP

已添加了使用 `osixia/openldap` 容器的 LDAP 服务连接支持。

#### Bitnami 容器镜像

除了官方镜像，Spring Boot 的 Docker Compose 支持还将检测和配置来自 [Bitnami](https://bitnami.com/) 的容器，包括 Cassandra、ES、MariaDB、MySQL、MongoDB、PostgreSQL、RabbitMQ 和 Redis 等受支持的技术。

### 虚拟线程

如果上下文中有 `AsyncTaskExecutor` 可用,现在它将在 websocket `ChannelRegistration` 上注册。如果使用虚拟线程,这通常将是一个启用了虚拟线程的 `SimpleAsyncTaskExecutor`,从而使 Websockets 支持虚拟线程。

### 批处理事务管理器

引入了 `@BatchTransactionManager` ，以便配置 Spring Batch 使用自定义事务管理器。有关更多信息,请参见[更新的文档](https://docs.spring.io/spring-boot/3.3/how-to/batch.html#howto.batch.specifying-a-transaction-manager)。

### Base64资源

现可用 `base64:` 前缀将资源加载为 Base64 编码的文本值。对诸如 SSL 证书之类的资源非常有用：

```yaml
spring:
  ssl:
    bundle:
      pem:
        mybundle:
          keystore:
            certificate: "base64:LS0tLS1CRUdJTi..."
            private-key: "base64:QmFnIEF0dHJpYn..."
```

### SBOM Actuator 端点

`spring-boot-actuator` 模块现在包括一个新的 SBOM 端点。默认情况下,该端点将提供 jar 中位于 `META-INF/sbom/bom.json` 或 `META-INF/sbom/application.cdx.json` 的文件。

`spring-boot-parent-starter` POM 中还提供了其他配置,以使 SBOM 插件更易于配置。有关更多详细信息,请参见[文档](https://docs.spring.io/spring-boot/reference/actuator/endpoints.html#actuator.endpoints.sbom)。

### 文档更新

[Spring Boot 文档](https://docs.spring.io/spring-boot/3.3/)已迁移到 Antora,提供了更好的结构以及更好的搜索功能。

### 使用 SNI 自动配置嵌入式 Web 服务器 SSL

在使用 SSL/TLS 配置 Tomcat、Netty 或 Undertow 嵌入式 Web 服务器时,现在可以为每个主机名配置唯一的信任材料以支持服务器名称指示 (SNI)。有关配置选项的更多信息,请参见[更新的文档](https://docs.spring.io/spring-boot/how-to/webserver.html#howto.webserver.configure-ssl)。

### 其他

除了上面列出的更改之外,还有许多小的调整和改进,包括:

- `spring.config.activate.on-cloud-platform=none` 现在将在活动云平台为 `null` 时匹配  
- 添加了一个配置选项,用于在错误响应中包含 "path" 字段。可以通过 `server.error.include-path` 属性控制。它默认为 `always`
- WebFlux `DefaultErrorAttributes` 现在使用 `request.requestPath().value()` 来填充路径错误属性
- 添加了属性 `server.tomcat.threads.max-queue-capacity` 来配置 Tomcat Web 服务器连接器的最大队列大小
- 配置处理器现在会检查 `additional-spring-configuration-metadata.json` 中是否有多余的键,如果遇到任何多余的键就会失败
- 添加了一个名为 `spring.task.execution.pool.shutdown.accept-tasks-after-context-close` 的属性,用于控制 `ThreadPoolTaskExecutor` 在上下文关闭后是否接受任务
- 添加了属性 `server.reactive.session.max-sessions` 以在使用 WebFlux 时控制最大会话数  
- `ExecutionContextSerializer` bean 现在会自动应用于 Spring Batch 配置。如果未提供任何 `ExecutionContextSerializer`,则使用 `DefaultExecutionContextSerializer`

* 四个受支持的 Web 服务器 (Jetty、Netty、Tomcat 和 Undertow) 产生的启动日志消息现在更加一致
* 为 JMS 连接添加了 `client-id` 和 `subscription-durable` 属性
* 如果一个类实现了多个 Servlet 接口,比如 `Filter` 或 `Servlet`,现在它将为所有接口注册 (例如,一个用于过滤器,一个用于 Servlet)
* 新的响应式 `sessions` actuator 端点现在可以为给定的用户名返回会话
* 环境的默认配置文件现在包含在 env actuator 端点的响应中
* 新增属性 `spring.liquibase.ui-service` 指定 Liquibase 使用的默认 UI 服务记录器
* 添加了新属性 `spring.security.saml2.relyingparty.registration.*.name-id-format` 来指定 SAML 注册的 NameID 格式
* 添加了新属性 `server.mime-mappings`,允许配置额外的自定义 MIME 类型映射
* `jakarta.inject:jakarta.inject-api` 现在包含在 Spring Boot 的依赖管理中
* `git-commit-id-maven-plugin` 的日期格式已更改为 `yyyy-MM-dd'T'HH:mm:ssXXX`,以允许可重现构建。详见 #39606
* `spring-boot-starter-jetty` 不再默认包含 `jetty-jndi`。如果需要 JNDI 支持,请将 `org.eclipse.jetty:jetty-jndi` 添加到您的依赖项中
* 在 Windows 上,现在可以使用 `mvn spring-boot:run` 启动应用程序,即使它有极大数量的依赖项。在 3.3.0 之前,由于类路径的长度,它会失败并抛出 "文件名或扩展名太长" 异常
* 新属性 `spring.data.jdbc.dialect` 可用于为 Spring Data JDBC 设置方言。如果未设置,将自动检测方言
* 增加了 `CloudFoundryVcapEnvironmentPostProcessor` 和 `ConfigDataEnvironmentPostProcessor` 的排序间隔
* Cassandra 驱动程序的坐标已从 `com.datastax.oss` 更改为 `org.apache.cassandra`
* 将新属性 `management.observations.long-task-timer.enabled` 设置为 `false` 现在可以防止为每个观测创建 `LongTaskTimer`
* 基础设施角色的 Bean 现在会自动从延迟初始化中排除
* Spring Config 现在在解析属性时使用来自环境的转换服务
* 将新属性 `spring.docker.compose.start.skip` 设置为 `never` 可以始终执行 Docker Compose 启动命令
* 添加了新属性 `spring.graphql.websocket.keep-alive`
* 如果定义了 `ManagedClassNameFilter` bean,JPA 自动配置现在会使用它
* 添加了新属性 `spring.rabbitmq.template.allow-list-patterns`

**Spring Boot 3.3.0 中的弃用内容**

* 支持新的 `ZipkinHttpClientBuilderCustomizer`,取代 `ZipkinRestTemplateBuilderCustomizer` 和 `ZipkinWebClientBuilderCustomizer`  
* 支持 `-Djarmode=tools extract --layers`,取代 `-Djarmode=layertools extract`
* 支持 `-Djarmode=tools list-layers`,取代 `-Djarmode=layertools list`
* 在 `BootJar` 和 `BootWar` 任务上使用 `includeTools`,取代 `layers.includeLayerTools`
* 使用 `<includeTools>…</includeTools>`,取代 `<layers><enabled>…</enabled></layers>`
* 声明 Actuator 端点的 `@ServletEndpoint`、`@ControllerEndpoint` 和 `@RestControllerEndpoint` 注解已被弃用。相反,应用程序应该使用 `@Endpoint` / `@ReadOperation` / `@WriteOperation` 模型来提供端点。参见 #31768
