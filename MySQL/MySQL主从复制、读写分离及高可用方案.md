## 1 从单机到集群

根据云厂商Benchmark结果，4核8G机器运行 MySQL 5.7 时，可支撑TPS 500，QPS 10000。
但随数据量增大，读写并发增加，系统可用性要求提升，单机 MySQL 出现如下危机：

- 容量问题，难以扩容

  考虑数据库拆分、**分库分表**

- 读写压力，QPS 过大，特别是分析类需求会影响到业务事务

  考虑多机集群、**主从复制**

- 高可用性不足，易宕机

  考虑故障转移、MHA/MGR/Orchestrator

- 高峰时数据库连接数经常超过上限

- 一致性问题
考虑分布式事务，X/A 柔性事务

读写分离基于主从复制架构：一主多从，只写主库，主库会自动将数据同步到从库。

### 主从复制的意义

高并发场景下MySQL的一种优化方案，依靠主从复制使得MySQL实现了数据复制为多份，增强了抵抗高并发读请求的能力，提升了MySQL查询性能同时，也提升了数据的安全性。当某一个MySQL节点，无论是主库还是从库故障时，还有其他的节点中存储着全量数据，保证数据不会丢失。

从库其实还有很多其他的应用场景，比如你可以挂一个从库，专门用来跑一些报表SQL 语句，那种SQL语句往往是上百行之多，运行要好几秒，所以可以专门给他一个从库来跑。也可以是专门部署一个从库，让你去进行数据同步之类的操作。

## 2 MySQL主从复制

###  2.1 发展史

2000年，MySQL 3.23.15版本引入复制
2002年，MySQL 4.0.2版本分离 I/O 和 SQL 线程，引入 relay log
2010年，MySQL 5.5版本引入半同步复制
2016年，MySQL 在5.7.17中引入 InnoDB Group Replication

### 2.2 核心

- 主库写 binlog
- 从库 relay log

### 2.4 binlog格式

```sql
# 查看binlog
mysqlbinlog -vv mysql-bin.000005
```
### 2.5 主从复制的实现方式

#### 2.5.1 异步复制

异步复制：经典的主从复制，Primary-Seconda [领域驱动设计与模式实战.pdf](../../../../Downloads/领域驱动设计与模式实战.pdf) ry Replication，2000年MySQL 3.23.15版本引入 Replication。

传统的MySQL复制提供了一种简单的主从复制方案。有一个主（source）并且有一或多个从（replicas）。主数据库execute事务，将其commit，然后将它们异步发给从库，以re-executed（在基于语句的复制中）或apply（在基于行的复制中）。它是一个无共享系统，默认情况下所有服务器都具有数据的完整副本。

MySQL异步复制：
![](https://img-blog.csdnimg.cn/20210202121510248.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)![](https://img-blog.csdnimg.cn/20210202123049502.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

- 首要确保主库和从库的server-id不同
- 其次，主库必须打开binlog功能，这样主库才会写binlog到本地磁盘，接着就能按如下步骤在主库上执行统一的操作

首先在主库上创建一个用于主从复制的账号：

```sql
create user 'backup_user'@'192.168.31.%' identiﬁed by 'backup_123';

grant replication slave on **.** to 'backup_user'@'192.168.31.%';

ﬂush privileges;
```

假设你主库run了一段时间，现在要挂个从库上去，从库总不能把你主库从0开始的所有binlog都拉一遍吧？这是不对的，此时你就应该在业务低峰期，在公司里直接让系统对外不可用，就说是维护状态（头条、银行等软件都是这样），然后对主库和从库做数据备份和导入。

可使用mysqldump工具把主库在这个时刻的数据做一个全量备份，但此时一定不允许系统操作主库，主库的数据此时不能有变动：

```sql
/usr/local/mysql/bin/mysqldump --single-transaction -uroot -proot --master-data=2 -A > backup.sql
```

mysqldump在MySQL安装目录的bin目录，用上述命令就可对你主库所有数据做备份，备份以SQL语句方式进入指定backup.sql文件。执行该文件，就可恢复出跟主库一样数据。

其中：

```sql
--master-data=2
```

即备份SQL文件里，记录此时主库的binlog文件和position号，为主从复制做准备。接着你能通过scp把这个backup.sql文件拷贝到从库服务器。

然后，操作步骤转移到从库上去执行，在从库上执行如下命令，把backup.sql文件里的语句都执行一遍， 这就相当于把主库所有的数据都还原到从库上去了，主库上的所有database、 table以及数据，在从库 里全部都有了。

```sql
# 在从库上执行下面的命令去指定从主库进行复制。

CHANGE MASTER TO MASTER_HOST='**************',

MASTER_USER='backup_user',MASTER_PASSWORD='backup_123',MASTER_LOG_FILE='mysql- bin.000015',MASTER_LOG_POS=1689;
```

上面的master机器的ip地址我们知道， master上用于执行复制的用户名和密码是我们自己创建的，但master的binlog文件和position如何知晓？

这不就是之前我们 mysqldump导出的backup.sql里就有，大家在执行上述命令前，打开那个backup.sql就可以看到：

```sql
MASTER_LOG_FILE='mysql-bin.000015',MASTER_LOG_POS=1689
```

然后你就把上述内容写入到主从复制的命令里去。

接着执行开始进行主从复制的命令： 

```sql
start slave
```

再用

```sql
show slave status
```

查看一下主从复制的状态，主要看到Slave_IO_Running和Slave_SQL_Running都是Yes就说明一切正常了，主从开始复制了。

接着就能在主库插入一条数据，然后在从库查询这条数据，只要能够在从库查到这条数据，就说明主从复制已经成功。

这是最简单的一种主从复制：异步复制，从库异步拉取binlog来同步，所以肯定会出现短暂的主从不一致，比如你在主库刚插入数据，结果在从库立马查询，可能查不到。

### 优点

简单
### 缺点
- 网络或机器故障，会造成数据不一致

SQL的每个增删改的会改变数据的操作，除了会更新数据外，对这个增删改操作还会写入一个日志文件，记录这个操作的日志，即binlog。

MySQL 5.7新版本的并行复制，多个SQL线程，每个线程从relay日志里读一个库的日志，重放。

从库同步主库数据的过程是**串行化**的，即主库上并行的操作，在从库会串行执行。
由于【从库】从【主库】拷贝日志及串行执行SQL的特点，在高并发下就有延时，从库数据一定比主库慢。
所以经常出现，刚写入主库的数据读不到，要过几十甚至几百ms才能读到。
从库串行化过程：

1. 读取binlog日志
2. 写relay日志、应用日志变更到自己本地数据

从库的I/O线程，读取主库的binlog日志时，老版本是单线程，5.6.x之后的新版本改为多线程读取。

若主库宕机时，恰好数据还没同步到从库，则有些数据可能在从库上没有，可能就这么丢失。

所以MySQL实际上在这有两个机制：

#### 2.5.2 半同步复制（生产常用）

半同步：你主库写入数据，日志进入binlog之后，起码得确保  binlog日志复制到从库了，你再告诉客户端说本次写入事务成本了是不是？起码你主库突然宕机，但他之前写入成功的数据的binlog日志都到从库了，从库切换为主库，数据也不会丢，这就是所谓的半同步。

它向协议添加了一个同步步骤。这意味着主库在提交时，等待从库确认已接收到事务。只有这样，主库才会恢复提交操作。

半同步复制有两种方式：

- AFTER_COMMIT，非默认，主库写入日志到binlog，等待binlog复制到从库了，主库就提交自己的本地事务，接着等待从库返回给自己一个成功的响应，主库再返回提交事务成功的响应给客户端
- MySQL  5.7默认方式，主库把日志写入binlog，并且复制给从库，然后开始等待从库的响应，从库返回说成功给主库了，主库再提交事务，接着返回提交事务成功的响应给客户端

这种方式可保证你每个事务提交成功前，binlog日志一定都复制到从库了，所以只要事务提交成功，就可认为数据在从库肯定有备份了，即使主库崩溃，已提交的事务的数据绝对不会丢。

### 搭建半同步复制

在之前搭建好异步复制的基础之上，安装一下半同步复制插件即可，先在主库中安装半同步复制插件，同时还得开启半同步复制功能：

```sql
install plugin rpl_semi_sync_master soname 'semisync_master.so'; set global rpl_semi_sync_master_enabled=on;

show plugins; 
```

可以看到你安装了这个插件，就ok。

接着在从库也是安装这个插件以及开启半同步复制功能：

```sql
install plugin rpl_semi_sync_slave soname 'semisync_slave.so'; set global rpl_semi_sync_slave_enabled=on;

show plugins; 
```

接着要重启从库的IO线程：

```sql
stop slave io_thread;
start slave io_thread;
```

然后在主库上检查一下半同步复制是否正常运行：

```sql
show global status like '%semi%';
```

若看到：

```sql
Rpl_semi_sync_master_status=ON
```

就行了。

到此，半同步复制开启成功，一般主从复制都建议做成半同步复制，这样配合高可用切  换机制，就能保证数据库有一个在线的从库热备份主库的数据了，而且主要主库宕机，从库立马切换为主库，数据不会丢失，数据库还高可用。

2010 年引入Semisynchronous Replication，5.5 可用，解决主库数据丢失问题，保证主从的最终一致性：
![](https://img-blog.csdnimg.cn/20210202122803481.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)
![](https://img-blog.csdnimg.cn/20210202123017251.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

1. 主库写入binlog日志后，会强制立即将数据同步到从库
2. 从库将日志写入自己的relay log后，会返回ack给主库
3. 主库接收到至少一个从库的ack后，才认为写操作完成

图中即为经典的异步MySQL复制协议（及其半同步变量）。不同实例之间的箭头表示服务器之间交换的消息或服务器与客户端应用程序之间交换的消息。

#### GTID

更简便的搭建方式。

先在主库配置：

```properties
gtid_mode=on
enforce_gtid_consistency=on
log_bin=on
server_id=单独设置一个
binlog_format=row
```

接着在从库进行配置：

```bash
gtid_mode=on enforce_gtid_consistency=on log_slave_updates=1

server_id=单独设置一个 
```

在主库创建好用于复制的账号后，就能和之前一样操作，比如在主库dump出一份数据，在从库里导入这份数据，利用mysqldump备份工具做的导出，备份文件里会有

```sql
SET @@GLOBAL.GTID_PURGED=***
```

一类的字样，照着执行即可。 其余步骤都和之前类似，最后执行

```sql
show master status
```

可见executed_gtid_set， 里面记录执行过的GTID，接着执行：

```sql
select * from gtid_executed
```

![](https://img-blog.csdnimg.cn/44829339c6cb4d90bb02b10e87b80ce9.png?x-oss-process=image/watermark,type_d3F5LXplbmhlaQ,shadow_50,text_SmF2YUVkZ2U=,size_14,color_FFFFFF,t_70,g_se,x_16)

对比一下，就会发现对应上了。此时就说明开始GTID复制了。

无论是GTID复制 or 传统复制，都很简单，MyCat或Sharding-Sphere也不难，照着文档做，整合到Java代码里去，就能做出基于主从复制的读写分离效果。那些中间件都支持读写分离模式。落地到项目里，就完成了一个主从架构和读写分离架构。若你的数据库之前对一个库的读写请求每秒总共是2000，此时读写分离后，也许就对主库每秒写TPS 才几百，从库的读QPS是1000多。那万一你要是从库的读QPS越来越大，达到了每秒几千，此时你是不是会压力很大？没关系，这个时候你可以给主库做更多的从库，搭建从库，给他挂到主库上去，每次都在凌晨搞，先让系统停机，对外不使用，数据不更新。

接着对主库做个dump，导出数据，到从库导入数据，做一堆配置，然后让从库开始接着某个时间点开 始继续从主库复制就可以了，一旦搭建完毕，就等于给主库挂了一个新的从库上去，此时继续放开系统  的对外限制，继续使用就可以了，整个过程基本在1小时以内。如果在凌晨比如2点停机1小时，基本对业务无影响。

## 3 组复制

2016年引入，5.7 开始，启用插件。
![](https://img-blog.csdnimg.cn/20210202125837266.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

基于 Paxos 协议实现的组复制，保证数据一致性。


组复制是一种可用于实施容错系统的技术。复制组是一组服务器，每个服务器都有自己的完整数据副本（无共享复制方案），并通过消息传递相互交互。通信层提供一组保证，如原子消息和总订单消息传递。可转化为有用的抽象，构建更高级的数据库复制解决方案。

MySQL组复制基于这些属性和抽象，并在所有复制协议中实现多源更新。一个复制组由多服务器组成，该组中的每个服务器可随时独立执行事务。但所有读写事务只有在组批准后才提交。即对任何读写事务，组都需要决定是否提交，因此提交操作不是来自原始服务器的单方面决定。只读事务无需组内的任何协调即可立即提交。

当读写事务准备好在原始服务器提交时，服务器自动广播：

- 写值（已更改的行）
- 相应的写集（已更新的行的唯一标识符）

由于事务通过原子广播发送，该组所有服务器都将接收该事务，否则将不会。如收到了，那相对之前发送的其他事务，他们都将以相同顺序收到它。因此，所有服务器都以相同顺序接收相同的事务集，并为事务建立全局总顺序。

但是，在不同服务器上同时执行的事务之间可能存在冲突。通过在称为认证的过程中检查并比较两个不同并发事务的写集，可以检测到此类冲突。在认证过程中，冲突检测是在行级别执行的：如果在不同服务器上执行的两个并发事务更新同一行，则存在冲突。冲突解决过程指出，已首先订购的事务在所有服务器上提交，而已订购第二的事务中止，因此在原始服务器上回滚并由组中的其他服务器丢弃。例如，如果t1和t2在不同的站点上同时执行，都更改了同一行，并且t2在t1之前排序，则t2赢得了冲突，并且t1被回滚。这实际上是一个分布式的首次提交胜出规则。请注意，如果两个事务之间的冲突经常发生，那么在同一个服务器上启动它们是一个好习惯，在那里，它们有机会在本地锁管理器上进行同步，而不必由于认证而回滚。

对于应用和外部化已认证的交易，如果不破坏一致性和有效性，组复制允许服务器偏离交易的约定顺序。组复制是最终的一致性系统，这意味着一旦传入流量减慢或停止，所有组成员将具有相同的数据内容。当流量在流动时，可以按略有不同的顺序对事务进行外部化，或者对某些成员先进行外部化。例如，在多主要模式下，尽管尚未应用全局顺序中较早的远程事务，但是本地事务可能会在认证后立即被外部化。当证明过程确定交易之间没有冲突时，这是允许的。在单主模式下，在主服务器上，并发，无冲突的本地事务以与组复制所同意的全局顺序不同的顺序进行提交和外部化的可能性很小。在不接受来自客户端的写操作的辅助服务器上，事务始终按照约定的顺序进行提交和外部化。



MySQL组复制协议，通过将其与MySQL复制（甚至MySQL半同步复制）比较，可看到区别。为清楚起见，此图缺少一些基本的共识和Paxos相关的消息。
![](https://img-blog.csdnimg.cn/20210202123232152.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

## 配置MySQL的主从同步？

当客户端提交一个事务到MySQL集群，直到客户端收到集群返回成功，在这过程中，MySQL集群要执行很多操作：主库提交事务、更新存储引擎中的数据、把Binlog写到磁盘、给客户端返回响应、把Binlog复制到所有从库、每个从库把复制来的Binlog写到暂存日志、回放这Binlog、更新存储引擎中的数据、给主库返回复制成功的响应。

这些操作时序非常重要，同样的操作，因为时序不同，对应用程序来说，有很大差异。若先复制Binlog，等Binlog复制到从节点后，主节点再提交事务，从节点的Binlog一直和主节点同步，任何情况下主节点宕机也不会丢数据。但把这时序倒过来，先提交事务再复制Binlog，性能就非常好，但存在丢数据风险。

MySQL提供参数配置这时序。默认情况下，MySQL采用异步复制，执行事务操作的线程不会等复制Binlog的线程：

![img](https://static001.geekbang.org/resource/image/63/3f/6359155a64c1a62cb5fe23f10946d23f.jpg)

MySQL主库在收到客户端提交事务的请求后，会先写Binlog，再提交事务，更新存储引擎中的数据，事务提交完成后，给客户端返回操作成功的响应。

从库有个专门复制线程，从主库接收Binlog，然后把Binlog写到一个中继日志里面，再给主库返回复制成功的响应。

从库还有另外一个回放Binlog的线程，读中继日志，然后回放Binlog更新存储引擎中的数据。**提交事务和复制这两个流程在不同的线程中执行，互不等待，即异步复制。**

异步复制的情况下，为什么主库宕机存在丢数据的风险？为什么读写分离存在读到脏数据的问题？都因为**异步复制没法保证数据能第一时间复制到从库。**

同步复制的时序和异步复制唯一区别，何时给客户端返回响应：

- 异步复制时，主库提交事务之后，就会给客户端返回响应
- 同步复制时，主库在提交事务时，会等待数据复制到所有从库之后，再给客户端返回响应

同步复制基本不用：

- 性能差，要复制到所有节点才返回响应
- 可用性差，主库和所有从库任何一个数据库出问题，都会影响业务

为解决这问题，MySQL 5.7增加半同步复制（Semisynchronous Replication）：

- 异步复制是，事务线程完全不等复制响应
- 同步复制是，事务线程要等待所有的复制响应
- 半同步复制介于二者之间，事务线程不用等着所有的复制成功响应，只要一部分复制响应回来之后，就可以给客户端返回

如一主二从集群，配置成半同步复制，只要数据成功复制到任一从库，主库事务线程就直接返回。这兼顾异步复制、同步复制优点。若主库宕机，至少还有一个从库有最新数据，不存在丢数据风险。半同步复制性能凑合，也提供高可用，从库宕机不影响主库提供服务。

配置半同步复制重要参数“rpl_semi_sync_master_wait_no_slave”，“至少等待数据复制到几个从节点再返回”。这个数量配置的越大，丢数据风险越小，但是集群的性能和可用性就越差。最大可以配置成和从节点的数量一样，变成同步复制。

默认值1够了，性能损失最小，可用性也高，只要还有一个从库活，就不影响主库读写。丢数据风险也不大，只有在恰好主库和那个有最新数据的从库一起坏掉的情况下，才可能丢数据。

“rpl_semi_sync_master_wait_point”控制主库执行事务的线程：

- 是在提交事务前（AFTER_SYNC）等待复制
- 还是在提交事务后（AFTER_COMMIT）等待复制

默认AFTER_SYNC，即先等待复制，再提交事务，这样完全不丢数据。AFTER_COMMIT具有更好性能，不会长时锁表，但存在宕机丢数据风险。

虽配置同步或半同步复制，并等待复制成功后再提交事务，还有特别容易被忽略、可能存在丢数据风险case。

若主库提交事务的线程等待复制的时间超时，事务仍会被正常提交。MySQL会自动降级为异步复制，直到有足够多（rpl_semi_sync_master_wait_no_slave）的从库追上主库，才能恢复成半同步复制。若期间主库宕机，仍存在丢数据风险。

## 4 复制状态机：分布式存储都复制数据方案

MySQL无论复制or备份恢复，都依赖全量备份和Binlog：

- 全量备份，备份那刻的一个数据快照
- Binlog记录每次数据更新的变化，即操作日志

主从同步就是数据复制：“快照+操作日志”，如Redis Cluster全量备份称为Snapshot，操作日志叫backlog，主从复制方式几乎和MySQL一样。

Elasticsearch是个内存数据库，读写都在内存，怎么保证数据可靠性？translog，它备份和恢复数据的原理和实现方式也一样。**几乎所有的存储系统和数据库，都用这套解决备份恢复和数据复制问题**。

这套方法叫[复制状态机(Replication State Machine)](https://en.wikipedia.org/wiki/State_machine_replication)，出处1978年Lamport的一篇论文[《The Implementation of Reliable Distributed Multiprocess Systems》](http://lamport.azurewebsites.net/pubs/implementation.pdf)。

## 3 主从复制缺点及解决方案

### 3.1 主从延迟

- 只能数据分片，把数据量做小

#### 主从同步适用场景

推荐在读 >> 写，且读时对数据时效性要求不高时采用。所以可以考虑用MySQL的并行复制，但问题是那是库级别的并行，所以有时作用不是很大。

### 3.2 应用侧需配合读写分离框架

#### 读写分离

借助于主从复制，我们现在有了多个 MySQL 服务器示例。如果借助这个新的集群，改进我们的业务系统数据处理能力？

最简单的就是配置多个数据源，实现读写分离
##### 动态切换数据源

1. 基于 Spring/Spring Boot，配置多个数据源(例如2个，master 和 slave)
2. 根据具体的 Service 方法是否会操作数据，注入不同的数据源，1.0版本

优化：
1.1：基于操作 AbstractRoutingDataSource 和自定义注解 readOnly 之类的，简化自动切换数据源
1.2：支持配置多个从库
1.3：支持多个从库的负载均衡
![](https://img-blog.csdnimg.cn/****************.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

#### 框架
“动态切换数据源”版问题：
- 代码侵入性强
- 降低侵入性会导致”写后立即读”不一致问题
写时（还没同步到从库），立马读（从库），导致你 insert 数据后去查却查不到！

改进方式，ShardingSphere-jdbc 的 Master-Slave 功能
1）SQL 解析和事务管理，自动实现读写分离
2）解决”写完读”不一致的问题
只要一个事务中解析到有写，所有读都读主库，而无需我们业务代码处理。

#### 数据库中间件
“框架版本”的问题？
- 对业务系统还是有侵入
- 对已存在的旧系统改造不友好

优化方案：MyCat/ShardingSphere-Proxy 的 Master-Slave 功能
- 需要部署一个中间件，规则配置在中间件
- 模拟一个 MySQL 服务器，对业务系统无侵入

但是该方案需要单独部署中间件，需要运维成本和领导审批，所以一般开发人员使用框架方案。

### 3.3 无法高可用

> 参考 
> - https://dev.mysql.com/doc/refman/5.7/en/group-replication-primary-secondary-replication.html

## 总结

任何存储系统，无论存储什么数据，用什么数据结构，都可抽象成一个状态机。存储系统中的数据称为状态（即MySQL中的数据），状态的全量备份称为快照（Snapshot）。

按序记录更新存储系统的每条操作命令就是操作日志（Commit Log，即MySQL中的Binlog）：

![img](https://static001.geekbang.org/resource/image/83/7a/83e34a8b9d4f81391e327172e5a2497a.jpg)

复制数据的时候，只要基于一个快照，按序执行快照后的所有操作日志，就可得到一个完全一样的状态。在从节点持续地从主节点上复制操作日志并执行，就可以让从节点上的状态数据和主节点保持同步。

主从同步做数据复制时，一般可以采用几种复制策略。

性能最好异步复制，主节点上先记录操作日志，再更新状态数据，然后异步把操作日志复制到所有从节点上，并在从节点执行操作日志，得到和主节点相同的状态数据。

异步复制劣势：存在主从延迟，若主节点宕机，可能丢数据。

半同步复制，主节点等待操作日志最少成功复制到N个从节点上之后，再更新状态，这种方式在性能、高可用和数据可靠性几个方面都比较平衡，很多分布式存储系统默认采用。

## FAQ 

复制状态机除了用于数据库的备份和复制以外，在计算机技术领域，还有哪些地方也用到了复制状态机？



系统实施读写分离的具体方案是什么样的？比如，如何分离读写数据库请求？如何解决主从延迟带来的数据一致性问题？

分离读写请求大多数采用代理或Sharding-JDBC。解决主从延迟，没有完全避免延迟的方法，但至少要做到能够监控主从延迟，当延迟太大的时候，采用一些降级方案。如把重要业务的读请求切回主库，暂停一些不重要业务，甚至限流等。
