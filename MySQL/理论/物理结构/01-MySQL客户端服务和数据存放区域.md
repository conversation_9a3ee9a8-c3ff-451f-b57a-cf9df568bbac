MySQL 报错执行日志：
```bash
2024-11-30T15:15:36.211256Z 0 [Warning] [MY-011070] [Server] 'binlog_format' is deprecated and will be removed in a future release.
2024-11-30T15:15:36.212452Z 0 [System] [MY-010116] [Server] /usr/local/Cellar/mysql/9.0.1_6/bin/mysqld (mysqld 9.0.1) starting as process 30174
2024-11-30T15:15:36.241549Z 0 [Warning] [MY-010159] [Server] Setting lower_case_table_names=2 because file system for /usr/local/var/mysql/ is case insensitive
2024-11-30T15:15:36.298691Z 1 [System] [MY-013576] [InnoDB] InnoDB initialization has started.
2024-11-30T15:15:36.757887Z 1 [System] [MY-013577] [InnoDB] InnoDB initialization has ended.
2024-11-30T15:15:36.783117Z 1 [ERROR] [MY-014060] [Server] Invalid MySQL server upgrade: Cannot upgrade from 80100 to 90001. Upgrade to next major version is only allowed from the last LTS release, which version 80100 is not.
2024-11-30T15:15:36.783298Z 0 [ERROR] [MY-010020] [Server] Data Dictionary initialization failed.
2024-11-30T15:15:36.783324Z 0 [ERROR] [MY-010119] [Server] Aborting
2024-11-30T15:15:36.830849Z 0 [System] [MY-010910] [Server] /usr/local/Cellar/mysql/9.0.1_6/bin/mysqld: Shutdown complete (mysqld 9.0.1)  Homebrew.
2024-11-30T15:15:36.830884Z 0 [System] [MY-015016] [Server] MySQL Server - end.
2024-11-30T15:17:23.710189Z 0 [System] [MY-015015] [Server] MySQL Server - start.
2024-11-30T15:17:24.070537Z 0 [Warning] [MY-011070] [Server] 'binlog_format' is deprecated and will be removed in a future release.
2024-11-30T15:17:24.071938Z 0 [System] [MY-010116] [Server] /usr/local/Cellar/mysql/9.0.1_6/bin/mysqld (mysqld 9.0.1) starting as process 32307
2024-11-30T15:17:24.078866Z 0 [Warning] [MY-010159] [Server] Setting lower_case_table_names=2 because file system for /usr/local/var/mysql/ is case insensitive
2024-11-30T15:17:24.139427Z 1 [System] [MY-013576] [InnoDB] InnoDB initialization has started.
2024-11-30T15:17:24.815908Z 1 [System] [MY-013577] [InnoDB] InnoDB initialization has ended.
2024-11-30T15:17:24.860580Z 1 [ERROR] [MY-014060] [Server] Invalid MySQL server upgrade: Cannot upgrade from 80100 to 90001. Upgrade to next major version is only allowed from the last LTS release, which version 80100 is not.
2024-11-30T15:17:24.861072Z 0 [ERROR] [MY-010020] [Server] Data Dictionary initialization failed.
2024-11-30T15:17:24.861415Z 0 [ERROR] [MY-010119] [Server] Aborting
2024-11-30T15:17:24.927292Z 0 [System] [MY-010910] [Server] /usr/local/Cellar/mysql/9.0.1_6/bin/mysqld: Shutdown complete (mysqld 9.0.1)  Homebrew.
2024-11-30T15:17:24.927363Z 0 [System] [MY-015016] [Server] MySQL Server - end.
javaedge@JavaEdgedeMac-mini web % 
```

现在问题明确了。这是一个版本升级冲突的问题：

```bash
[ERROR] [MY-014060] [Server] Invalid MySQL server upgrade: Cannot upgrade from 80100 to 90001. Upgrade to next major version is only allowed from the last LTS release, which version 80100 is not.
```

从之前的检查结果来看，您系统中确实只安装了 MySQL 9.0.1 版本（在 /usr/local/Cellar/mysql/9.0.1_6），而没有 8.1 版本的二进制文件。

虽然您的数据目录（/usr/local/var/mysql/）中是 8.1 版本的数据，但实际的 MySQL 程序已经被更新到了 9.0.1。

所以我们确实需要：

1. 先卸载当前的 9.0.1：

```bash
brew unlink mysql
```

1. 安装 8.0 版本：

```bash
$ brew install mysql@8.0
==> Auto-updating Homebrew...
Adjust how often this is run with HOMEBREW_AUTO_UPDATE_SECS or disable with
HOMEBREW_NO_AUTO_UPDATE. Hide these hints with HOMEBREW_NO_ENV_HINTS (see `man brew`).
==> Auto-updated Homebrew!
Updated 2 taps (homebrew/core and homebrew/cask).
==> New Formulae
aws-c-auth           aws-c-event-stream   aws-c-s3             ducker
aws-c-cal            aws-c-http           aws-c-sdkutils       imgp
aws-c-common         aws-c-io             aws-checksums        victorialogs
aws-c-compression    aws-c-mqtt           aws-crt-cpp
==> New Casks
font-agu-display                          font-playwrite-fr-trad-guides
font-badeen-display                       font-playwrite-gb-j-guides
font-playwrite-ar-guides                  font-playwrite-hr-guides
font-playwrite-at-guides                  font-playwrite-hr-lijeva-guides
font-playwrite-au-nsw-guides              font-playwrite-hu-guides
font-playwrite-au-qld-guides              font-playwrite-in-guides
font-playwrite-au-sa-guides               font-playwrite-is-guides
font-playwrite-au-tas-guides              font-playwrite-it-moderna-guides
font-playwrite-au-vic-guides              font-playwrite-it-trad-guides
font-playwrite-be-vlg-guides              font-playwrite-mx-guides
font-playwrite-be-wal-guides              font-playwrite-ng-modern-guides
font-playwrite-br-guides                  font-playwrite-nl-guides
font-playwrite-ca-guides                  font-playwrite-no-guides
font-playwrite-cl-guides                  font-playwrite-nz-guides
font-playwrite-co-guides                  font-playwrite-pe-guides
font-playwrite-cu-guides                  font-playwrite-pl-guides
font-playwrite-cz-guides                  font-playwrite-pt-guides
font-playwrite-de-grund-guides            font-playwrite-ro-guides
font-playwrite-de-la-guides               font-playwrite-sk-guides
font-playwrite-de-sas-guides              font-playwrite-tz-guides
font-playwrite-de-va-guides               font-playwrite-us-modern-guides
font-playwrite-dk-loopet-guides           font-playwrite-us-trad-guides
font-playwrite-dk-uloopet-guides          font-playwrite-vn-guides
font-playwrite-es-deco-guides             font-playwrite-za-guides
font-playwrite-es-guides                  sys-pc-tool
font-playwrite-fr-moderne-guides          windsurf

You have 25 outdated formulae installed.

We've installed your MySQL database without a root password. To secure it run:
    mysql_secure_installation

MySQL is configured to only allow connections from localhost by default

To connect run:
    mysql -u root

mysql@8.0 is keg-only, which means it was not symlinked into /usr/local,
because this is an alternate version of another formula.

If you need to have mysql@8.0 first in your PATH, run:
  echo 'export PATH="/usr/local/opt/mysql@8.0/bin:$PATH"' >> /Users/<USER>/.zshrc

For compilers to find mysql@8.0 you may need to set:
  export LDFLAGS="-L/usr/local/opt/mysql@8.0/lib"
  export CPPFLAGS="-I/usr/local/opt/mysql@8.0/include"

For pkg-config to find mysql@8.0 you may need to set:
  export PKG_CONFIG_PATH="/usr/local/opt/mysql@8.0/lib/pkgconfig"

To start mysql@8.0 now and restart at login:
  brew services start mysql@8.0
Or, if you don't want/need a background service you can just run:
  /usr/local/opt/mysql@8.0/bin/mysqld_safe --datadir\=/usr/local/var/mysql
==> Summary
🍺  /usr/local/Cellar/mysql@8.0/8.0.40_3: 319 files, 297.2MB
==> Running `brew cleanup mysql@8.0`...
Disable this behaviour by setting HOMEBREW_NO_INSTALL_CLEANUP.
Hide these hints with HOMEBREW_NO_ENV_HINTS (see `man brew`).
==> Upgrading 1 dependent of upgraded formula:
Disable this behaviour by setting HOMEBREW_NO_INSTALLED_DEPENDENTS_CHECK.
Hide these hints with HOMEBREW_NO_ENV_HINTS (see `man brew`).
mysql 9.0.1_6 -> 9.0.1_7
==> Fetching mysql
==> Downloading https://mirrors.ustc.edu.cn/homebrew-bottles/bottles/mysql-9.0.1_7
########################################################################### 100.0%
==> Verifying attestation for mysql
==> Upgrading mysql
  9.0.1_6 -> 9.0.1_7 
==> Pouring mysql-9.0.1_7.sonoma.bottle.tar.gz
xcode-select: note: No developer tools were found, requesting install.
If developer tools are located at a non-default location on disk, use `sudo xcode-select --switch path/to/Xcode.app` to specify the Xcode that you wish to use for command line developer tools, and cancel the installation dialog.
See `man xcode-select` for more details.
==> Caveats
Upgrading from MySQL <8.4 to MySQL >9.0 requires running MySQL 8.4 first:
 - brew services stop mysql
 - brew install mysql@8.4
 - brew services start mysql@8.4
 - brew services stop mysql@8.4
 - brew services start mysql

We've installed your MySQL database without a root password. To secure it run:
    mysql_secure_installation

MySQL is configured to only allow connections from localhost by default

To connect run:
    mysql -u root

To start mysql now and restart at login:
  brew services start mysql
Or, if you don't want/need a background service you can just run:
  /usr/local/opt/mysql/bin/mysqld_safe --datadir\=/usr/local/var/mysql
==> Summary
🍺  /usr/local/Cellar/mysql/9.0.1_7: 324 files, 302.0MB
==> Running `brew cleanup mysql`...
Removing: /usr/local/Cellar/mysql/9.0.1_6... (324 files, 301.9MB)
Removing: /Users/<USER>/Library/Caches/Homebrew/mysql--9.0.1_6.sonoma.bottle.tar.gz... (80.6MB)
==> Checking for dependents of upgraded formulae...
==> No broken dependents found!
==> Caveats
==> mysql@8.0
We've installed your MySQL database without a root password. To secure it run:
    mysql_secure_installation

MySQL is configured to only allow connections from localhost by default

To connect run:
    mysql -u root

mysql@8.0 is keg-only, which means it was not symlinked into /usr/local,
because this is an alternate version of another formula.

If you need to have mysql@8.0 first in your PATH, run:
  echo 'export PATH="/usr/local/opt/mysql@8.0/bin:$PATH"' >> /Users/<USER>/.zshrc

For compilers to find mysql@8.0 you may need to set:
  export LDFLAGS="-L/usr/local/opt/mysql@8.0/lib"
  export CPPFLAGS="-I/usr/local/opt/mysql@8.0/include"

For pkg-config to find mysql@8.0 you may need to set:
  export PKG_CONFIG_PATH="/usr/local/opt/mysql@8.0/lib/pkgconfig"

To start mysql@8.0 now and restart at login:
  brew services start mysql@8.0
Or, if you don't want/need a background service you can just run:
  /usr/local/opt/mysql@8.0/bin/mysqld_safe --datadir\=/usr/local/var/mysql
==> mysql
Upgrading from MySQL <8.4 to MySQL >9.0 requires running MySQL 8.4 first:
 - brew services stop mysql
 - brew install mysql@8.4
 - brew services start mysql@8.4
 - brew services stop mysql@8.4
 - brew services start mysql

We've installed your MySQL database without a root password. To secure it run:
    mysql_secure_installation

MySQL is configured to only allow connections from localhost by default

To connect run:
    mysql -u root

To start mysql now and restart at login:
  brew services start mysql
Or, if you don't want/need a background service you can just run:
  /usr/local/opt/mysql/bin/mysqld_safe --datadir\=/usr/local/var/mysql
```

先取消当前 MySQL 的链接：

```bash
brew unlink mysql
```

然后强制链接 MySQL 8.0：

```bash
brew link --overwrite mysql@8.0
```

这不是重复安装，而是安装正确版本的 MySQL 程序来匹配您的数据。这样操作不会影响您现有的数据，只是确保 MySQL 服务器版本与数据版本匹配。



## 理论分析

这些知识来自多个来源的组合：

1. MySQL 官方文档：

- [MySQL Installation Layout](https://dev.mysql.com/doc/refman/8.0/en/installation-layout.html) 详细说明了 MySQL 的安装布局，包括二进制文件和数据目录的分离
- [Upgrading MySQL](https://dev.mysql.com/doc/refman/8.0/en/upgrading.html) 描述了版本升级的注意事项

1. Homebrew 的工作机制：

- [Homebrew Documentation](https://docs.brew.sh/Formula-Cookbook) 解释了它如何管理软件包
- Homebrew 使用 `/usr/local/Cellar` 存储程序文件
- 使用 `/usr/local/var` 存储可变数据
- `brew unlink/link` 只操作符号链接，不影响数据文件

1. Unix/Linux 文件系统层面的设计：

- `/usr/local/Cellar` - 程序文件
- `/usr/local/var` - 可变数据
- `/usr/local/opt` - 符号链接

这种设计遵循了 Unix 的基本原则：

1. 程序和数据分离
2. 配置文件集中管理
3. 可变数据独立存储

实际上，这种设计不仅适用于 MySQL，很多其他数据库（如 PostgreSQL、MongoDB）也采用类似的布局。这样的设计使得：

- 程序升级不影响数据
- 数据备份更简单
- 版本切换更安全

所以当我们说 `brew unlink mysql` 是安全的，是因为从系统设计层面就保证了这一点，而不是碰巧如此。

Q：所以因为没有执行卸载 MySQL 操作，所以不会删除已有数据是吗？

这里涉及到 MySQL 的两个重要部分：

1. MySQL 服务器程序（在 /usr/local/Cellar/mysql/ 目录下）
2. MySQL 数据文件（在 /usr/local/var/mysql/ 目录下）

`brew unlink mysql` 只会解除 MySQL 9.0.1 程序的符号链接，不会碰触数据目录。这个操作相当于：

- ❌ 不会删除数据
- ❌ 不会修改数据
- ✅ 只是断开对 MySQL 9.0.1 程序的引用

而 `brew install mysql@8.1` 会：

- ✅ 安装 8.1 版本的 MySQL 程序
- ❌ 不会覆盖现有数据目录
- ❌ 不会创建新的数据目录（因为已经存在）

这就是为什么这个操作是安全的 - 我们只是在替换 MySQL 程序本身，让它与您现有的 8.1 版本数据兼容，而不会影响到您的实际数据。