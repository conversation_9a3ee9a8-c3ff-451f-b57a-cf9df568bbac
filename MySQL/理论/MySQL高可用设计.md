## 1 为何要高可用

高可用，即若数据库突然宕机一台机器，比如主库或从库宕机，那数据库还能正常使用吗？

从库宕机影响并不大，大不了就是让所有读流量都从主库去读，但主库宕机呢？那就麻烦了，就没法写入数据，从库毕竟不允许写入，而只允许读取。

所以如何能保证在主库宕机后，就立即将从库切为主库呢，然后所有人都对从库切换为的主库去写入和读取？这样数据库就实现高可用了。

 一般生产环境，数据库高可用架构管理的工具是MHA，Master High Availability Manager and Tools for MySQL，perl脚本写的一个工具，用于监控主库的状态，如感觉不对劲，可将从库切为主库。

MHA需单独部署，分为：

- Manager节点

  一般是单独部署一台机器的

- Node节点

  一般是部署在每台MySQL机器上的，因为Node 节点得通过解析各个MySQL的日志来进行一些操作。

Manager节点会通过探测集群里的Node节点去判断各个Node所在机器上的MySQL运行是否正常，若发现某个Master故障了，就直接把他的一个Slave提升为Master，然后让其他Slave都挂到新的Master上去，完全透明。

## 2 搭建过程

准备4台机器，其中一台机器装个mysql作为master，另外两台机器都装mysql作 为slave，然后在每个机器上都得部署一个MHA的node节点，然后用单独的最后一台机器装MHA的  master节点。

首先确保4台机器之间免密码通信，即4台机器之间要不依靠密码可以直接ssh登录上去，因为这是MHA的perl脚本要用。

接着部署一个MySQL master和两个MySQLslave ，先装好MySQL，接着进行主从复制的搭建，全部按照之前的步骤走就行了，可以选择异步复制、半同步复制。

在三个数据库所在机器上安装MHA node节点的步骤，必安装Perl语言环境。

yum装Perl语言环境： 

```bash
yum install perl-DBD-MySQL
```

再从下述地址下载MHA node代码：https://github.com/yoshinorim/mha4mysql-node，再将node的压缩包用WinSCP之类的工具上传到机器，再解压node包：

```bash
tar -zxvf   mha4mysql-node-0.57.tar.gz
```

再安装perl-cpan软件包：

```bash
 

cd mha4mysql-node-0.57

yum -y install perl-CPAN*

perl Makeﬁle.PL

make && make install
```

至此node安装可以。3个部署MySQL的机器都要安装node，再安装MHA的manager节点，先安装依赖：

```bash
yum install -y perl-DBD-MySQL*

rpm -ivh perl-Params-Validate-0.92-3.el6.x86_64.rpm

rpm -ivh perl-Conﬁg-Tiny-2.12-1.el6.rfx.noarch.rpm

rpm -ivh perl-Log-Dispatch-2.26-1.el6.rf.noarch.rpm

rpm -ivh perl-Parallel-ForkManager-0.7.5-2.2.el6.rf.noarch.rpm
```

再安装manager节点，先在下面地址下载manager压缩包：https://github.com/yoshinorim/mha4mysql-manager。

再然后上传到机器上去，按照下述步骤安装：

```bash
tar -zxvf mha4mysql-manager-0.57.tar.gz
perl Makeﬁle.PL
make
make install
```

接着为MHA manager创建几个目录： /usr/local/mha， /etc/mha，然后进入到/etc/mha目录下， vi mha.conf一下，编辑他的配合文件

```properties


[server default]

user=zhss

password=12345678

manager_workdir=/usr/local/mha

manager_log=/usr/local/mha/manager.log

remote_workdir=/usr/local/mha

ssh_user=root

repl_user=repl

repl_password=repl

ping_interval=1

master_ip_failover_script=/usr/local/scripts/master_ip_failover

master_ip_online_change_script=/usr/local/scripts/master_ip_online_change

 

 

[server1]

hostname=xx.xx.xx.xx

ssh_port=22

master_binlog_dir=/data/mysqll

condidate_master=1

port=3306

 

 

[server1]

hostname=xx.xx.xx.xx

ssh_port=22

master_binlog_dir=/data/mysqll

condidate_master=1

port=3306

 

 

[server1]

hostname=xx.xx.xx.xx

ssh_port=22
```

![img](file:////private/var/folders/gd/7v6l67j50mx_bvxd4w04fh9h0000gn/T/com.kingsoft.wpsoffice.mac/wps-apple/ksohtml/wpsJCVRpj.png)master_binlog_dir=/data/mysqll

```properties
condidate_master=1

port=3306
```

上面那份配置文件就可以指导MHA manager节点去跟其他节点的node通信了，大家可以观察到，上面 说白了都是配置一些工作目录，日志目录，用户密码之类的东西，还有一些脚本，另外比较关键的是， 你有几个node节点，就配置一个server，把每个server的ip地址配置进去就可以了

![img](file:////private/var/folders/gd/7v6l67j50mx_bvxd4w04fh9h0000gn/T/com.kingsoft.wpsoffice.mac/wps-apple/ksohtml/wpsh4uWX7.jpg)

接着创建存放脚本的目录： /usr/local/scripts，在里面需要放一个master_ip_failover脚本， vi master_ip_failover就可以了，输入下面的内容：

接着在编辑一下online_change脚本：

![img](file:////private/var/folders/gd/7v6l67j50mx_bvxd4w04fh9h0000gn/T/com.kingsoft.wpsoffice.mac/wps-apple/ksohtml/wpss1dtFA.png) 

![img](file:////private/var/folders/gd/7v6l67j50mx_bvxd4w04fh9h0000gn/T/com.kingsoft.wpsoffice.mac/wps-apple/ksohtml/wpsf6g8ee.jpg) 

![img](file:////private/var/folders/gd/7v6l67j50mx_bvxd4w04fh9h0000gn/T/com.kingsoft.wpsoffice.mac/wps-apple/ksohtml/wpsCV4Ucl.jpg) 

![img](file:////private/var/folders/gd/7v6l67j50mx_bvxd4w04fh9h0000gn/T/com.kingsoft.wpsoffice.mac/wps-apple/ksohtml/wpsyxgMAs.png) 

![img](file:////private/var/folders/gd/7v6l67j50mx_bvxd4w04fh9h0000gn/T/com.kingsoft.wpsoffice.mac/wps-apple/ksohtml/wps4MMTSV.png) 

![img](file:////private/var/folders/gd/7v6l67j50mx_bvxd4w04fh9h0000gn/T/com.kingsoft.wpsoffice.mac/wps-apple/ksohtml/wps1FEANH.png) 



![img](file:////private/var/folders/gd/7v6l67j50mx_bvxd4w04fh9h0000gn/T/com.kingsoft.wpsoffice.mac/wps-apple/ksohtml/wpsxALASj.jpg) 

![img](file:////private/var/folders/gd/7v6l67j50mx_bvxd4w04fh9h0000gn/T/com.kingsoft.wpsoffice.mac/wps-apple/ksohtml/wpsDCQDxS.png) 

![img](file:////private/var/folders/gd/7v6l67j50mx_bvxd4w04fh9h0000gn/T/com.kingsoft.wpsoffice.mac/wps-apple/ksohtml/wpsCYLLwd.png) 

给两个脚本增加权限：

```bash
chmod +x master_ip_failover

chmod +x master_ip_online_change
```

接着安装需要的软件包：

```
 yum -y install perl-Time-HiRes
```

执行SSH检测命令： 

```bash
/usr/local/bin/masterha_check_ssh --conf=/etc/mha/mha.conf
```

如果检测结果全部显示为OK，那么就代表你安装完毕了

然后检测主从架构： 

```bash
/usr/local/bin/masterha_check_repl --conf=/etc/mha/mha.conf
```

如果检测结果全部正常即可。

首先，要在MySQL主库所在的机器上去添加VIP，虚拟VIP地址

```bash
ip addr add xx.xx.xx.xx dev eth0
```

这里的xx.xx.xx.xx，就是你自定义的一个VIP地址

接着就可以启动MHA manager节点了，在MHA manager所在机器上执行下述命令： 

```bash
nohup masterha_manager --conf=/etc/mha/mha.conf > /tmp/mha_manager.log < /dev/null 2>&1 &
```

这就可以启动MHA的manager节点了。接着验证一下启动是否成功：

```bash
masterha_check_status --conf=/etc/mha/mha.conf
```

此时只要看到 MHA manager正常工作就行了，接着就可以测试一下数据库高可用了，比如你可以先把主库停了：

```bash
mysqladmin -uroot -proot shutdown
```

然后从库会自动获取到主库机器上的VIP的，同时从库会被转换为新的主库，其他从库也会指向新的主  库，这些都是MHA自动给你完成的，然后你可以把宕机的主库重新启动，然后把他配置为从库，指向新 的主库就可以了

数据库的高可用架构就是这么个意思，其实搭建虽然很繁琐，但是只要搭建好了，基本就是 比较自动化的了，相信大家结合之前的一些内容，应该都能理解，只不过在搭建的过程中可能会遇到一 些小问题，可以自己尝试去解决一下。

## 意义

1、读写分离，提升读能力
2、故障转移，提供 failover 能力

加上业务侧连接池的心跳重试，实现断线重连，业务不间断，降低RTO和RPO。

高可用意味着，更少的不可服务时间。一般用SLA/SLO衡量。

```bash
1年 = 365天 = 8760小时
99 = 8760 * 1% = 8760 * 0.01 = 87.6小时
99.9 = 8760 * 0.1% = 8760 * 0.001 = 8.76小时
99.99 = 8760 * 0.0001 = 0.876小时 = 0.876 * 60 = 52.6分钟
99.999 = 8760 * 0.00001 = 0.0876小时 = 0.0876 * 60 = 5.26分钟
```

## 3 failover，故障转移，灾难恢复

容灾：热备与冷备

对于主从来说，就是主挂了，某一个从变成主，集群整体来看依旧能正常对外服务。

常见策略：

- 多个实例不在一个主机/机架
- 跨机房和可用区部署
- 两地三中心容灾高可用方案

###  3.3.3 高可用方案

####  ******* 主从手动切换

若主节点宕机，将某个从改成主；重新配置其他从节点。修改应用数据源配置。

缺点：

1. 可能数据不一致
2. 需要人工干预
3. 代码和配置的侵入性

#### ******* 主从自动切换

LVS+Keepalived实现多节点的探活+请求路由。

配置 VIP 或 DNS 实现配置不变更。

缺点：

- 手工处理主从切换
- 大量配置和脚本定义

算半自动。

#### ******* MHA

MHA，Master High Availability，目前在 MySQL 高可用是相对成熟解决方案，由日本 DeNA 公司youshimaton（现就职 Facebook）开发，是套优秀的作为 MySQL 高可用性环境下故障切换和主从提升的高可用软件：

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/cf66b59495679e38695ded1c108ffbe5.png)

基于 Perl 语言开发，一般能在30s内实现主从切换。切换时，直接通过 SSH 复制主节点的日志。

缺点：

- 需要配置 SSH 信息
- 至少3台

#### ******* MGR

不借助外部力量，只用 MySQL 本身。如主节点宕机，自动选择某从为主；无需人工干预，基于组复制，保证数据一致性：

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/40c489845cad84602fc2097b7ad28179.png)

缺点：

- 外部获得状态变更，需读取数据库
- 外部需用 LVS/VIP 配置

特点：

- 高容错性：自动检测机制，只要不是大多数节点宕机就可继续工作，内置防脑裂保护机制
- 高扩展性：节点增加、移除会自动更新组成员信息，新节点加入后，自动从其他节点同步增量数据，直到与其他节点数据一致
- 高灵活性：提供单主模式和多主模式，单主模式在主库宕机后能够自动选主，所有写入都在主节点进行，多主模式支持多节点写入

适用场景：

- 弹性复制：需非常流畅的复制基础架构的环境，其中服务器的数量必须动态地增长或缩减，而最少尽可能的痛苦：
  ![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/067e5798a63cab8ebad9fec6836a4ceb.png)
- 高可用分片
  Sharding is a popular approach to achieve write scale-out. Users can use MySQL Group Replication to implement highly available shards. Each shard
  can map into a Replication Group.
  分片是实现写横向扩展的一种流行方法。用户可以使用MySQL组复制来实现高度可用的分片。每个分片可以映射到副本组。
  ![](https://img-blog.csdnimg.cn/20210202212748415.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

#### 3.3.3.4 MySQL Cluster

完整的数据库层高可用解决方案。
**MySQL InnoDB Cluster**是一个高可用的框架，构成组件： 

- MySQL Group Replication
  提供DB的扩展、自动故障转移
- MySQL Router
  轻量级中间件，提供应用程序连接目标的故障转移。MySQL Router是一个轻量级的中间件，可以提供负载均衡和应用连接的故障转移。它是MySQL团队为MGR量身打造的，通过使用Router和Shell,用户可以利用MGR实现完整的数据库层的解决方案。如果你在使用MGR，请一定配合使用Router和Shell，可以理解为它们是为MGR而生的，会配合MySQl 的开发路线图发展的工具。
- MySQL Shell
  新的MySQL客户端，多种接口模式。可以设置群组复制及Router。MySQL Shell是MySQL团队打造的一个统一的客户端， 它可以对MySQL执行数据操作和管理。它支持通过JavaScript，Python，SQL对关系型数据模式和文档型数据模式进行操作。使用它可以轻松配置管理InnoDB Cluster。
  ![](https://img-blog.csdnimg.cn/20210202224102202.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

#### ******* Orchestrator

如果主节点挂掉，将某个从改成主。
一款MySQL高可用和复制拓扑管理工具，支持复制拓扑结构的调整，自动故障转移和手动主从切换等。后端数据库用MySQL或SQLite存储元数据，并提供Web界面展示MySQl 复制的拓扑关系及状态，通过Web可更改MySQL实例的复制关系和部分配置信息，同时也提供命令行和API接口，方便运维管理。

特点:

1. 自动发现MySQL的复制拓扑，并且在web.上展示;
2. 重构复制关系， 可以在web进行拖图来进行复制关系变更;
3. 检测主异常，并可以自动或手动恢复，通过Hooks进行自定义脚本;
4. 支持命令行和web界面管理复制。

基于 Go 语言开发，实现了中间件本身的高可用。

两种部署方式
orchestrator/raft：

1. 数据一致性由orchestrator的raft协议保证
2. 数据库之间不通信
   orchestrator/[galera | xtradb cluster | innodb cluster]:
3. 数据一致性由数据库集群保证
4. 数据库结点之间通信

如不部署client

1. 使用HTTP (/api/leader-check)查询并路由到主节点

优势：
能直接在 UI 界面
拖拽改变主从关系
![](https://img-blog.csdnimg.cn/20210202231819583.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)