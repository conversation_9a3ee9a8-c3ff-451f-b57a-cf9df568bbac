## 1 主从读写分离

当我们面临高并发的查询数据请求时，可以使用主从读写分离，部署多个从库分摊读压力。
大部分互联网业务都是读多写少，因此优先考虑DB如何支撑更高并发查询，首先就需要区分读、写流量，这才方便针对读流量单独扩展，即主从读写分离。

若前端流量突增导致从库负载过高，DBA会优先做个从库扩容上去，这样对DB的读流量就会落到多个从库，每个从库的负载就降了下来，然后开发再尽力将流量挡在DB层之上。

> Cache V.S MySQL读写分离
> 由于从开发和维护的难度考虑，引入缓存会引入复杂度，要考虑缓存数据一致性，穿透，防雪崩等问题，并且也多维护一类组件。所以推荐优先采用读写分离，扛不住了再使用Cache。

### 1.1 core

主从读写分离一般将一个DB的数据拷贝为一或多份，并且写入到其它的DB服务器中：
- 原始DB为主库，负责数据写入
- 拷贝目标DB为从库，负责数据查询

#### 主从读写分离关键

- 数据的拷贝：即主从复制
- 屏蔽主从分离带来的访问DB方式的变化：让开发人员使用感觉依旧在使用单一DB

## 2 主从复制

MySQL主从复制依赖binlog，即记录MySQL上的所有变化并以二进制形式保存在磁盘。

主从复制就是将binlog数据从主库传输到从库，一般异步：主库操作不会等待binlog同步完成。

### 2.1 主从复制过程

- 从库在连接到主节点时会创建一个I/O线程，以请求主库更新的binlog，并把接收到的binlog写入relay log文件，主库也会创建一个log dump线程发送binlog给从库
- 从库还会创建一个SQL线程，读relay log，并在从库中做回放，最终实现主从的一致性

使用独立的log dump线程是异步，避免影响主库的主体更新流程，而从库在接收到信息后并不是写入从库的存储，是写入一个relay log，这是为避免写入从库实际存储会比较耗时，最终造成从库和主库延迟变长。

![](https://img-blog.csdnimg.cn/d03b032126af44019d8785523c3e8203.png?x-oss-process=image/watermark,type_ZHJvaWRzYW5zZmFsbGJhY2s,shadow_50,text_SmF2YUVkZ2U=,size_20,color_FFFFFF,t_70,g_se,x_16)

- 主库将变更写到binlog日志（顺序写）
- 【从库】定期对主库 binlog 文件探测是否改变，若改变，从库有个I/O线程，负责和主库建立一个TCP连接，接着请求主库传输binlog日志给自己。
- 主库为每个I/O线程启动一个I/O dump线程，负责通过该TCP连接把binlog日志传输给从库的I/O线程（顺序读）。将主库的binlog日志拷贝到本地，写入一个中继日志（顺序写）
- 【从库】有个SQL线程，会从中继日志中读binlog，然后执行binlog日志内容。即在本地再执行一遍SQL，确保和主库数据相同（随机读写，因为需要把不同SQL语句对数据的修改写到不同的表）
- 最后，I/O 线程和SQL线程将进入睡眠，等待下一次被唤醒

![](https://img-blog.csdnimg.cn/f4a72cf3e6a043a88113d613bd7f909e.png)

考虑到性能，主库写入流程并没有等待主从同步完成就返回结果，极端case，如主库binlog还没落盘，就发生磁盘损坏或机器掉电，导致binlog丢失，主从数据不一致。不过概率很低，也能容忍。

> 主库宕机后，binlog丢失导致的主从数据不一致也只能手动恢复。

主从复制后，即可：
- 在写入时只写主库
- 在读数据时只读从库

这样即使写请求会锁表或锁记录，也不会影响读请求执行。高并发下，可部署多个从库共同承担读流量，即一主多从支撑高并发读。

从库也能当成个备库，以避免主库故障导致数据丢失。

那无限制地增加从库就能支撑更高并发吗？
NO！从库越多，从库连接上来的I/O线程越多，主库也要创建同样多log dump线程处理复制的请求，对于主库资源消耗较高，同时受限于主库的网络带宽，所以一般一个主库最多挂3～5个从库。

## 2.2 主从复制的副作用
比如发朋友圈这一操作，就存在数据的：
- 同步操作
如更新DB
- 异步操作
如将朋友圈内容同步给审核系统

所以更新完主库后，会将朋友圈ID写入MQ，由Consumer依据ID在从库获取朋友圈信息再发给审核系统。
**此时若主从DB存在延迟，会导致在从库取不到朋友圈信息，出现异常！**

主从延迟对业务影响：

![](https://img-blog.csdnimg.cn/80fea7d207da46f3a7f82592e16a4f6d.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

主库是多线程并发写，但从库是单线程拉取数据，所以导致从库复制数据的速度较慢，自然就会导致主从延迟。

主从到底延迟多久可监控，推荐percona- toolkit工具集里的pt-heartbeat工具，会在主库创建一个heartbeat表，然后会有一个线程定时更新这个表里的时间戳字段，从库上就有一个monitor线程会负责检查从库同步过来的heartbeat表里的时间戳。把时间戳跟当前时间戳比较，就知道主从之间同步落后多长时间。 

主从延迟会导致什么问题呢？做了读写分离架构，主库写，从库读，会不会你的系统刚写入一条数据到主库，接着代码里立即就在从库里读取，可能此时从库复制有延迟，你会读不到刚写进去的数据！另外可能你的从库同步数据太慢，导致你从库读取的数据都是落后和过期数据，也可能会导致你的系统产生一定的业务上的bug。

主从同步延迟也是排查问题时容易忽略。
有时会遇到从DB获取不到信息，会纠结代码中是否有一些逻辑把之前写入内容删除了，但发现过段时间再去查询时又能读到数据，这基本就是主从延迟。
所以，一般把从库落后的时间作为一个重点DB指标，做监控和报警，正常时间在ms级，s级告警。

> 主从的延迟时间预警，那如何通过哪个数据库中的哪个指标来判别？ 在从从库中，通过监控show slave
> status\G命令输出的Seconds_Behind_Master参数的值判断，是否有发生主从延时。
> 这个参数值是通过比较sql_thread执行的event的timestamp和io_thread复制好的
> event的timestamp(简写为ts)进行比较，而得到的这么一个差值。
> 但如果复制同步主库bin_log日志的io_thread线程负载过高，则Seconds_Behind_Master一直为0，即无法预警，通过Seconds_Behind_Master这个值来判断延迟是不够准确。其实还可以通过比对master和slave的binlog位置。

所以要尽可能缩小主从同步的延迟时间，让从库也用多线程并行复制数据，这样从库复制数据的速度快了，延迟就会很低了。MySQL 5.7已支持并行复制了，可在从库里设置slave_parallel_workers>0，然后把 slave_parallel_type设置为LOGICAL_CLOCK。 

若觉得还是要求刚写入的数据你立马强制必须一定能读到，此时可使用一个办法，在类似MyCat或者Sharding-Sphere之类的中间件里设置强制读写都从主库走，这样你写入主库的数据，强制从主库里读取，一定立即能读到。

因此落地读写分离架构时，注意复制方式：

- 异步

  如 果说你对数据丢失并不是强要求不能丢失的话，可以用异步模式来复制，再配合一下从库的并行复制机制

- 半同步

  若对MySQL做高可用保证数据绝对不丢失的话，建议还是用半同步机制比较好一些，同理最好是配合从库的并行复制机制。

这咋办？解决方案很多：

### 2.3 降低主从复制的延迟

前三条的核心思想都是尽量不去从库查。比如针对上述案例，就有如下方案：

#### 2.3.1 数据冗余
发MQ时，不止发送朋友圈ID，而是发给Con需要的所有朋友圈信息，避免从DB重新查询数据。

> 推荐该方案，因为足够简单，不过可能造成单条消息较大，从而增加消息发送的带宽和时间。
>

#### 2.3.2 使用Cache
>
> 在同步写DB同时，把朋友圈数据写Cache，这样Con获取朋友圈信息时，先查Cache，也能保证数据一致性。

该方案适合新增数据的场景。

若在更新数据场景下，先更新Cache可能导致数据不一致。如两个线程同时更新数据：

> - 线程A把Cache数据更新为1
> - 另一个线程B把Cache数据更新为2
> - 然后线程B又更新DB数据为2
> - 线程A再更新DB数据为1

最终DB值（1）和Cache值（2）不一致！

####  2.3.3 查主库
可在Con中不查询从库，而改为查询主库。要明确查询的量级不会很大，是在主库的可承受范围之内，否则会对主库造成较大压力。

若非万不得已，不要使用该方案。因为要提供一个查询主库的接口，很难保证其他人不滥用。

若确实存在必须先插入，立马要求查询，然后立马就反过来执行一些操作，对这个查询设置**直连主库**(不推荐，这导致读写分离无意义)

#### 2.3.4 分库

将一个主库拆分，每个主库的写并发降低，主从延迟即可忽略不计

#### 2.3.5 打开MySQL支持的并行复制

多个库并行复制，若某库的写并发很高，达到2000/s，并行复制还是没意义。28法则，很多时候可能就是少数几个订单表，写入2000/s，而其他几十个表10/s。

从库开启多线程，并行读取relay log中不同库的日志，然后**并行重放不同库的日志**，这是**库级别的并行**。

#### 2.3.6 重构代码

插入数据后，直接更新（操作主库），不去查询（通过从库）

## 3 如何访问DB

使用主从复制将数据复制到多个节点，也实现了DB的读写分离，这时，对DB使用也变化：
- 以前只需使用一个DB地址
- 现在需使用一个主库地址，多个从库地址，且需区分写入操作和查询操作，再结合“分库分表”，复杂度提升

为降低实现的复杂度，业界涌现了很多DB中间件解决DB的访问问题，大致分为：
### 3.1 应用程序内部

如TDDL（ Taobao Distributed Data Layer），以代码形式内嵌运行在应用程序内部。可看成是一种数据源代理，它的配置管理多个数据源，每个数据源对应一个DB，可能是主库或从库。
当有一个DB请求时，中间件将SQL语句发给某个指定数据源，然后返回处理结果。
#### 优点

简单易用，部署成本低，因为植入应用程序内部，与程序一同运行，适合运维较弱的小团队。
#### 缺点

缺乏多语言支持，都是Java语言开发的，无法支持其他的语言。版本升级也依赖使用方的更新。
### 3.2 独立部署的代理层方案

如Mycat、Atlas、DBProxy。

这类中间件部署在独立服务器，业务代码如同在使用单一DB，实际上它内部管理着很多的数据源，当有DB请求时，它会对SQL语句做必要的改写，然后发往指定数据源。
#### 优点

- 一般使用标准MySQL通信协议，所以可支持多种语言
- 独立部署，所以方便维护升级，适合有运维能力的大中型团队
#### 缺点
所有的SQL语句都需要跨两次网络：从应用到代理层和从代理层到数据源，所以在性能上会有一些损耗。
## 4 总结

可以把主从复制引申为存储节点之间互相复制存储数据的技术，可以实现数据冗余，以达到备份和提升横向扩展能力。

使用主从复制时，需考虑：
 - 主从的一致性和写入性能的权衡
若保证所有从节点都写入成功，则写性能一定受影响；若只写主节点就返回成功，则从节点就可能出现数据同步失败，导致主从不一致。互联网项目，一般优先考虑性能而非数据的强一致性
- 主从的延迟
会导致很多诡异的读取不到数据的问题

很多实际案例：
- Redis通过主从复制实现读写分离
- Elasticsearch中存储的索引分片也可被复制到多个节点
- 写入到HDFS中，文件也会被复制到多个DataNode中

不同组件对于复制的一致性、延迟要求不同，采用的方案也不同，但设计思想是相通的。
## FAQ

订单通过userId hash到不同库，对前台用户订单查询有利，但后台系统页面需查看全部订单且排序，SQL执行就很慢，咋办？

由于后台系统不能直接查询分库分表的数据，可考虑将数据同步至ES。
