# 1 数据结构及算法基础
## 1.1 索引到底是什么？
官方定义：索引（Index）是帮助MySQL高效获取数据的数据结构，即索引是数据结构。
其出现就是为了提高数据查询效率，就像书的目录。

既然是查询，就主要需要从查询算法角度优化。
- 最基本的查询算法[顺序查找](http://en.wikipedia.org/wiki/Linear_search)（linear search），复杂度为O(n)的算法在数据量大时是糟糕的。
- 更优秀的查找算法，如二分查找要求被检索数据有序，二叉树查找只能应用于[二叉查找树](http://en.wikipedia.org/wiki/Binary_search_tree)，但`数据本身的组织结构不可能完全满足各种数据结构`

所以，在数据之外，数据库系统还维护着`满足特定查找算法的数据结构`，这些数据结构以某种方式引用（指向）数据，这样就可以`在这些数据结构上实现高级查找算法`
这种ADT，就是索引。

- 一种可能的索引方式
![图1  一个例子](https://imgconvert.csdnimg.cn/aHR0cDovL3VwbG9hZC1pbWFnZXMuamlhbnNodS5pby91cGxvYWRfaW1hZ2VzLzQ2ODU5NjgtYzY1YmUwNzMzM2RiZmIyNy5wbmc?x-oss-process=image/format,png)

左边是数据表，两列14条记录，最左边是数据记录的物理地址。
为加快`Col2`的查找，可维护一个右边所示二叉查找树，每个节点分别包含索引键值及一个指向对应数据记录物理地址的指针，这样就可以运用二叉查找在O(log2 N)内取到相应数据。
但实际数据库系统几乎没有使用二叉查找树或其进化品种[红黑树](http://en.wikipedia.org/wiki/Red-black_tree)（red-black tree）实现


## 1.4 主存存取原理
计算机使用的主存基本都是随机读写存储器（RAM），抽象出一个十分简单的存取模型来说明RAM的工作原理

从抽象角度看，主存是一系列的存储单元组成的矩阵，每个存储单元存储固定大小的数据
每个存储单元有唯一的地址，现代主存的编址规则比较复杂，这里将其简化成一个二维地址：通过一个行地址和一个列地址可以唯一定位到一个存储单元
- 存取过程
当系统需要读取主存时，将地址信号通过地址总线传给主存，主存读到地址信号后，解析信号并定位到指定存储单元，然后将此存储单元数据放到数据总线，供其它部件读取

- 写主存
过程类似，系统将要写入单元地址和数据分别放在地址总线和数据总线上，主存读取两个总线的内容，做相应的写操作

这里可以看出，主存存取的时间仅与存取次数呈线性关系，因为不存在机械操作，两次存取的数据的“距离”不会对时间有任何影响，例如，先取A0再取A1和先取A0再取D3的时间消耗是一样的
## 1.5  磁盘存取原理
索引一般以文件形式存储在磁盘上，索引检索需要磁盘I/O。与主存不同，磁盘I/O存在机械消耗，因此磁盘I/O时间消耗巨大。

磁盘由大小相同且同轴的圆形盘片组成，磁盘可以转动（各磁盘必须同步转动）
在磁盘的一侧有磁头支架，磁头支架固定了一组磁头，每个磁头负责存取一个磁盘的内容。磁头不能转动，但是可以沿磁盘半径方向运动（实际是斜切向运动），每个磁头同一时刻也必须是同轴的，即从正上方向下看，所有磁头任何时候都是重叠的（不过目前已经有多磁头独立技术，可不受此限制）

盘片被划分成一系列同心环，圆心是盘片中心，每个同心环叫做一个磁道，所有半径相同的磁道组成一个柱面。磁道被沿半径线划分成一个个小的段，每个段叫做一个扇区，每个扇区是磁盘的最小存储单元。为了简单起见，我们下面假设磁盘只有一个盘片和一个磁头。

当需要从磁盘读取数据时，系统会将数据逻辑地址传给磁盘，磁盘的控制电路按照寻址逻辑将逻辑地址翻译成物理地址，即确定要读的数据在哪个磁道，哪个扇区
为了读取这个扇区的数据，需要将磁头放到这个扇区上方，为了实现这一点，磁头需要移动对准相应磁道，这个过程叫做寻道，所耗费时间叫做寻道时间，然后磁盘旋转将目标扇区旋转到磁头下，这个过程耗费的时间叫做旋转时间
## 1.6  局部性原理与磁盘预读
由于存储介质特性，磁盘本身存取就比主存慢，再加上机械运动耗费，磁盘存取速度往往是主存的几百万分之一，因此要提高效率，必须减少磁盘I/O。
为了达到这个目的，磁盘往往也不是严格按需读取，而是每次都会预读，即使只需要一个字节，磁盘也会从这个位置开始，顺序向后再读取一定长度的数据放入内存。
这样做的理论依据是计算机科学中著名的局部性原理：
`当一个数据被用到时，其附近的数据也通常会马上被使用`，`程序运行期间所需要的数据通常比较集中`。
由于磁盘顺序读取的效率很高（无需寻道时间，只需很少的旋转时间），因此对于具有局部性的程序来说，预读可以提高I/O效率。

`预读的长度一般为页（page）的整数倍`。innodb 默认一次读取 16k 。
页是存储器的逻辑块，os往往将主存和磁盘存储区分割为连续的大小相等的块，每个存储块称为一页（许多 os 的页大小一般为4k），主存和磁盘以页为单位交换数据。
当程序要读取的数据不在主存中时，会触发缺页异常，系统会向磁盘发出读盘信号，磁盘会找到数据的起始位置并向后连续读取一页或几页载入内存中，然后异常返回，程序继续运行。

## 1.7 性能分析
一般使用磁盘I/O次数评价索引结构的优劣

### 1.7.1 为什么不用平衡二叉树？
平衡二叉树只有两个分支，而B+树的分支≥2；
B+树的层数只会小于平衡二叉树，层数越少，在查询时所需要的 I/O 硬盘访问越少，查询速度相对更快，提高了对系统资源的利用率。

### 1.7.2 为什么不用红黑树？
h明显要深的多。由于逻辑上很近的节点（父子）物理上可能很远，无法利用局部性，所以红黑树的I/O渐进复杂度也为O(h)，效率明显比B-Tree差很多

B+Tree更适合外存索引，原因和内节点出度d有关
从上面分析可以看到，`d越大索引的性能越好`
`出度的上限取决于节点内key和data的大小`：
```bash
dmax=floor(pagesize/(keysize+datasize+pointsize))
```
floor表示向下取整。由于B+Tree内节点去掉了data域，因此可以拥有更大的出度，更好的性能。

### 1.7.3 B Tree
定义数据记录为一个二元组[key, data]
- key为记录的K值，对于不同数据记录，key互不相同
- data为数据记录除key外的数据

#### 特点
- d为大于1的一个正整数，为B-Tree的度
- h为一个正整数，为B-Tree的高度
- 每个非叶节点由n-1个key和n个指针组成，其中`d<=n<=2d`
- 每个叶节点最少包含一个key和两个指针，最多包含2d-1个key和2d个指针，叶节点的指针均为`null`
- 所有叶节点具有相同深度，等于树高h
- key和指针互相间隔，节点两端是指针
- 一个节点中的key从左到右非递减排列
- 所有节点组成树结构
- 每个指针要么为null，要么指向另外一个节点
- 如果某个指针在节点node最左边且不为null，则其指向节点的所有key小于>v(key1),v(key1)为node的第一个key的值
- 如果某个指针在节点node最右边且不为null，则其指向节点的所有key大于v(keym),v(keym)为node的最后一个key的值。
- 如果某个指针在节点node的左右相邻key分别是keyi,keyi+1且不为null，则其指向节点的所有key小于v(keyi+1)且大于v(keyi)

由于B Tree的特性，按key检索数据的算法非常直观
- 首先从根节点二分查找
- 如果找到则返回对应节点的data
- 否则对相应区间的指针指向的节点递归进行查找
- 直到找到目标节点/null指针，查找成功/失败
```java
bTreeSearch(node, key) {
    if(node == null) return null;
    foreach(node.key) {
        if(node.key[i] == key) return node.data[i];
            if(node.key[i] > key) return bTreeSearch(point[i]->node);
    }
    return bTreeSearch(point[i+1]->node);
}
data = bTreeSearch(root, my_key);
```
关于B-Tree有一系列有趣的性质，例如一个度为d的B-Tree，设其索引N个key，则其树高h的上限为
![](https://imgconvert.csdnimg.cn/aHR0cDovL3VwbG9hZC1pbWFnZXMuamlhbnNodS5pby91cGxvYWRfaW1hZ2VzLzQ2ODU5NjgtMzRhYTNkOTJjOGNkYzA5Yy5wbmc?x-oss-process=image/format,png)

检索一个key，其查找节点个数的渐进时间复杂度为
![](https://imgconvert.csdnimg.cn/aHR0cDovL3VwbG9hZC1pbWFnZXMuamlhbnNodS5pby91cGxvYWRfaW1hZ2VzLzQ2ODU5NjgtNzAxYWY1NjMxMzRjZmUyMC5wbmc?x-oss-process=image/format,png)
从这点可以看出，B Tree是一个非常有效率的索引数据结构。

检索一次最多需要访问h个节点。数据库系统的设计者巧妙利用了磁盘预读原理（磁盘数据存储是采用块的形式存储的，每个块的大小为4K，每次I/O进行数据读取时，同一个磁盘块的数据可以一次性读取出来），将一个节点的大小设为块的整数倍16K，这样每个磁盘块只需一次I/O即可完全载入内存。

为达到该目的，在实际实现B-Tree还需要使用如下技巧：
1. 每次新建节点时，直接申请一个页的空间，保证一个节点物理上也存储在一个页里，而且计算机存储分配都是按页对齐，就实现了一个node只需一次I/O
2. B-Tree中一次检索最多需要h-1次I/O（根节点是常驻内存的），渐进复杂度为O(h)=O(logdN)

以InnoDB的一个整数字段索引为例，N差不多是1200。树高是4时，可存

```java
1200^3=17亿
```
考虑到根的数据块总在内存，一个10亿行的表上一个整数字段的索引，查找一个值最多只需要访问3次磁盘。其实，树的第二层也有很大概率在内存中，那么访问磁盘的平均次数就更少了。
综上所述，用B-Tree作为索引结构效率是非常高的。但是其内部的非叶节点也存储了 data 数据，所以一个节点里也存不了多少数据。
![](https://img-blog.csdnimg.cn/20200830223131662.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70#pic_center)

### 1.7.4 B+树 V.S B树

![](https://img-blog.csdnimg.cn/20200831013206413.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70#pic_center)

相比B Tree的B+Tree：

- 每个节点的指针上限为2d
- 内节点只存key
- 叶节点不存指针，叶节点指向被索引的数据，而非其他叶节点
    - innodb中，指向主键
    - myshaym中，指向数据的物理地址

并非所有节点有相同的域，因此B+Tree中叶节点和内节点大小不同。这点与B Tree不同，虽B Tree中不同节点存放的key和指针可能数量不一致，但每个节点的域和上限一致，所以在实现中B Tree对每个节点申请同等大小空间。
-  B+树的非叶节点不存储数据，且所有数据节点之间指针串联（即链表）。B+Tree每个叶节点增加一个指向相邻叶节点指针，形成带有顺序访问指针的B+Tree，以提高区间访问性能，如查询key为从18到49的所有数据记录，当找到18后，只需顺着节点和指针顺序遍历就可以一次性访问到所有数据节点，极大提高区间查询效率

- B树子结点带数据，且兄弟节点之间无指针串联

#### 查询性能
最简单的对比测试，假设范围查询 [0,N-1] ：
- B+ 树，只需要确定范围查询的边界节点，然后遍历即可
时间复杂度粗略算做 `2logN + N` (2logN：两个范围边界值的查找)
-  B 树可能就需要一个个查找
B 树就是 `NlogN` 

范围越大，查询性能差异越明显。

### 为什么不用 B*树？
![](https://img-blog.csdnimg.cn/20200831012857264.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70#pic_center)

B*树是B+树的变种：
1. 关键字个数限制问题，B+树初始化的关键字初始化个数是cei(m/2)，b*树的初始化个数为（cei(2/3*m)）
2. B+树节点满时就会分裂，而B*树节点满时会检查兄弟节点是否满（因为每个节点都有指向兄弟的指针），如果兄弟节点未满则向兄弟节点转移关键字，如果兄弟节点已满，则从当前节点和兄弟节点各拿出1/3的数据创建一个新的节点出来
