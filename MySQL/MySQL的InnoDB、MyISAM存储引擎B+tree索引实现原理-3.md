

## 3 索引的维护

B+树为维护索引的有序，插入新值时需要做必要维护。

上图为例，插入新行ID 700，只需在R5的记录后面插入。如果新插入ID 400，就麻烦了，需要逻辑上挪动后面数据，腾出位置。
更坏的结果是，如果R5所在数据页已满，需申请新数据页，然后挪部分数据过去。该过程称为页分裂，影响性能。

页分裂还影响数据页的利用率。原本放在一个页的数据，现在分两页，整体空间利用率降低。有分裂就有合并。当相邻两个页由于删除数据，利用率很低后，会将数据页合并。合并过程可认为是分裂过程的逆过程。

## 案例
建表规范：建表语句一定要有自增主键。
![](https://img-blog.csdnimg.cn/20210602141922445.png)
到底哪些场景下应该自增主键？
自增主键一般这么定义： 
```sql
NOT NULL PRIMARY KEY AUTO_INCREMENT
```
- 考虑性能
插新记录可不指定ID，系统会获取当前ID最大值加1作为新记录ID，即自增主键符合递增插入场景。每插入新记录，都是追加，不涉及挪动其他记录，也不会触发叶节点分裂。
而有业务逻辑的字段做主键，不易保证有序插入，因此写数据成本较高。

- 考虑存储空间
假设表中确实有个唯一字段身份证号，应该用身份证号做主键，还是自增字段？
因为非主键索引的叶节点都是主键值。
- 身份证号做主键，每个二级索引的叶节点占约20字节
- 整型主键，只要4字节，长整型（bigint）8字节

主键长度越小，普通索引的叶节点越小，普通索引占用空间就越小。
因此从性能和空间考虑，自增主键往往更合理。

有无场景适合用业务字段做主键？
场景如下：
1. 只有一个索引
2. 该索引须是唯一索引

即KV场景。
因为没有其他索引，所以不用考虑其他索引的叶节点大小。
要优先考虑“尽量使用主键查询”原则，直接将该索引设为主键，避免每次查询要搜两棵树。
