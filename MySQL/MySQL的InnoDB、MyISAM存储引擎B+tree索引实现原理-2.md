# 2 索引的实现

索引属于存储引擎部分，不同存储引擎索引实现方式不同。本文只讨论MyISAM和InnoDB两个存储引擎的索引实现方式。
## 2.1 MyISAM索引
使用B+Tree作为索引结构，叶节点data域存放数据记录的地址

- MyISAM索引的原理图
![](https://imgconvert.csdnimg.cn/aHR0cDovL3VwbG9hZC1pbWFnZXMuamlhbnNodS5pby91cGxvYWRfaW1hZ2VzLzQ2ODU5NjgtM2M1MjhlMDUwYmE5OTU5My5wbmc?x-oss-process=image/format,png)
设Col1为主键，则上图是一个MyISAM表的主索引（Primary key），可见MyISAM的索引文件仅保存数据记录的地址。

MyISAM的主/辅索引在结构上无任何区别，只是主索引要求`key唯一`，辅索引`key可重复`

如果在Col2上建立一个辅索引
- Col2上建立的辅索引
![](https://imgconvert.csdnimg.cn/aHR0cDovL3VwbG9hZC1pbWFnZXMuamlhbnNodS5pby91cGxvYWRfaW1hZ2VzLzQ2ODU5NjgtY2EyMzQzNDFlY2MyNTlkMC5wbmc?x-oss-process=image/format,png)
同样也是一颗B+Tree，data域保存数据记录的地址。
因此，MyISAM中索引检索的算法为首先按照B+Tree搜索算法搜索索引，如果指定的Key存在，则取出其data域的值，然后以data域的值为地址，读取相应数据记录。

MyISAM的索引方式也叫“非聚集”，是为了与InnoDB的聚集索引区别。

#### `注意 聚集 = 聚簇`
## 2.2 InnoDB 索引
虽然InnoDB也使用B+Tree作为索引结构，但具体实现方式却与MyISAM截然不同

### `InnoDB的数据文件本身就是索引文件`
- MyISAM
索引文件和数据文件分离，索引仅保存数据的地址
- InnoDB
表数据文件本身就是按B+Tree组织的一个索引结构，该树的叶节点data域保存了完整的数据记录。
索引的key：数据表的主键，因此InnoDB表数据文件本身就是主索引。

-  InnoDB主索引（也是数据文件）
![](https://imgconvert.csdnimg.cn/aHR0cDovL3VwbG9hZC1pbWFnZXMuamlhbnNodS5pby91cGxvYWRfaW1hZ2VzLzQ2ODU5NjgtMzZhMTBlYTVjZTZmZGVhNC5wbmc?x-oss-process=image/format,png)

InnoDB的数据文件本身要按主键聚集，所以InnoDB要求表必须有主键（MyISAM可无），如果没有显式指定，则MySQL会自动选择一个可以唯一标识数据记录的列作为主键。
不存在这种列，则MySQL自动为InnoDB表生一个隐含字段作为主键，这个字段长度为6个字节，类型为长整形

### InnoDB的辅索引data域存储相应记录主键的值而非地址
即InnoDB的所有辅助索引都引用主键作为data域。

- 定义在Col3上的一个辅索引
  ![](https://imgconvert.csdnimg.cn/aHR0cDovL3VwbG9hZC1pbWFnZXMuamlhbnNodS5pby91cGxvYWRfaW1hZ2VzLzQ2ODU5NjgtOWU4ZWI5MDQwMjliM2JjZS5wbmc?x-oss-process=image/format,png)
  这里以英文字符的ASCII码作为比较准则
  聚集索引这种实现方式使得按主键的搜索十分高效，但是辅助索引搜索需要检索两遍索引：
  -   首先检索辅助索引获得主键
  - 然后用主键到主索引中检索获得记录

知道了InnoDB的索引实现后，就很容易明白为什么不建议使用过长的字段作为主键，因为所有辅索引都引用主索引，过长的主索引会令辅索引变得过大
再如，用非单调的字段作为主键在InnoDB中不是个好主意，因为InnoDB数据文件本身是一颗B+Tree，非单调的主键会造成在插入新记录时数据文件为了维持B+Tree的特性而频繁的分裂调整，十分低效，而使用自增字段作为主键则是一个很好的选择。

InnoDB表都根据主键顺序以索引形式存放，该存储方式的表称为索引组织表。
而InnoDB又使用的B+树索引模型，所以数据都是存储于B+树。

**每一个索引在InnoDB里面就对应一棵B+树。**

- 主键列id，字段k，在k上有索引的建表语句
![](https://img-blog.csdnimg.cn/20200716231848265.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_1,color_FFFFFF,t_70)

表中R1~R5的(id,k)值分别为(100,1)、(200,2)、(300,3)、(500,5)、(600,6)
- 两棵树的示意图，即InnoDB的索引组织结构
![](https://img-blog.csdnimg.cn/20200716232154390.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_1,color_FFFFFF,t_70)

根据叶节点内容，索引类型分为
### 主键索引(聚簇索引)
InnoDB的主键索引也称聚簇索引、聚集索引（clustered index）。主键索引的叶节点存整行数据。

聚簇索引并非一种单独的索引类型，而是一种`数据存储方式`。
细节依赖其实现方式，但InnoDB 的聚簇索引实际上在同一个结构中`保存了B-Tree索引和数据行`，是对磁盘上实际数据重新组织以按指定的一个或多个列的值排序的算法。

每个 InnoDB 表都有一个称为聚集索引的特殊索引，用于存储行数据。通常，聚集索引与主键同义。为了从查询、插入和其他数据库操作中获得最佳性能，了解 InnoDB 如何使用聚集索引来优化常见的查找和 DML 操作非常重要。
- 在表上定义主键时，InnoDB 将其用作聚簇索引。应该为每个表定义一个主键。若没有逻辑唯一且非空的列或列集使用主键，请添加自增列。自增列值是唯一的，并在插入新行时自动添加
- 若未定义主键，则 InnoDB 使用第一个 UNIQUE 索引，所有键列都定义为 NOT NULL 作为聚集索引。
- 若表没有主键或合适的唯一索引，InnoDB 会在包含行 ID 值的合成列上生成一个名为 **GEN_CLUST_INDEX** 的隐藏聚集索引。行按 InnoDB 分配的行 ID 排序。行 ID 是一个 6 字节的字段，随着插入新行而单调增加。因此，按行 ID 排序的行在物理上是按插入顺序排列的。

- 特点
存储数据的顺序和索引顺序一致。

一般情况下主键会默认创建聚簇索引，`一张表只允许存在一个聚簇索引`。

当表有聚簇索引，数据实际存在索引叶子页（leaf page）中。

聚簇：数据行和相邻的键值交错的存储在一起，InnoDB通过主键聚集数据。因无法同时把数据行存放在两个不同地方，所以在一个表只能有一个聚簇索引 （不过，覆盖索引可以模拟多个聚簇索引）。

- 若未定义主键，InnoDB 会选择一个唯一的非空索引代替
- 若无这样的索引，InnoDB 会隐式定义一个主键来作为聚簇索引
- InnoDB值聚集在同一个页面中的记录，包含相邻键值的页面可能会相距很远

#### 聚集索引如何加快查询速度
通过聚集索引访问一行很快，因为索引搜索直接指向包含行数据的页面。若表很大，与使用与索引记录不同的页面存储行数据的存储组织相比，聚簇索引体系结构通常可以节省磁盘 I/O。

#### 二级索引与聚集索引的关系
聚集索引以外的索引称为二级索引。在 InnoDB 中，二级索引中的每条记录都包含该行的主键列，以及为二级索引指定的列。 InnoDB 使用这个主键值来搜索聚集索引中的行。

如果主键很长，二级索引会占用更多的空间，所以主键短是有利的。
### 非主键索引
也叫非聚簇索引、辅助索引、二级索引secondary index）。

非主键索引的叶节点是主键值。

主键索引 	V.S 普通索引的查询
- select * from T where ID=500，主键查询，只需搜ID这棵B+树
- select * from T where k=5，普通索引查询，需先搜k索引树，得到ID的值为500，再到ID索引树搜索（回表）。

> 回表：InnoDB在普通索引a上查到主键id的值后，再根据一个个主键id的值到主键索引上去查整行数据的过程。

非主键索引的查询需要多扫描一棵索引树。因此`尽量使用主键查询`，减少回表。

InnoDB和MyISAM是如何存储下面的这个表的
```sql
CREATE TABLE layout_test(
　　　　col1 int not null,
　　　　col2 int not null,
 　　　 primary key (col1),
　　　　key(col2)
);
```
假设该表的主键取值为1~10000，按随机顺序插入，并使用`OPTIMIZE TABLE`命令优化。
即数据在磁盘的存储方式已最优，但进行的顺序是随机的。
列col2的值时从1~100之间随机赋值，所以有很多重复值。

## MyISAM 数据分布
MyIsam按数据插入的顺序存储在磁盘。实际上，MyISAM 中主键索引和其他索引在结构上没有什么不同。主键索引就是一个名为PRIMARY的唯一非空索引。

## InnoDB 的数据分布
而InnoDB支持聚簇索引，在InnoDB中，聚簇索引“是”表，不像myISAM那样需要独立的行存储。

### 聚簇索引优点
- 把相关数据保存在一起
例如，实现电子邮箱时，可以根据用户id来聚集数据，这样只需要从磁盘读取少数数据页，就能获取某个用户的全部邮件。若未使用聚簇索引，则每封邮件都可能导致一次I/O。
- 数据访问更快
聚簇索引将索引和数据保存在同一B-Tree，从聚簇索引中获取数据通常比非聚簇索引中快
- 覆盖索引扫描的查询可直接使用页节点中的主键值

### 聚簇索引缺点
聚簇索引最大限度提高了I/O密集型应用性能，但若数据全存内存，则访问顺序就没那么重要，聚簇索引也没啥优势了。

- 插入速度严重依赖插入的顺序
按主键的顺序插入是加载数据到innodb表中速度最快的。
但若不是按主键顺序，则加载后最好使用OPTIMIZE TABLE重新组织表。

- 更新聚簇索引的代价高
因为会强制InooDB将每个更新的数据移动到新位置。

基于聚簇索引的表在插入行，或主键被更新导致需要移动行时，可能产生页分裂（page split）。
当行的主键值要求必须将该行插入到某个满页时。存储引擎会将该页分裂成两个页面来容纳该行，这就是一次页分裂。页分裂会导致表占用更多存储空间。

聚簇索引可能导致全表扫描变慢，尤其是行比较稀疏，或由于页分裂导致数据存储不连续时。

二级索引（非聚簇索引）可能比想象的要更大，因为在二级索引的子节点包含了最优一个几点可能让人有些疑惑
- 为什么二级索引需要两次索引查找？
二级索引中保存的“行指针”的本质：不是物理地址的指针，而是行的主键值。所以通过二级索引查找行，引擎需要找到二级索引的子节点获得对应主键值，然后根据该值去聚簇索引找到对应行。
出现重复工作：两次B-Tree查找，而非一次。对于InnoDB，自适应哈希索引能够减少这样重复。

《数据库原理》解释聚簇索引和非聚簇索引区别：
- 聚簇索引的叶节点就是数据节点
- 非聚簇索引的叶节点仍是索引节点，只不过有指向对应数据块的指针

MYISAM和INNODB两种引擎的索引结构。
- 原始数据
![](https://img-blog.csdnimg.cn/20210601201544160.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

- MyISAM数据存储
![](https://img-blog.csdnimg.cn/20210601201636760.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

MYISAM按列值与行号组织索引，叶子节点保存的是指向存放数据的物理块的指针。
MYISAM引擎的索引文件（.MYI）和数据文件(.MYD)是相互独立的。

而InnoDB按聚簇索引存储数据，存储数据的结构如下：
![](https://imgconvert.csdnimg.cn/aHR0cHM6Ly91cGxvYWQtaW1hZ2VzLmppYW5zaHUuaW8vdXBsb2FkX2ltYWdlcy80Njg1OTY4LTE1ZjYzZDM2N2EwNzdhYTQucG5n?x-oss-process=image/format,png)


注：聚簇索引中的每个叶子节点包含主键值、事务ID、回滚指针(rollback pointer用于事务和MVCC）和余下的列(如col2)。

INNODB的二级索引与主键索引有很大的不同。InnoDB的二级索引的叶子包含主键值，而不是行指针(row pointers)，这减小了移动数据或者数据页面分裂时维护二级索引的开销，因为InnoDB不需要更新索引的行指针。其结构大致如下：
![](https://imgconvert.csdnimg.cn/aHR0cHM6Ly91cGxvYWQtaW1hZ2VzLmppYW5zaHUuaW8vdXBsb2FkX2ltYWdlcy80Njg1OTY4LTFkNTZhNmJjNTY5NmQ0Y2MucG5n?x-oss-process=image/format,png)
INNODB和MYISAM的主键索引与二级索引的对比：
![](https://imgconvert.csdnimg.cn/aHR0cHM6Ly91cGxvYWQtaW1hZ2VzLmppYW5zaHUuaW8vdXBsb2FkX2ltYWdlcy80Njg1OTY4LWQyMzRjYTg1OTE5ODI2NDcucG5n?x-oss-process=image/format,png)

InnoDB的的二级索引的叶子节点存放的是KEY字段加主键值。因此，通过二级索引查询首先查到是主键值，然后InnoDB再根据查到的主键值通过主键索引找到相应的数据块。而MyISAM的二级索引叶子节点存放的还是列值与行号的组合，叶子节点中保存的是数据的物理地址。所以可以看出MYISAM的主键索引和二级索引没有任何区别，主键索引仅仅只是一个叫做PRIMARY的唯一、非空的索引，且MYISAM引擎中可以不设主键。
