### 

```bash
# 下载
brew install mysql

==> Pouring mysql-8.0.16.mojave.bottle.tar.gz
==> /usr/local/Cellar/mysql/8.0.16/bin/mysqld --initialize-insecure --
==> Caveats
We've installed your MySQL database without a root password. To secure it run:
    mysql_secure_installation

MySQL is configured to only allow connections from localhost by default

To connect run:
    mysql -uroot

A "/etc/my.cnf" from another install may interfere with a Homebrew-built
server starting up correctly.

To have launchd start mysql now and restart at login:
  brew services start mysql
Or, if you don't want/need a background service you can just run:
  mysql.server start
==> Summary
?  /usr/local/Cellar/mysql/8.0.16: 275 files, 269.8MB



To connect run:
    mysql -u root

mysql@8.0 is keg-only, which means it was not symlinked into /opt/homebrew,
because this is an alternate version of another formula.

If you need to have mysql@8.0 first in your PATH, run:
  echo 'export PATH="/opt/homebrew/opt/mysql@8.0/bin:$PATH"' >> ~/.zshrc

For compilers to find mysql@8.0 you may need to set:
  export LDFLAGS="-L/opt/homebrew/opt/mysql@8.0/lib"
  export CPPFLAGS="-I/opt/homebrew/opt/mysql@8.0/include"

To start mysql@8.0 now and restart at login:
  brew services start mysql@8.0
Or, if you don't want/need a background service you can just run:
  /opt/homebrew/opt/mysql@8.0/bin/mysqld_safe --datadir\=/opt/homebrew/var/mysql
```

```bash
javaedge@JavaEdgedeMac-Studio JavaVirtualMachines % brew info mysql
==> mysql: stable 9.2.0 (bottled)
Open source relational database management system
https://github.com/mysql/mysql-server

Upgrading from MySQL <8.4 to MySQL >9.0 requires running MySQL 8.4 first:
 - brew services stop mysql
 - brew install mysql@8.4
 - brew services start mysql@8.4
 - brew services stop mysql@8.4
 - brew services start mysql

We've installed your MySQL database without a root password. To secure it run:
    mysql_secure_installation

MySQL is configured to only allow connections from localhost by default

To connect run:
    mysql -u root

To start mysql now and restart at login:
  brew services start mysql
Or, if you don't want/need a background service you can just run:
  /opt/homebrew/opt/mysql/bin/mysqld_safe --datadir\=/opt/homebrew/var/mysql
==> Analytics
install: 56,846 (30 days), 166,664 (90 days), 605,368 (365 days)
install-on-request: 56,765 (30 days), 166,428 (90 days), 604,378 (365 days)
build-error: 1,168 (30 days)
javaedge@JavaEdgedeMac-Studio JavaVirtualMachines %
```

在 MySQL 8.0 中，修改用户密码的语法有所变化，您遇到的错误是因为使用了不正确的语法。以下是正确的修改密码方法：
### 方法一：使用 `ALTER USER` 语句
这是 MySQL 8.0 推荐的修改密码方法：
```sql
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '123456';
```
接着刷新权限：
```sql
FLUSH PRIVILEGES;
```
### 方法二：使用 `UPDATE` 语句
虽然不推荐，但也可以通过更新 `authentication_string` 字段来修改密码：
```sql
USE mysql;
UPDATE user SET authentication_string=PASSWORD('123456') WHERE user='root';
FLUSH PRIVILEGES;
```
### 注意事项
1. **权限验证插件**：确保使用的是 `mysql_native_password` 插件，否则可能需要调整插件类型。
2. **刷新权限**：修改密码后，务必执行 `FLUSH PRIVILEGES;` 以使更改生效。
### 示例
```sql
mysql> ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '123456';
mysql> FLUSH PRIVILEGES;
```
或者
```sql
mysql> USE mysql;
mysql> UPDATE user SET authentication_string=PASSWORD('123456') WHERE user='root';
mysql> FLUSH PRIVILEGES;
```