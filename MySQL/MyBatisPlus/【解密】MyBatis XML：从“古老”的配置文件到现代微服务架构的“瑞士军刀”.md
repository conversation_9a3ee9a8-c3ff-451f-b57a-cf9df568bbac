## 0 前言

看似“老派”但至今仍在无数项目中扮演着核心角色的技术——MyBatis XML。很多年轻的开发者可能会觉得，在 Spring Boot “约定优于配置”大行其道的今天，XML 似乎显得有些格格不入。但复杂的企业级应用和微服务场景下，MyBatis XML 强大的灵活性和控制力，能让它成为你解决复杂数据库交互问题的“瑞士军刀”。

这篇文章，我将带你重新认识一下 MyBatis XML，不仅会讲清楚它的“是什么”和“怎么用”，更会结合当下最火的微服务实践案例，告诉你如何在现代架构中将它的威力发挥到极致。

## 1 MyBatis XML到底是啥？

不仅是 SQL 的搬运工！很多初学者会简单地认为，MyBatis XML 文件不就是个写 SQL 的地方吗？是，但不全是。

### 1.1 更精准比喻

**MyBatis XML 是你的 Java 代码和关系型数据库之间的一位“专业翻译官兼数据塑形师”。**

  * **翻译官**：它将你的 Java 方法调用，精准地“翻译”成数据库能听懂的 SQL 语句。
  * **数据塑形师**：它还能将数据库返回的“原始”结果集（ResultSet），精细地“塑造”成你想要的 Java 对象（POJO）的模样，无论这个对象的结构有多复杂。

### 1.2 MyBatis XML文件结构

通常称为 Mapper XML，简单但包含 MyBatis XML 核心三要素：

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 1. 命名空间，连接 Java 接口（Mapper Interface）和 XML 文件的桥梁。值通常是你的 Mapper 接口全限定类名。当代码调用 UserMapper.findUserById(1) ，MyBatis 就知道来这文件里找 id="findUserById" 的 <select> 标签 -->
<mapper namespace="com.javaedge.repository.UserMapper">

<!-- 2. 语句标识，id 对应 Mapper 接口的方法名。这标签告诉 MyBatis，这是个查询操作。还有 <insert>, <update>, <delete> -->

<!-- 3. 结果类型映射，它告诉 MyBatis，将查询出来的每一行数据，自动映射成哪个 Java 对象。MyBatis自动根据列名和对象属性名进行匹配(驼峰命名也能智能转换，如 user_name -> userName) -->
    <select id="findUserById" resultType="com.javaedge.model.User">
        SELECT * FROM users WHERE id = #{id}
    </select>

</mapper>
```

## 2 最佳实践：resultMap

精准控制的艺术！`resultType` 虽然方便，但稍复杂业务场景就力不从心。如：

  * 数据库列名和 Java 属性名完全对不上
  * 需复杂关联查询（JOIN），将多张表的数据映射到一个复杂的 Java 对象
  * 处理一对多、多对一的关联关系

就轮到“数据塑形大师”—— `<resultMap>` 登场。

### 2.1 业务实战：电商平台的订单详情查询

电商微服务中查询一个订单完整信息，包括：

  * 订单基本信息（订单号、总金额、下单时间）
  * 下单用户信息（用户ID、昵称、头像）
  * 该订单包含的所有商品列表（商品名、价格、购买数量）

至少涉及三张表：`orders`（订单表）、`users`（用户表）、`order_items`（订单商品关联表）。希望一次查询就拿到一个结构清晰的 `OrderDetailVO` 对象。

```java
// 目标Java对象结构
public class OrderDetailVO {
    private String orderNo;
    private BigDecimal totalAmount;
    private Date createTime;
    private UserInfo user; // 关联的用户信息
    private List<OrderItem> items; // 关联的商品列表
}

public class UserInfo {
    private Long userId;
    private String nickname;
}

public class OrderItem {
    private String productName;
    private Integer quantity;
}
```

咋用一个 SQL 和 `<resultMap>` 实现？

### 2.2 最佳实践代码示例

```xml
<mapper namespace="com.javaedge.repository.OrderMapper">

  	<!-- 就像“装配图纸”。先定义好这个图纸，给它一个id=OrderDetailResultMap -->
    <resultMap id="OrderDetailResultMap" type="com.javaedge.vo.OrderDetailVO">
        <!-- id、result 标签负责处理主表的字段映射，property是Java对象的属性名，column是SQL查询结果的列名 -->
        <id property="orderNo" column="order_no"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="createTime" column="create_time"/>
        <!-- 处理“多对一”或“一对一”关系。这里告诉MyBatis，OrderDetailVO的user属性是UserInfo类型对象，其数据来源是SQL结果里的user_id、nickname列 -->
        <association property="user" javaType="com.javaedge.model.UserInfo">
            <result property="userId" column="user_id"/>
            <result property="nickname" column="nickname"/>
        </association>
        
        <!-- 处理“一对多”关系。告诉 MyBatis，items属性是个List，List里的每个元素是 OrderItem类型，其数据来源是product_name、quantity列 -->
        <collection property="items" ofType="com.javaedge.model.OrderItem">
            <result property="productName" column="product_name"/>
            <result property="quantity" column="quantity"/>
        </collection>
    </resultMap>

    <select id="findOrderDetailByOrderNo" resultMap="OrderDetailResultMap">
        SELECT
            o.order_no,
            o.total_amount,
            o.create_time,
            u.id AS user_id,
            u.nickname,
            oi.product_name,
            oi.quantity
        FROM
            orders o
        JOIN
            users u ON o.user_id = u.id
        JOIN
            order_items oi ON o.order_no = oi.order_no
        WHERE
            o.order_no = #{orderNo}
    </select>
</mapper>
```

### 2.3 优势

#### 高内聚

一次数据库交互，返回所有需要的数据，减少了网络开销和微服务间的多次调用。

#### 高可读性

复杂的映射逻辑清晰地定义在 XML 中，Java 代码非常干净，只需调用一个方法即可。

#### 解耦

数据库表结构变化，大多只需修改 XML 中的 `resultMap`，而无需改动 Java 端的模型代码。

## 3 动态 SQL

让你的 SQL “活”起来！MyBatis XML 最强大功能。实际业务经常遇到多条件组合查询。

### 3.1 业务实战-后台管理系统的商品搜索接口

商品搜索功能，用户可按条件筛选：

  * 商品名称（可选，模糊匹配）
  * 商品分类（可选）
  * 价格区间（可选）
  * 商品状态（可选，如：上架、下架）

传统 JDBC 或简单 SQL 拼接，是人祸！要写大量 `if-else` 拼接 SQL 字符串，丑陋且极易引发 SQL 注入风险。

**MyBatis 动态 SQL 的优雅实践：**

```xml
<mapper namespace="com.example.repository.ProductMapper">

    <sql id="productColumns">
        id, product_name, category_id, price, status
    </sql>

    <select id="searchProducts" resultType="com.example.model.Product">
        SELECT <include refid="productColumns"/>
        FROM products
        <where>
            <if test="productName != null and productName != ''">
                AND product_name LIKE CONCAT('%', #{productName}, '%')
            </if>
            <if test="categoryId != null">
                AND category_id = #{categoryId}
            </if>
            <if test="minPrice != null">
                AND price &gt;= #{minPrice}
            </if>
            <if test="maxPrice != null">
                AND price &lt;= #{maxPrice}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>
```

**通俗讲解与最佳实践：**

  * **`<sql>` 与 `<include>`**：这是非常好的一个实践！将通用的列名清单定义在 `<sql>` 标签中，然后在需要的地方用 `<include>` 引入。这样修改列名时，只需要改一个地方，符合 DRY (Don't Repeat Yourself) 原则。
  * **`<where>`**：智能标签！它知道如果内部的任何一个条件成立，就在最前面自动加上一个 `WHERE` 关键字。更智能的是，它会自动处理掉第一个条件前面多余的 `AND` 或 `OR`。告别 `WHERE 1=1` 这种反模式吧！
  * **`<if>`**：最常用的判断标签。`test` 属性中写的是 OGNL 表达式，可以直接访问传入参数的属性。
  * **`CONCAT` 函数**：在进行模糊查询时，推荐使用数据库的 `CONCAT` 函数，而不是 `'%${productName}%'` 这种方式，可以有效防止 SQL 注入。
  * **其他动态标签**：还有 `<choose>` (when, otherwise) 类似于 Java 的 `switch` 语句，`<foreach>` 用于处理 `IN` 查询等，都非常强大。

## 4 总结

现代架构下，我们为什么依然选择 MyBatis XML？

在 Spring Data JPA 等 ORM 框架大行其道的今天，MyBatis 及其 XML 配置模式依然生命力旺盛，尤其是在对性能和 SQL 优化要求极高的微服务和分布式系统中。

1.  **极致的 SQL 优化能力**：你可以完全控制和优化每一条 SQL 语句，利用数据库的特定功能（如索引提示、窗口函数等），这是很多自动化 ORM 框架难以做到的。对于高并发的金融、电商核心交易链路，这种控制力至关重要。
2.  **清晰的职责分离**：SQL 逻辑与业务逻辑彻底分离。DBA 或数据库专家可以专注于优化 XML 中的 SQL，而 Java 开发者则专注于业务逻辑实现，互不干扰。
3.  **应对复杂报表和异构数据源**：当需要处理极其复杂的报表查询，或者与一些设计不那么“规范”的旧系统数据库打交道时，MyBatis XML 的灵活性无人能及。
4.  **学习曲线平滑**：对于熟悉 SQL 的开发者来说，上手 MyBatis 非常快。

当然，它也有缺点，比如需要手写 SQL，以及 XML 文件在项目庞大后可能不易管理。但通过良好的模块化设计（如每个微服务有自己的 Mapper 集合）和利用 `<sql>` 等标签提高复用性，这些问题完全可以被克服。
