

## 1 前言

数据库除事务处理、查询引擎等核心功能，还提供小特性，看上去不起眼，却对简化开发工作很有益。

但这些特性的设计往往以单体数据库架构和适度的并发压力为前提。随业务扩大，海量并发下，这些特性可能被削弱或失效。分布式架构下，是否延续这些特性也存在不确定性，本文聊自增主键这种小特性。

虽我对自增主键态度和存储过程一样，都不推荐，但原因不同：

- 存储过程主要是工程原因
- 自增主键是架构因素

## 2 自增主键的特性

在不同数据库存在形式有异。

MySQL建表时可关键字auto_increment定义：

```sql
CREATE TABLE `test` (
  `id` INT(16) NOT NULL AUTO_INCREMENT,
  `name` CHAR(10) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB;
```

Oracle先声明一个连续序列sequence，在insert语句中直接引用sequence：

```sql
create sequence test_seq increment by 1 start with 1;

insert into test(id, name) values(test_seq.nextval, ' An example ');
```

自增主键给开发提供

### 便利

因主键须唯一，多数设计规范都会要求，主键不要带业务属性，若数据库没内置特性，开发须自己设计一套主键生成逻辑。数据库原生提供的自增主键免去工作量，且还能满足：

- 唯一性，必须保证
- 单调递增，后插入记录的自增主键值一定比先插入记录要大
- 连续递增，自增主键每次+1。有些应用系统甚至基于自增主键的“连续递增”特性来设计业务逻辑

## 3 单体数据库的自增主键

除了最基本的唯一性，另两层期待都无法充分满足。

### 3.1 无法连续递增

多数情况下，自增主键确实表现连续递增。但当事务发生冲突，主键就会跳跃，留下空洞。

MySQL处理过程：

![](https://static001.geekbang.org/resource/image/1a/21/1ae285029b67aa128fd34c5cc3caf721.jpg)

两个事务T1、T2都要在同一张表中插入记录：

- T1先执行，得到主键25
- T2后执行，得到26

![](https://static001.geekbang.org/resource/image/7e/54/7e48552810fcc500aec4f4c253a18e54.jpg)

但T1事务还要操作其他数据库表，结果不走运，异常，T1须回滚。T2事务则正常执行成功，完成了事务提交：

![](https://static001.geekbang.org/resource/image/f8/6e/f8890f3f251cc74808f6fyy53a29526e.jpg)

数据表中就缺少了主键25记录，而当下一个事务T3再次申请主键得到27，25成永远的空洞。

#### 为啥不支持连续递增？

因为自增字段所依赖的计数器不是和事务绑定。如要做到连续递增，就要保证计数器提供的每个主键都被使用。

咋确保每个主键都被使用？就要等待使用主键的事务都提交成功。须前一个事务提交后，计数器才能为后一个事务提供新的主键，这计数器就变成一个表级锁。如存在这么大粒度的锁，性能肯定很差，所以MySQL优先选择性能，放弃连续递增。至于那些因事务冲突被跳过的数字，系统也不会再回收重用，因为要保证自增主键单调递增。

虽实现不了连续递增，但至少保证单调递增，还行。但这单调递增有时也不能保证。

### 3.2 无法单调递增

单体数据库，自增主键确实单调递增。但使用自增主键有前提：主键生成的速度要能满足应用系统的并发需求。

而高并发量时，每个事务都要申请主键，数据库如无法及时处理，自增主键就成为瓶颈。这时只用自增主键已不能解决问题，还要在应用系统优化。

如Oracle常见优化方式由Sequence负责生成主键的高位，由应用服务器负责生成低位数字，拼成完整主键。

![](https://static001.geekbang.org/resource/image/f2/0c/f2b6cc531b0dbec7d19f191b6225b20c.jpg)

数据库Sequence是5位的整型数字，范围从10001~99999。每个应用系统实例先拿到一个号，如10001，应用系统在使用这5位为作为高位，自己再去拼接5位的低位，得到10位主键。这样，每个节点访问一次Sequence就可处理99999次请求，处理过程是基于应用系统内存中的数据计算主键，无磁盘I/O开销，而相对的Sequence递增时是要记录日志的，所以方案改进后性能大幅提升。

这方案虽使用Sequence，但也只能保证全局唯一，数据表中最终保存的主键不再是单调递增。因为，几乎所有数据库中的自增字段或自增序列都要记录日志，也就都会产生磁盘I/O，都面临这性能瓶颈。所以，可得结论：在一个海量并发场景下，即使借助单体数据库的自增主键特性，也不能实现单调递增。

## 4 自增主键的问题

分布式数据库，自增主键带来麻烦更大：

- 自增主键的产生环节
- 自增主键的使用环节

难在单调递增。单调递增要求和全局时钟的TSO相似。TSO实现复杂，易成为系统瓶颈，如再用作主键发生器，不大合适。

使用单调递增主键，也给分布式数据库写入带来问题。这是在Range分片下发生的“尾部热点”。

### 4.1 尾部热点

一组性能测试数据看尾部热点问题的现象，这些数据和图表来自[CockroachDB官网](https://www.cockroachlabs.com/blog/unpacking-competitive-benchmarks/)：

![](https://static001.geekbang.org/resource/image/53/d2/5324e7ee83485724b062d6e8e72bcdd2.png)

这本身是CockraochDB与YugabyteDB的对比测试。测试环境使用亚马逊跨机房三节点集群，执行SQL insert操作时：

- YugabyteDB TPS=58,877
- CockroachDB的TPS=34,587

YugabyteDB集群三节点上的CPU都得到充分使用，而CockroachDB集群中负载主要集中在一个节点上，另外两个节点的CPU多数情况都处于空闲状态。

为啥CockroachDB的节点负载这么不均衡？因为CockroachDB默认设置为Range分片，而测试程序的生成主键是单调递增的，所以新写入的数据往往集中在一个 Range 范围内，而Range又是数据调度的最小单位，只能存在于单节点，那么这时集群就退化成单机的写入性能，不能充分利用分布式读写的扩展优势了。当所有写操作都集中在集群的一个节点时，就出现了我们常说的数据访问热点（Hotspot）。

图中也体现了CockroachDB改为Hash分片时的情况，因为数据被分散到多个Range，所以TPS一下提升到61,113，性能达到原来的1.77倍。

性能问题根因找到了，就是同时使用自增主键和Range分片。Range分片很多优势，使其成为不能轻易放弃的选择。于是，主流产品默认方案保持Range分片，放弃自增主键，转用随机主键。

## 5 随机主键方案

产生方式分：

- 数据库内置
- 应用外置

对应用开发者来说，内置方式使用更简便。

### 5.1 内置UUID

可能是最经常使用的唯一ID算法，CockroachDB也建议使用UUID作主键，并内置同名的数据类型和函数。UUID由32个16进制数字组成，所以每个UUID长度128位（16^32 = 2^128）。UUID作为一种广泛使用标准，有多个实现版本，影响它因素包括时间、网卡MAC地址、自定义Namesapce等。

缺点也明显，键值长达128位，存储和计算的代价都增加。

### 5.2 内置Radom ID

TiDB默认支持自增主键，对未声明主键的表，会提供隐式主键_tidb_rowid，因为这个主键大体上是单调递增的，所以也会出现我们前面说的“尾部热点”问题。

TiDB也提供UUID函数，在4.0版本中还提供了另一种解决方案AutoRandom。TiDB 模仿MySQL的 AutoIncrement，提供了AutoRandom关键字用于生成一个随机ID填充指定列。

![img](https://static001.geekbang.org/resource/image/4y/86/4yy3c805599ebb98ba418a4c63220986.jpg)

这个随机ID是一个64位整型，分为三个部分。

- 第一部分的符号位没有实际作用。
- 第二部分是事务开始时间，默认为5位，可以理解为事务时间戳的一种映射。
- 第三部分则是自增的序列号, 使用其余位。

AutoRandom可以保证表内主键唯一，用户也不需要关注分片情况。

### 外置Snowflake

Twitter分布式项目采用的ID生成算法。指定机器 & 同一时刻 & 某一并发序列，是唯一的。据此可生成一个64 bits的唯一ID（long）。默认采用图中的字节分配：

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/06/349f2bba7c6850cebd12391845baae99.jpg)

生成的ID是64位长整型long：

- sign(1bit)，1位符号位，值为 0,没有实际意义，主要为兼容长整型的格式。固定1bit符号标识，即生成的UID为正数
- 41位时间戳，记录本地的毫秒时间。41 位的时间戳可以容纳的毫秒数是 2 的 41 次幂,一年所使用的毫秒数是365 * 24 * 60 * 60 * 1000,即 69.73 年。即ShardingSphere的SnowFlake算法的时间纪元从2016年11月1日零点开始，可用到2086年
- 10 bit工作进程位，机器ID，机器就是生成ID的节点，用10位长度给机器做编码，那意味着最大规模可以达到1024个节点（2^10）。前 5 个 bit 代表机房 id,后 5 个 bit 代表机器i
- 12位序列号，某个机房某台机器上在一毫秒内同时生成的 ID 序号。如果在这个毫秒内生成的数量超过 4096(即 2 的 12 次幂),那么生成器会等待下个毫秒继续生成。序列的长度直接决定了一个节点1毫秒能够产生的ID数量，12位就是4096（2^12）

TODO：上下该信谁的实现？

- delta seconds (28 bits)
  当前时间，相对于时间基点"2016-05-20"的增量值，单位：秒，最多可支持约8.7年
- worker id (22 bits)
  机器id，最多可支持约420w次机器启动。内置实现为在启动时由数据库分配，默认分配策略为用后即弃，后续可提供复用策略。
- sequence (13 bits)
  每秒下的并发序列，13 bits可支持每秒8192个并发。

**以上参数均可通过Spring自定义**。

据数据结构推算：

- 每秒可以产生 26 万个自增可排序的 ID
- 支持TPS可达（2^22*1000，419万左右），够绝大多数系统。

但实现雪花算法时，注意时间回拨影响。机器时钟若回拨，产生的ID就可能重复，需在算法中特殊处理。

SnowFlake算法依赖时间戳，需考虑时钟回拨，即服务器因时间同步，导致某部分机器的时钟回到过去的时间点。时间戳的回滚肯定导致生成一个已用过的ID，因此默认分布式主键生成器提供一个最大容忍的时钟回拨ms数：

- 若时钟回拨时间超过最大容忍的毫秒数阈值，则程序报错
- 在可容忍范围内，默认分布式主键生成器会等待时钟同步到最后一次主键生成的时间后，再继续工作

## 6 总结

单体数据库普遍提供自增主键或序列，自动产生主键。其自增主键保证主键唯一、单调递增，但发生事务冲突时，做不到连续递增。海量并发下，通常不能直接用数据库自增主键，因其性能不够。解决方式：应用系统优化，有数据库控制高位，应用系统控制低位，提升性能。但用这种方案，主键就不再是单调递增。

分布式数据库在两方面都有问题：

- 生成自增主键时，要做到绝对的单调递增，其复杂度等同于TSO全局时钟，而且存在性能上限
- 使用自增主键时，会导致写入数据集中在单个节点，出现“尾部热点”

由于自增主键问题，有的分布式数据库如CockroachDB，更推荐随机主键。其产生机制可分数据库内置、应用系统外置两种。内置方案如CockraochDB的UUID和TiDB的RadomID。外置方案如Snowflake。

![](/Users/<USER>/Downloads/IDEAProjects/java-edge-master/assets/image-20240605110824996.png)

## 7 FAQ

如果分布式数据库使用Range分片的情况下，单调递增的主键会造成写入压力集中在单个节点上，出现“尾部热点”问题。因此，很多产品都用随机主键替换自增主键，分散写入热点。使用随机主键是不是一定能避免“热点”？

参考：

- CockroachDB: [*Yugabyte vs CockroachDB: Unpacking Competitive Benchmark Claims*](https://www.cockroachlabs.com/blog/unpacking-competitive-benchmarks/)


不好说一定能避免出现“热点”。首先，随机主键替换自增主键，确实能分散写入热点。但如果这个写入"热点"超过db分配集群的容量,那么再怎么分散也没有意义。其次，既然是随机，那么脸也很重要，非酋手全落到一个rang分片内，那么热点还是会出现。
对于db的单机性能瓶颈，到底是自增主键先到还是db连接数(并行任务数)先到呢？从栏主的描述，感觉是自增主键会先到。（从自增主键的产生环节的描述来看）



随机主键也不一定避免热点，因为索引也可能有热点：

- 索引列值可能单调递增，如以 created_at 索引，该索引的写入也有尾部热点
- 索引值的基数分布不均匀，如 user_id 索引，但恰巧他是大客户，数据库20%都是同一user_id的数据，那也有热点

Q：oceanbase的自增字段只能保证在一个分区内的单调递增就是为了这个原因吧！印象中oceanbase好像不能使用自增字段做主键？

A：OceanBase在设计自增字段时,正是基于这种分布式场景下单调递增主键可能出现的热点问题。OceanBase在设计主键的特点:

1. 不建议使用自增字段作为主键：OceanBase自增字段只能保证在单个分区内的单调递增,而无法在分区间保证全局唯一和递增，这是为避免单调递增主键可能导致的热点分区问题
   
2. 推荐使用UUID作为主键
   - UUID能够保证全局唯一性,同时又可以利用时间戳等信息实现趋势递增。
   - 这样可以更好地分散写入压力,避免热点分区。

3. 支持自定义主键生成策略
   - OceanBase除了支持UUID,还允许用户自定义主键生成策略。
   - 用户可以根据业务特点设计出合适的主键生成机制,满足分布式场景下的需求。

总的来说,OceanBase在主键设计上的这些特点,正是为了更好地适应分布式环境,解决单调递增主键可能引发的性能问题。这也反映了OceanBase在分布式数据库设计方面的一些独特经验和思考。

Q：随机主键的若是64位Long，再使用Range分区，某段时间内某个分区依然还是热点吧？

A：是的，因为:

1. 数据写入分布不均匀：
   - 虽64位Long类型UUID可以生成足够多的唯一标识，但实际数据写入的分布可能仍不均匀
   - 如若业务存在一些高并发热点数据，相应的写入会集中在某几个分区中，导致热点

2. 分区范围设计不当：
   - Range分区的设计需要根据业务数据特点进行合理划分
   - 如果分区范围设置不当,也很容易导致某些分区承担大量的写压力

3. 数据访问模式问题：
   - 即使主键是随机的,但如果业务中大部分查询都集中在某个时间范围内,也会使得相应的分区变成热点。

为缓解这种热点分区，可采取：

- 使用更细粒度的分区策略,如按时间+其他维度进行分区
- 考虑哈希分区等方式,尽量保证数据写入均匀性
- 监控分区使用情况,动态调整分区策略
- 引入缓存、读写分离等措施,分散热点压力

总之,在分布式系统中,即使使用了随机主键,也需要结合具体的业务特点和访问模式,采取针对性的方案来应对热点分区的问题。这需要设计人员深入理解业务,并持续优化系统架构。



自增主键本身在单体数据库中不是好设计，应该定义自己的主键或流水号规则。分布式系统需要一个流水号分配中心，类似Oracle的解决方案，分配一个号段先持久化，然后对外发放，异常后+X来避免重复分配，保障流水号唯一。



Q：分布式下，做到全局唯一和趋势递增更简单，别想着单调递增？