## 1 前言

分布式数据库已是技术新潮，产品越来越多，技术选型或想学习，如何下手？怎么更高效了解不同产品特点？就要分类，有些差不多产品，熟悉其中一个，剩下只要记下差异点。

如何分类？业界已有共识，分布式数据库可按架构风格分为：

- NewSQL，代表系统是Google Spanner
- 从单体数据库中间件基础上演进出来的，Prxoy风格，无公认的代表系统。Prxoy名太笼统，无法反映架构全貌，还要有具体的架构模板，出现较早的产品来指代这种风格，即PostgreSQL-XC（简称PGXC）

## 2 数据库的基本架构

先了解“数据库（关系型数据库）”架构。数据库全貌：

![](https://img-blog.csdnimg.cn/d948f895ef3548b6ba6454e97c0a2854.png)

约瑟夫 · 海勒斯坦(<PERSON>)等人论文“[Architecture of a Database System](https://dsf.berkeley.edu/papers/fntdb07-architecture.pdf)”翻译而来。

将数据库从逻辑拆为:

- 客户端通讯管理器(Client Communications Manager)
- 查询处理器（Relational Query Processor）
- 事务存储管理器（Transactional Storage Manager）
- 进程管理器（Process Manager）
- 共享组件与工具(Shared Components and Utilities)

你在各种数据库产品中都能找到这5个部分的对应实现，比如Oracle、DB2、SQL Server和MySQL，无一例外。下面，我依次介绍下这5个部分的功能。

1. **客户端通讯管理器。**这是应用开发者能够直观感受到的模块，通常我们使用JDBC或者ODBC协议访问数据库时，连接的就是这个部分。
2. **进程管理器。**连接建好了，数据库会为客户端分配一个进程，客户端后续发送的所有操作都会通过对应的进程来执行。当然，这里的进程只是大致的说法。事实上，Oracle和PostgreSQL是进程的方式，而MySQL使用的则是线程。还有，进程与客户也不都是简单的一对一关系，但这部分功能不会影响你对分布式数据库的理解，可以略过。
3. **查询处理器。**它包括四个部分，功能上是顺序执行的。首先是解析器，它将接收到的SQL解析为内部的语法树。然后是查询重写（Query Rewrite），它也被称为逻辑优化，主要是依据关系代数的等价变换，达到简化和标准化的目的，比如会消除重复条件或去掉一些无意义谓词 ，还有将视图替换为表等操作。再往后就是查询算法优化（Query Optimizer），它也被称为物理优化，主要是根据表连接方式、连接顺序和排序等技术进行优化，我们常说的基于规则优化（RBO）和基于代价优化（CBO）就在这部分。最后就是计划执行器（Plan Executor），最终执行查询计划，访问存储系统。
4. **事务存储管理器。**它包括四个部分，其中访问方式（Access Methods）是指数据在磁盘的具体存储形式。锁管理（Lock Manager）是指并发控制。日志管理（Log Manager）是确保数据的持久性。缓存管理（Buffer Manager）则是指I/O操作相关的缓存控制。
5. **共享组件和工具。**在整个过程中还会涉及到的一些辅助操作，当然它们对于数据库的运行也是非常重要的。例如编目数据管理器（Catalog Manager）会记录数据库的表、字段、视图等元数据信息，并根据这些信息来操作具体数据内容。复制机制（Replication）也很重要，它是实现系统高可靠性的基础，在单体数据库中，通过主备节点复制的方式来实现数据的复制。

这就能串起PGXC和NewSQL两种架构风格的关键功能。数据库本身运行机制复杂，可研读约瑟夫 · 海勒斯坦这篇论文。

## 3 PGXC：单体数据库的自然演进

单体数据库功能看似完善，但高并发时写性能不足。因此，就有向分布式数据库的动力。要解决写性能不足，最简单的分库分表。

在多个单体数据库前增加代理节点，本质增加SQL路由：

- 代理节点先解析客户端请求
- 再根据数据的分布情况
- 将请求转发到对应的单体数据库

![](https://img-blog.csdnimg.cn/100d618ce5554de8af082e7e13161dd1.png)

代理节点需要实现：

- 客户端接入
- 简单的查询处理器
- 进程管理中的访问控制

分库分表方案一大重要功能：分片信息管理，分片信息即数据分布情况，区别于编目数据的一种元数据。考虑到分片信息也存在多副本的一致性问题，大多会独立出来。

若把每次的事务写入都限制在一个单体数据库，业务场景就很局限。因此，跨库事务必不可少，但单体数据库不感知这事，就得在代理节点增加分布式事务组件。

简单的分库分表不能满足全局性查询，因为每个数据节点只能看到一部分数据，有些查询运算无法处理，如排序、多表关联。代理节点就要增强查询计算能力，支持跨多个单体数据库的查询。

随分布式事务和跨节点查询等功能加入，代理节点已不再只是简单路由功能，更多时候称协调节点。

![](https://img-blog.csdnimg.cn/0bf988a9d2c6436f8ed76f186d581d6e.png)

很多分库分表方案会演进到该阶段，如MyCat。这时离分布式数据库还差全局时钟。全局时钟是实现数据一致性的必要条件。

加上这最后一块拼图，PGXC区别于单体数据库的功能也就介绍完整了，它们是分片、分布式事务、跨节点查询和全局时钟。

![](https://img-blog.csdnimg.cn/6f376875d57b4fbc93ce382b9edce7f8.png)

协调节点与数据节点，实现了一定程度上的计算与存储分离，这也是所有分布式数据库的一个架构基调。但因PGXC的数据节点本就是完整的单体数据库，也具备很强算力。

PGXC风格的分布式数据库到底包括哪些产品呢？PGXC（PostgreSQL-XC）本意指一种以PostgreSQL为内核的开源分布式数据库。因为PostgreSQL的影响力和开放的软件版权协议（类似BSD），很多厂商在PGXC上二次开发，推出自己产品。但这些改动都没变更主体架构风格，这类产品统称PGXC风格，包括TBase、GuassDB 300和AntDB等。PGXC不限于以PostgreSQL为内核，那些以MySQL为内核产品也采用同样架构，如GoldenDB，所以我把它们也归入了PGXC风格。

## 4 NewSQL：革命性架构

相对于PGXC，NewSQL有完全不同发展路线。NewSQL也叫原生分布式数据库，这名更准确体现这类架构风格的特点，它的每个组件都是基于分布式架构，不像PGXC带有明显的单体架构痕迹。

NewSQL基础是NoSQL，类似BigTable的分布式KV系统。分布式KV系统选择做减法，完全放弃数据库事务处理能力，重点放在对存储和写入能力的扩展，这能力扩展的基础就是分片。引入分片的另一好处：系统能以更小粒度调度数据，实现各节点上的存储平衡和访问负载平衡。

分布式KV系统由于具备这些特点，在细分场景获得成功（如电商网站对商品信息存储），但面对大量的事务处理场景时就无能为力（如支付系统）。这直到Google Spanner出世才被改变，因为Spanner基于BigTable构建新的事务能力。

NewSQL重要革新：

- 高可靠机制
- 存储引擎的设计

高可靠机制变化在于，放弃粒度更大的主从复制，转而以分片为单位采用Paxos或Raft等共识算法。NewSQL就实现了更小粒度的高可靠单元，获得更高的系统整体可靠性。存储引擎层，使用LSM-Tree模型替换B+ Tree模型，大幅提升写性能。

由于NewSQL架构革新，产品实现难度比PGXC大，产品就相对少：

- Spanner是NewSQL鼻祖
- 其他知名度较高产品CockroachDB、TiDB和YugabyteDB都宣称设计灵感来自Spanner
- 阿里OceanBase，因为它有一个代理层，有时会被同行质疑，但是从整体架构风格归NewSQL

![](/Users/<USER>/Downloads/IDEAProjects/java-edge-master/assets//67b03095173a1cf570cdeec485b7aa87.jpg)

系统架构上，NewSQL设计思想更领先，有里程碑意义，而PGXC架构偏保守。但PGXC优势在稳健，直接采用单机数据库作数据节点，大幅降低工程开发工作量，也减少引入风险：

- NewSQL长在架构设计
- PGXC长在工程实现

NewSQL架构设计也不是完美，作为计算存储分离更彻底的架构，NewSQL计算节点需借助网络才能与存储节点通讯，要花费更大代价传输数据。随NewSQL分布式数据库应用实践越来越多，很多产品为获得更好计算性能，会尽量将更多计算下压到存储节点执行。这种架构修正，似乎也可理解为，NewSQL朝PGXC方向做了点回拨。

##  5 总结

1. 架构上，数据库可以被拆分为5个部分，分别是客户端通讯管理器、进程管理器、查询处理器、事务存储管理器和共享组件与工具。分布式数据库在此基础上增加四个主要功能，包括分片信息管理、分布式事务管理、跨节点查询和全局时钟。
2. PGXC架构是从分库分表方案演进而来的。它设置了协调节点，在代理功能的基础上增加了分布式事务管理、跨节点查询功能；原有的单体数据继续作为数据节点；新增了全局时钟和分片信息管理两个功能，这两个功能又有两种实现情况，一是拆分为两个独立角色节点，例如GoldenDB，二是合并为一个角色节点，例如TBase。
3. NewSQL架构是原生分布式数据库，架构中的每个层次的设计都是以分布式为目标。NewSQL是从分布式键值系统演进而来，主要的工作负载由计算节点和存储节点承担，另外由管理节点承担全局时钟和分片信息管理功能。不过，这三类节点是逻辑功能上划分，在设计实现层面是可分可合的。比如，TiDB是分为独立节点，CockroachDB则是对等的P2P架构。
4. NewSQL在架构上更加领先，而PGXC最大程度复用了单体数据库的工程实现，更加稳健。

本文从单体数据库架构出发，简单介绍PGXC和NewSQL两种架构。为把握要点，内容专门挑选最能体现与单体数据库差异部分。这些内容尚不足以完全解释数据库的整体运作原理，但对理解两种架构风格的分布式数据库产品的基本框架够了。

![](https://img-blog.csdnimg.cn/6b9f964d692d4715b83ad779f5990b7c.png)

参考

- Joseph M. Hellerstein et al.：[*Architecture of a Database System*](https://dsf.berkeley.edu/papers/fntdb07-architecture.pdf)
- 加西亚-莫利纳 等：[《数据库系统实现》](https://book.douban.com/subject/4838430/)

## 6 FAQ

想学习下分布式数据库源码，newsql有没有java实现的分布式数据库，golang看起来比较累

多数都是Go, Rust, C/C++开发的。Java看HBase源码，理解分布式K/V系统有助。







在传统数据库之上，不做分片和分表等逻辑，基于共识协议达成多个节点的数据一致性，这类产品怎么算归属？

没有分片就很难扩展存储和运算能力，和传统主备复制相比，只是复制协议的调整，那就仍然是单体数据库。

Q：腾讯的TDSQL应该也是属于PGXC类的，也是比较依赖于proxy？

A：TDSQL是比较依赖Proxy，且以MySQL作为数据节点。但说它属PGXC，有点纠结。因为现在TDSQL还缺少全局时钟，严格说还不是，不过它也在调整。目前腾讯输出的TDSQL是没有全局时钟的，也没有解决全局一致性的问题，所以严格来说，不是PGXC架构。但是，我们通过一些公开资料可以了解到，TDSQL也正在调整架构努力解决这些问题。

Q：hbase nosql kv数据库是不是完全可用pgxc架构数据库替代，因为在pgxc不仅可存储海量数据且支持更复杂计算，使用场景多。hbase优势在可通过region分裂达到更好负载平衡，但这pgxc实现应该也不难。

A：技术选型，还得根据数据访问模式匹配应用场景。如KV存储在点查询优势是PGXC很难超越的。PGXC要做动态分片也不容易，这要动下面的数据节点，也就是要改单体数据库。

Q：greenplum是不是也是pgxc风格？

A：GP确实是PostgreSQL为核，但它主要用在OLAP场景，而PGXC用于OLTP场景。

Q：TiDB中PD转移至TiKV项目，是否算"NewSQL 朝 PGXC 的方向做了一点回拨"，亦或是通讯链路边长、网络通讯“代价”过大。
吹了很久的微服务的风，问题也逐渐暴露。现有“分布式”向“单体”回拨的趋势，如Istio。

A：PD转移到TiKV？没听说。微服务架构的复杂性确实大家也在重新反思，确实未必都要用微服务架构。

Q：最近用ck，感觉真快，也能解决一些OLAP和OLTP混合场景！

A：ck是很有特点，但与OLTP场景需求差异还蛮大，如只能批量加载数据。ck还是主要适合在部分OLAP场景使用。

newsql支持结构化数据，nosql半结构化数据偏多，都有存在意义，只是对比架构。熟悉nosql是Redis，因为Redis集群不支持集群的跨节点事务（单线程支持单机事务很容易，lua脚本即可），使得redis集群架构简单高效，哈希槽分片，gossip传递集群信息，当然分片KV也不必支持全局时钟，使redis做单纯KV服务非常高效。

对比Newsql，因为我们下的定义是关系型数据库，需支持分布式事务，复杂且昂贵（spanner使用的两阶段提交本身还有单点故障，数据不一致的问题），因为数据更改顺序很重要，所以也需全局时钟，更不必说单机关系型数据库本身逻辑复杂性，所以不但读写都非常昂贵，也使架构很复杂（spanner为物理时钟而引入time master和每台机器上的timeslave daemon，分布式事务要映入事务管理者，此时也要锁表）。当然它们都有适合场景，还是取决数据类型与具体业务取舍。

觉得PGXC与Newsql的界限很模糊，因为它们需要支持分片，事务，全局时钟等，分片需要分机器（newsql粒度更小，spanner以一个Tablet组为粒度），大家都一样，分布式事务则需要协调者，大家也一样，全局时钟也都是依靠外界，那么是否可以理解为它们的差别就是单个机器上是运行仅进行存储（当然还有各种日志）的程序还是一个完整DBMS呢？

Q：全局时钟的话，还能调整系统时间吗？往回调整时间允许吗？

A：全局时钟意义就是单调递增，所以不能回调时间。正常也不应有回调需求。

Q：NewSQL是在nosql基础上进化，就是比nosql 多事务功能？还是有更多变化？像mongodb也提供了事务功能，它不属于NewSQL？

NewSQL与NoSQL最大差异在强一致性：数据一致性和事务一致性，其他技术实现则是细节。MangoDB不属NewSQL，因其不是传统意义关系模型，而事务的增加确实是MangoDB向数据库的一个重要转变，为它拓展更多OLTP场景。