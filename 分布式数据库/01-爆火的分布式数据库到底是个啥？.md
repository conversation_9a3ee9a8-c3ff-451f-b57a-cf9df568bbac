## 1 啥是分布式DB？

TiDB为首分布式DB兴起，赋予关系型DB一定的分布式特性。数据分片及分布式事务都是内置功能。业务开发只需用框架对外提供的JDBC接口，就像用MySOL传统关系型DB一样。

而shardingSphere是一种分布式DB中间件，提供标准化的数据分片解决方案，分布式事务和DB治理功能。

### 1.1 事实标准

当一个技术产品占据市场主导，自然就成为同类产品事实标准。如关系型DB，Oracle就是事实标准，因为所有 DB产品发布新版本时，都要去和Oracle比。手机发布新功能都和iPhone比。

分布式DB作为新兴基础软件，尚无产品占据“事实标准”位置。既无参照，就自定义分布式DB概念。由表及里、由外到内，是认识事物的普遍规律。

## 2 外部视角：外部特性

分布式 DB具备啥特性，能解决啥痛点。

业务应用系统按交易类型分类：

- 联机交易（OLTP）：面向交易的处理过程，单笔交易数据量很小，但要在短时间给出结果，如购物、缴费、转账

- 联机分析（OLAP）：通常基于大数据集的运算，如生成个人年度账单、企业财务报表


难有产品完全满足，因此单体DB时代演化两类不同关系型DB。向分布式架构演进后，两者在架构设计也采用完全不同策略，很难一个框架说清。

### OLTP场景下的分布式DB

本专栏的“DB”默认“关系型DB”，分布式DB也都指支持关系模型的分布式DB。即不讨论NoSQL，整体看，关系型DB因支持SQL、提供ACID事务，具有更好通用性，在更广泛场景中无法被NoSQL取代。

分布式DB目标正是融合传统关系型 DB与NoSQL DB的优势，已取得不错效果。

## 3 定义

### 3.1 OLTP关系型 DB

仅用“OLTP场景”作为定语显然不够精准，我们来进一步看看OLTP场景具体的技术特点。

OLTP场景的通常有三个特点：

- **写多读少**，指请求数量。而且读操作的复杂度较低，一般不涉及大数据集的汇总计算
- **低延时**，用户对于延时的容忍度较低，通常在500毫秒以内，稍微放大一些也就是秒级，超过5秒的延时通常是无法接受的；
- **高并发**，并发量随着业务量而增长，没有理论上限。

我们是不是可以有这样一个结论：**分布式 DB是服务于写多读少、低延时、高并发的OLTP场景的 DB**。

### 3.2 海量并发

你可能会说这个定义有问题，比如MySQL和Oracle这样的关系型 DB也是服务于OLTP场景的，但它们并不是分布式 DB。

相对传统关系型 DB，分布式 DB最大差异就是分布式 DB远高于前者的并发处理能力。

传统关系型 DB往往是单机模式，主要负载运行在一台机器。 DB的并发处理能力与单机的资源配置是线性相关的，所以并发处理能力的上限也就受限于单机配置的上限。这种依靠提升单机资源配置来扩展性能的方式，即垂直扩展（Scale Up）。

在一台机器中，随随便便就能多塞进些CPU和内存来提升提性能吗？当然没那么容易。所以，物理机单机配置上限的提升是相对缓慢的。即在一定时期内，依赖垂直扩展的 DB总会存在性能的天花板。很多银行采购小型机或大型机的原因之一，就是相比x86服务器，这些机器能够安装更多的CPU和内存，可以把天花板推高一些。

而分布式 DB不同，在维持关系型 DB特性不变的基础上，它通过水平扩展增加机器数量，提供远高单体 DB的并发量。这个并发量几乎不受单机性能限制，我将这个级别的并发量称为“海量并发”。

#### “海量并发”到底多大

没权威数字。虽然理论上是可以找一台世界上最好的机器来测试一下，但考虑到商业因素，这个数字不会有什么实际价值。不过，我可以给出一个经验值，这个“海量并发”的下限大致是10,000TPS。

2.0版本的定义：**分布式 DB是服务于写多读少、低延时、海量并发OLTP场景的关系型 DB**。

### 3.3 高可靠

V2仍有问题。没有高并发就不需要分布式DB了？不是的，你还要考虑DB的高可靠性。一般可靠性和硬件设备的故障率有关。

与银行不同，很多互联网公司中小企业采用x86服务器，故障率相对高，年故障率5%。更可靠数据来自Google论文[*Failure Trends in a Large Disk Drive Population*](http://bnrg.eecs.berkeley.edu/~randy/Courses/CS294.F07/11.3.pdf)，详细探讨通用设备磁盘的故障情况。磁盘年度故障率的统计：

![](https://img-blog.csdnimg.cn/bda4bce7fccd4fcba7ab4af20a506ce7.png)

前3月超过2%的磁盘损坏率，到第二年这个数字会上升到8%左右。

这数字也不高啊！金融行业关键应用系统通常要求5个9可靠性（99.999%），即一年系统服务中断时间不能超过5.26分钟（365`*`24`*`60`*`（1-99.999%） ≈ 5.26 ）。随人们对互联网依赖，越来越多的系统都有高可靠性要求。

如你公司有四、五个关键业务系统，十几台DB服务器，磁盘数量一定超100。保守估计按损坏率2%，一年就碰到2次磁盘损坏，要达5个9只有5.26分钟，能处理完一次磁盘故障吗？几乎做不到，可能你刚冲到机房，时间用尽。

我猜你会建议用RAID（独立冗余磁盘阵列）来提高磁盘的可靠性。这确实是一个办法，但也会带来性能上的损耗和存储空间上的损失。分布式 DB的副本机制可以比RAID更好地平衡可靠性、性能和空间利用率三者的关系。副本机制就是将一份数据同时存储在多个机器上，形成多个物理副本。

回到 DB的话题上，可靠性还要更复杂一点，包括两个度量指标，恢复时间目标（Recovery Time Objective, RTO）和恢复点目标（Recovery Point Objective, RPO）。RTO是指故障恢复所花费的时间，可以等同于可靠性；RPO则是指恢复服务后丢失数据的数量。

 DB存储着重要数据，而金融行业的 DB更是关系到客户资产安全，不能容忍任何数据丢失。所以， DB高可靠意味着RPO等于0，RTO小于5分钟。

传统上，银行通过两种方法配合来实现这个目标。

第一种还是采购小型机和大型机，因为它们的稳定性优于x86服务器。

第二种是引入专业存储方案，例如EMC的Symmetrix远程镜像软件（Symmetrix Remote Data Facility, SRDF）。 DB采用主备模式，在高端共享存储上保存 DB文件和日志，使 DB近似于无状态化。主库一旦出现问题，备库启动并加载共享存储的文件，继续提供服务。这样就可以做到RPO为零，RTO也比较小。

但是，这套方案依赖专用的软硬件，不仅价格昂贵，而且技术体系封闭。在去IOE（IBM小型机、Oracle DB和EMC存储设备）的大背景下，我们必须另辟蹊径。分布式 DB则是一个很好的备选方案，它凭借节点之间的互为备份、自动切换的机制，降低了x86服务器的单点故障对系统整体的影响，提供了高可靠性保障。

令人兴奋的是，这种单点故障处理机制甚至可以延展到机房层面，通过远距离跨机房部署。如此一来，即使在单机房整体失效的情况下，系统仍然能够正常运行， DB永不宕机。

3.0定义，**分布式 DB是服务于写多读少、低延时、海量并发OLTP场景的，高可靠的关系型 DB**。

### 3.4 海量存储

虽然单体 DB依靠外置存储设备可以扩展存储能力，但这种方式本质上不是 DB的能力。现在，借助分布式的横向扩展架构，通过物理机的本地磁盘就可以获得强大的存储能力，这让海量存储成为分布式 DB的标配。

最后，我们终于得到一个4.0终极版本的定义，**分布式 DB是服务于写多读少、低延时、海量并发OLTP场景的，具备海量数据存储能力和高可靠性的关系型 DB**。

## 4 内部视角：内部构成

具有相同的外在特性和功效，未必就是同样的事物。

“日心说”反驳“地心说”要用到34个圆周解释天体运动轨迹；而100多年后，开普勒只用7个椭圆就达到同样效果，彻底摧毁“地心说”。从哥白尼到开普勒，效果近似，简洁程度大不一样，这背后代表的是巨大科学进步。

因此，讲完外部特性，还要从内部视角观察。

为应对海量存储和海量并发，很多解决方案在效果上跟V4定义相似。但它们向用户暴露了太多的内部复杂性。用户约束太多、使用过程太复杂、不够内聚的方案，不能称为成熟产品。同时，业界的主流观点并不认为它们是分布式 DB。

来看分类：

### 4.1 客户端组件 + 单体 DB

通过独立的逻辑层建立数据分片和路由规则，实现单体 DB的初步管理，使应用能够对接多个单体 DB，实现并发、存储能力的扩展。其作为应用系统的一部分，对业务侵入比较深。

典型产品Sharding-JDBC：

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/11/b94db2983d70b6e5852b74a29ad517aa.jpg)

### 4.2 代理中间件 + 单体 DB

独立中间件，管理数据规则和路由规则，独立进程存在，与业务应用层和单体 DB隔离，减少对应用的影响。随代理中间件的发展，还衍生出部分分布式事务处理能力。典型产品MyCat。

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/11/93893f67c28d1516e9bd9654c03dc221.jpg)

### 4.3 单元化架构+单体DB

单元化架构是对业务应用系统的彻底重构，应用系统被拆成若干实例，配置独立单体DB，让每个实例管理一定范围的数据。

如银行贷款系统，可为每个支行搭建独立的应用实例，管理支行各自用户。跨支行业务时，由应用层代码通过分布式事务组件保证事务ACID。

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/11/87f66977ff88e64f3f94124495e3a8c4.jpg)

根据不同分布式事务模型，应用系统要配合改造，复杂性相应增加。如TCC下的应用须提供幂等。

分布式DB出现前，一些头部互联网公司使用过这种架构风格，该方案的应用系统改造量最大，实施难度最高。

共同特点是单体DB仍能被应用系统感知。相反，分布式DB**将技术细节收敛到产品内部，以一个整体面对业务应用。**

## 5 亚马逊的Aurora

和这里说的分布式DB有明显差别，了解Aurora或同类产品吗？和本文分布式DB差异在哪？为啥会有这种差异？

为啥说Aurora不是分布式DB？Aurora存算分离，使亚马逊云存储服务更高效、易使用。算是NewSQL中比较成功的了。

### 特点

Aurora是提出一种新的单体架构以减少网络IO和同步阻塞，逻辑上可以看做一个庞大的单体 DB，用分布式来支持容错和高吞吐量。：

- Aurora分片的方式是将 DB的总容量划分为固定大小的数据段，在每一段内存储数据，每一个段是一组机器（六个），个人觉得算支持分片

- 写多读少、低延时这就是Aurora所重点的做的事情，通过log is database和支持异步来实现
- 海量存储可靠堆机器（当然这些机器肯定是有一个控制中心来管理的，论文中没有提）
- 高可靠则是靠每一个数据段把数据冗余到三个可用区的六台机器
- 而且它同样也是关系型DB

但Aurora特点是Share storage，计算节点垂直扩展，存储节点水平扩展，写入性能收单机资源的影响。

#### 投票机制

aurora也用到了，6个副本，半数以上就确认写入成功。但无分片，不能多写，肯定不算分布式。

不能多写（重点！），适用场景有很大区别，所以这是个重要标准。但因为Aurora是基于共享存储，所以说它是分布式也不是没道理。定标准只是为让学习思路清晰。

#### 实际场景Aurora和分布式存储的应用的差别

Aurora还是关系型 DB，而分布式存储系统范围较广，比如HBase这样的分布式键值系统。两者在功能上有很大差异。

AWS aurora，阿里polarDB，腾讯CynosDB，华为的Taurus等产品都是类似架构：计算存储分离。所有计算节点都访问存储节点上的同一份数据，也可以说是分布式架构。这架构的局限是写入不能横向扩展，对很多小规模应用够了，所以不影响它取得商业成功。

### 阿里的PolarDB是分布式DB？它采用哪种方案？

PolarDB和Aurora架构类似，存算分离，计算节点垂直扩展，存储节点水平扩展。代表其写入能力有上限，但因简化了日志存储和其他一些优化，单点能力比普通MySQL强很多。

## 6 总结



![](https://img-blog.csdnimg.cn/93bf36f993c049388bdf103a3f2beecc.png)

逐层递进，勾勒出分布式 DB的六个外部特性：**写多读少、低延时、海量并发、海量存储、高可靠性、关系型 DB。**

也存在一些与分布式 DB能力近似解决方案，它们不足之处是都需要对应用系统进行一定的改造，对应用的侵入程度更深；其优势则在于可以最大程度利用单体 DB的稳定可靠，毕竟这些特性已经历经无数次的考验。

### 分布式DB的名称做一些延伸。

“分布式 DB”在字面上可以分解为“分布式”和“ DB”两部分，代表了它是跨学科的产物，它的理论基础来自两个领域。这同时也呼应了产品发展的两条不同路径，一些产品是从分布式存储系统出发，进而增加关系型 DB的能力；另外一些产品是从单体 DB出发，增加分布式技术元素。而随着分布式 DB的走向工业应用，在外部需求的驱动下，这两种发展思路又呈现出进一步融合的趋势。

## 7 FAQ

### ① 写多读少不应加入分布式DB的定义？

分布式DB服务写多读少应用，我觉得不管写多读多都可应用分布式，关键是单体承担不了这么多请求了（不论读写），所以高并发就够了，写多读少不应加入分布式DB的定义？

强调写多读少，是因为：

- 写操作的负载只能是单体DB的主节点，无法转移
- 而读操作，如对一致性要求不高，可转移到备节点，甚至在某些条件下还能保证一致性。就是说单体DB可通过一主多备解决读负载大问题，而无需引入分布式DB

云dbms在分布式基础，更关注算存分离后可独立扩展，甚至动态扩缩容，self-driven搞起来，更好卖。这也引发问题：

- aurora类DB提出log is database思想，降低写压力
- snowflake通过建立中间分布式换存层，降低网络瓶颈等

### ② 分布式DB V.S 分库分表

Q：分布式DB，感觉就是把客户端或中间件的方案直接作为DB服务端特性组件，让分库分表更自动化？

A：最大区别在于：

- 分布式DB使用体验接近关系型DB，无需应用层额外控制，降低业务代码开发难度
- 分库分表方案在分布式事务和跨节点查询等方面，支持不好

### ③ MyCat咋说？

随分布式DB发展，类MyCat中间件市场越来越小。当然，其使用场景也可能转向异构DB支持

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/8adfdec1f31b794665d09944130770d5.png)

就像Presto：

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/30646a796b3d42d266bd6d4f3bb3a5f4.png)

### ④ 互联网应用数据请求“读多写少”

所以有一主多从读写分离、全量数据缓存等解决“读”问题的扩容手段。如果说的是同一指标，是否意味分布式DB不适合互联网应用？

互联网确实可通过【一主多从】满足“读多写少”，但前提是对读对一致性要求低。而金融场景很多读操作依然无法在备库运行，就是一致性不满足要求。所以，对互联网也不能一概而论，还是区分场景。

### ⑤ 交易场景下，交易代码配合分布式 DB而做出的交易补偿或者数据回放等

如需要交易代码配合做出补偿和回放，这很可能意味着它不是分布式DB。分布式DB成熟前，确有不少应用代码配合单体 DB。这类应用代码也会被抽离出来形成独立框架，如阿里SOFA。

### ⑥ Newsql落地如何？

如北京银行和光大银行都上线TiDB，Oceanbase也在南京银行落地。

### ⑦ BigTable算特殊的（代理中间件 + 单体 DB（分布式文件系统））吗？

毕竟靠Chubby作为一个中间层，不过数据的获取是直接与文件系统中交互完成。

BigTable是分布式KV系统，不属于分布式DB。因为这里所说的分布式DB是分布式架构实现的关系型DB。当然它底层依赖一个分布式文件系统，所以看上去也分两层，但职能和DB差别很大，建议关注PGXC风格分布式DB。

### ⑧ 基于OLAP使用场景的分布式关系型DB产品

最典型的MPP架构DB，如Greenplum和华为的GaussDB 200，内核都使用PostgreSQL。还有Vertica。OLAP不再强调事务支持，如弱化对数据更新要求，很多大数据生态都可纳入，如Clickhouse，Hive on spark，甚至Kylin都算广义OLAP分布式DB。
