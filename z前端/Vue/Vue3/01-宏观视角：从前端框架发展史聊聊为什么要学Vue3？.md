## 0 前言

熟悉这段历史之后，相信你能把握到 Vue 在前端框架中的地位。这样，你就会对 Vue 有一个更精准的定位，从而能够知道我们为什么要选择 Vue 框架，以及Vue的优势和它的真正价值在哪里。

前端网页在这些年很多变化，都能感觉到网页在设计模式、渲染等变化，而变化背后，都可以放到前端框架的演变历史来解释。

## 1 石器时代

前端发展史：

- 1990 年，第一个 Web 浏览器诞生了。这是前端这个技术的起点，代表这一年它出生了
- 1994 年，网景公司发布第一个商业浏览器 Navigator
- 1995 年，网景工程师 Brendan Eich 用 10 天时间蹭Java热度设计js，同年微软发布了 IE 浏览器，掀起浏览器大战
- 2002年，IE在浏览器大战胜，IE6占有率超96%

前端发展史，直观显示在前端网页的演变史。90年代受限网速，网页都是静态，显示单一，前端工作大部分只是让美工切图和写HTML+CSS。因此，1990s的前端处萌发期，前端工程师工种未明确出现。

后来，后端越来越复杂，开始分层。就像小公司大家啥都干，但公司规模大，要分部门，职责明确，代码也从揉在一起发展到MVC负责不同功能。

**这就是后端MVC模式的盛行，让我们可以在模板里写上要展现的数据。以前的代码都是所有内容写在一起，现在就会用Model负责数据。**

后端渲染页面之前，会把数据库的数据显示在前端。这个时候，除了写前端代码必备的HTML、CSS和简单的JavaScript动效，我们也开始用到了JSP和Smarty，我们会写出如下这种代码：

```xml
<!DOCTYPE html>
  <html>
  <head>
  <meta charset="utf-8">
  <title>smarty test1</title>
  </head>
  <body>
  它的名字叫{$name}
  </body>
  </html>

```

上述代码写出来的页面，就可以直接显示后端数据库里的数据了，这也就是所谓的动态网页。动态页面使得前端本身的丰富程度大大提升。这一下子迎来了整个互联网开发的繁荣时期，但这种模式下的任何数据更新，都需要刷新整个页面，并且在带宽不足的年代，这样做会耗费不少加载网页的时间。

所以这个时代的网页主要还是以显示数据和简单的特效为主，比如当时众多的门户网站，也都没有太多的用户交互，主要就是显示后端存储的新闻。

直到2004年，Google发布了Gmail，用户可以在不刷新页面的情况下进行复杂的交互，之后，Ajax逐渐成为网页开发的技术标准，也不断地被应用于各种网站。 **Ajax这个技术让我们可以异步的获取数据并且刷新页面，从此前端不再受限于后端的模板，这也宣告了Web2.0时代正式到来。** 至此，前端工程师也正式作为一个独立工种出现。

## 铁器时代

在Gmail诞生后，虽然依然有浏览器的混战和兼容性问题，如绑定事件，不同浏览器就要写不同代码，但大家意识到前端也可以做出复杂应用。而jQuery的出现迅速风靡全球，一个$走天下，学会jQuery就等同于学会了前端，算是前端车同轴的时代。在这之后，前端的具体开发不再被JavaScript的兼容性问题所困扰。

那个时候 jQuery+Bootstrap一把梭，成为了前端开发领域的主流技术，前端代码内嵌在后端的项目中，写完直接发布，通篇都是如下的代码：

```js
$('#alert-btn').on('click',function(){
  $('#app .input').val('hi')
})
```

那个时候写代码，就是找到某个元素，进行DOM操作，特别像铁器时代的拼刺刀，随着前端项目规模的逐渐提升，前端也需要规模化的时候，在2009年AngularJS和Node.js的诞生，也宣告前端工业革命的到来。

## 工业时代

AngularJS的诞生，引领了前端MVVM模式的潮流；Node.js的诞生，让前端有了入侵后端的能力，也加速了前端工程化的诞生。现在前端三大框架Angular、React、Vue 的发展主线，也就是从这里开始的。

所谓MVVM，就是在前端的场景下，把Controller变成了View-Model层，作为Model和View的桥梁，Model数据层和View视图层交给View-Model来同步，第二讲我们会通过一个清单应用让你熟悉MVVM开发模式和传统jQuery的开发模式的区别，这里你先留个印象就好。

### 前端三大框架

在前端MVVM模式下，不同框架的目标都是一致的，就是利用数据驱动页面，但是怎么处理数据的变化，各个框架走出了不同的路线。

![](/Users/<USER>/Downloads/IDEAProjects/java-edge-master/assets/image-20241024093434127.png)

这些框架要回答的核心问题就是，数据发生变化后，我们怎么去通知页面更新。各大框架在这个步骤上，各显神通：

Angular 1就是最老套的脏检查。所谓的脏检查，指的是Angular 1在对数据变化的检查上，遵循每次用户交互时都检查一次数据是否变化，有变化就去更新DOM这一方法。这个方法看似简单粗暴，但算是数据驱动页面早期的实现，所以一经推出，就迅速占领了MVVM市场。

后面Angular团队自断双臂，完全抛弃Angular 1，搞了一个全新的框架还叫Angular，引入了TypeScript、RxJS等新内容，虽然这些设计很优秀，但是不支持向前兼容，抛弃了老用户。这样做也伤了一大批Angular 1用户的心，包括我。这也是Angular这个优秀的框架现在在国内没有大面积推广的原因。

而Vue 1解决方案是响应式，初始化时，Watcher监听了数据的每个属性，这样数据变化时，就能精确知道数据的哪个key变了，去针对性修改对应DOM。过程解构：

![](/Users/<USER>/Downloads/IDEAProjects/java-edge-master/assets/image-20241024093858984.png)

左侧是实际网页内容，我们在网页用{{}}渲染一个变量，Vue 1就在内容里保存一个监听器监控该变量，即Watcher，数据有变化，watcher会收到通知去更新网页。

通俗来说，若将网页数据看成你管理的员工，普通数据就是那种每次你都需要找到他，告诉他要咋做的人，响应式数据就是他本身有任何变化，都会主动给你发日报告诉你的积极员工。

React团队提出不同于Angular、Vue的解决方案，页面初始化时，在浏览器DOM之上，搞了个虚拟DOM，用一个js对象描述整个DOM树。方便通过虚拟DOM计算变化的数据，去进行精确修改。React中的一段代码：

```xml
<div id = "app">
    <p class = "item">Item1</p>
    <div class = "item">Item2</div>
</div>
```

React中，这一段HTML被映射成一个js对象。这个对象就像数据和实际DOM的一个缓存层，通过管理这个对象的变化，来减少对实际DOM的操作。

这种形式不仅让性能有很好保障，还多了用JSON描述网页的工具，并让虚拟DOM技术脱离Web的限制。因为积累这么多优势，虚拟DOM在小程序，客户端等跨端领域大放异彩。

虚拟DOM在运行时就是这么一个对象：

```json
{
  tag: "div",
  attrs: {
    id: "app"
  },
  children: [
    {
      tag: "p",
      attrs: { className: "item" },
      children: ["Item1"]
    },
    {
      tag: "div",
      attrs: { className: "item" },
      children: ["Item2"]
    }
  ]
}
```

这个对象完整地描述了DOM的树形结构，这样数据有变化的时候，我们生成一份新的虚拟DOM数据，然后再对之前的虚拟DOM进行计算，算出需要修改的DOM，再去页面进行操作。

浏览器操作DOM一直都是性能杀手，而虚拟DOM的Diff的逻辑，又能确保尽可能少操作DOM，这也是虚拟DOM驱动框架性能一直优秀的原因之一。

![](/Users/<USER>/Downloads/IDEAProjects/java-edge-master/assets/image-20241024094950847.png)

### Vue V.S React 框架

通过上面对前端三大框架介绍，Vue 和 React 在数据变化后，通知页面更新的方式不同：

- Vue 若数据变，框架主动告诉你修改哪些数据
- React数据变，只能通过新老数据计算Diff得知数据变化

俩方案都解决了数据变化后，咋通知页面更新的问题，并迅速获得很高占有率，但都有性能瓶颈：

- 对于 Vue，它的一个核心就是“响应式”，即数据变化后，主动通知我们。响应式数据新建Watcher监听，本身就比较损耗性能，项目大了后，每个数据都有一个watcher会影响性能
- 对于React的虚拟DOM的Diff计算逻辑，如虚拟DOM树过于庞大，使计算时间大于16.6ms，就可能造成性能卡顿

为解决这种性能瓶颈， Vue 和 React 走了不同的道路。

React为突破性能瓶颈，借鉴os时间分片概念，引入Fiber架构。把整个虚拟DOM树微观化，变成链表，然后利用浏览器的空闲时间计算Diff。一旦浏览器有需求，可将没计算完的任务放在一旁，把主进程控制权还给浏览器，等待浏览器下次空闲。

这种架构虽未减少运算量，但巧妙利用空闲实现计算，解决卡顿

![](/Users/<USER>/Downloads/IDEAProjects/java-edge-master/assets/image-20240706183037704.png)

左侧是树形结构，树形结构Diff很难中断；右侧把树形结构改造成链表，遍历严格按子元素->兄弟元素->父元素，随时可中断和恢复Diff的计算过程。

为方便理解计算Diff：

![](/Users/<USER>/Downloads/IDEAProjects/java-edge-master/assets/image-20241024095501815.png)

两个虚线之间是浏览器的一帧，高性能动画要求60fps，每帧就是16.6ms，在这16.6毫秒，浏览器自己的渲染更新任务执行后，会有一部分空闲时间，这段时间就计算Diff。

等下一帧任务来，就把控制权还给浏览器，让它继续去更新和渲染，等待空闲时间再继续计算，这就不会导致卡顿。

Vue 1问题在于响应式数据过多，导致内存占用过多。所以 Vue 2 大胆引入虚拟DOM解决响应式数据过多。该解决方案用虚拟DOM：

- 解决响应式数据过多的内存占用问题
- 良好规避React中虚拟DOM的问题
- 给 Vue 带来跨端能力

**响应式数据是主动推送变化，虚拟DOM是被动计算数据的Diff，一个推一个拉，看着是两个方向技术，但被 Vue 2 很好融合在一起，采用方式就是组件级划分。**

对于Vue 2，组件之间变化，可通过响应式来通知更新。组件内部的数据变化，则通过虚拟DOM去更新页面。这就把响应式的监听器，控制在组件级，而虚拟DOM的量级，也控制在了组件的大小。

该方案也体现 Vue 一直以来中庸设计思想。

左侧是一个个组件，组件内部没有Watcher监听器，而是通过虚拟DOM来更新，每个组件对应一个监听器，大大减小监听器的数量：

![](/Users/<USER>/Downloads/IDEAProjects/java-edge-master/assets/image-20241024100020407.png)

除了响应式和虚拟DOM维度，Vue和React还有一些理念和路线的不同，模板书写，也走出template、JSX两个路线：

![](/Users/<USER>/Downloads/IDEAProjects/java-edge-master/assets/image-20241024100058423.png)

React只有JSX，最终JSX都在Compiler层，即工程化那里编译成JS执行，所以React最终拥有全部JS的动态性，这也导致React API一直很少，只有state、hooks、Component几个概念，主要都是JavaScript本身的语法和特性。

而 Vue 默认是template，即语法限死，如v-if 和v-for等。有了这些写法规矩，可在上线前做很多优化。Vue 3优秀点是在虚拟DOM的静态标记做到极致，让静态的部分越过虚拟DOM的计算，真正做到按需更新，很好提高性能。

![](/Users/<USER>/Downloads/IDEAProjects/java-edge-master/assets/image-20241024100740799.png)

在模板的书写上，除了 Vue 和 React 走出的template和JSX两个路线，还出现了 Svelte 这种框架，没有虚拟DOM的库，直接把模板编译成原生DOM，几乎没有Runtime，所有的逻辑都在Compiler层优化，算是另外一个极致。

![](/Users/<USER>/Downloads/IDEAProjects/java-edge-master/assets/image-20241024100757729.png)

## 总结

了解了前端MVVM框架发展的历史和方向后，相信你脑海里已经建立起了一个前端框架发展的地图，每个框架都在探索自己的路线。后面还会涌现出更多优秀的框架，我们到时候只需要把那个框架纳入到这个地图中去理解，这样你很快就明白这个框架做了什么，而这也是很多前端大神能够快速学习一个新框架的主要原因。

浏览器的诞生让我们可以方便地显示文本和图片的内容和样式；JavaScript的出现让网页动了起来；Gmail的发布，宣告前端也可以使用Ajax异步加载技术，来进行复杂网页的开发，前端工程师这个工种也正式出现了。

随着浏览器厂商的混战，各个浏览器都有自己的特色，jQuery框架的出现统一了写法，解决了那个时代最棘手的前端问题：兼容性，极大提高了开发者的效率。

随着Angular 1的诞生，我们多了一套开发模式，就是数据驱动页面。我们甚至不再需要使用jQuery去寻找DOM，而是只关注数据的来源和修改，这也就是现在我们所处的前端时代。我们所熟悉的Vue、React、Angular和Svelte等框架，都是在数据驱动页面这个场景下涌现的框架。

## 总结

明白前端的这些框架的风格和特点，以及 Vue 在这些框架中的地位，Vue 3 在Vue 2 的基础之上做了全面的升级，在性能、扩展性和包的大小上，Vue3都有质的飞跃。

## Vue需要Fiber吗？

Vue的更新机制在性能上已经非常高效，尤其在Vue 3通过编译器的优化进一步减少了不必要的虚拟DOM更新。不过，Vue并没有直接采用类似React Fiber的架构，因为Vue的设计哲学更倾向于在组件级别进行优化，而Fiber更适用于大规模、长耗时的异步更新。

Vue不需要像React那样处理复杂的任务调度问题，因此在当前的架构下，Vue并不急需引入Fiber。不过，随着前端应用场景越来越复杂，Vue团队可能会在未来进一步考虑类似的解决方案。
