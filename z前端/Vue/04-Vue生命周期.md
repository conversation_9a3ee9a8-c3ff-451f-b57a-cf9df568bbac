## 1 Vue实例

```html
<div id="root">
    <!-- v-on可简写为@ -->
    <div @click="handleClick">
        {{message}}
    </div>
    <item></item>
</div>

<script>

    Vue.component('item', {
        template: '<div>hello item</div>'
    })

    /**
     * 每个Vue应用都是通过用Vue函数创建一个新的Vue实例开始
     */
    var vm = new Vue({
        /**
         * 表示Vue实例将挂载到id为"root"的HTML元素
         * 该HTML元素可未任何元素，如div、section
         * Vue实例挂载到该元素后，Vue实例就可操作该元素及其子元素，以及响应用户的交互行为
         */
        el: '#root',
        /**
         * 当一个Vue实例被创建，它将data对象中的所有的 property 加入到 Vue 的响应式系统中
         * 当这些property的值变化，视图将产生“响应”，即匹配更新为新值
         * 仅当实例被创建时就已经存在于data中的property才是响应式的
         */
        data: {
            message: 'hello world'
        },
        // 在methods对象中定义方法
        methods: {
            handleClick: function () {
                alert("hello")
            }
        }
    })
</script>
```

一个 Vue 应用由一个通过 `new Vue` 创建的**根 Vue 实例**，以及可选的嵌套的、可复用的组件树组成。

所有的 Vue 组件都是 Vue 实例，并且接受相同的选项对象 (一些根实例特有的选项除外)。

组件是可复用的 Vue 实例，且带有一个名字：在这个例子中是 `<item>`。

```html
<div id="root">
    <!-- v-on可简写为@ -->
    <div @click="handleClick">
        {{message}}
    </div>
    <!-- 使用item组件 -->
    <item></item>
</div>
```

## 2 Vue2实例生命周期

```html
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>Vue实例生命周期函数</title>
	<script src='../vue.js'></script>
</head>
<body>
	<div id="app">Hello World</div>

	<script>
		// 生命周期函数：Vue实例在某时间点自动执行的函数
		var vm = new Vue({
			el: "#app",
			template: "<div>{{test}}</div>",
			data: {
				test: "hello world"
			},
			beforeCreate: function() {
				console.log("beforeCreate");
			},
			created: function() {
				console.log("created");
			},
			beforeMount: function() {
				console.log(this.$el);
				console.log("beforeMount");
			},
			mounted: function() {
				console.log(this.$el);
				console.log("mounted");
			},
			beforeDestroy: function() {
				console.log("beforeDestroy");
			},
			destroyed: function() {
				console.log("destroyed");
			},
			beforeUpdate: function() {
				console.log("beforeUpdate");
			},
			updated: function() {
				console.log("updated");
			}
		})
	</script>
</body>
</html>
```

这些方法都是单独定义，不放在 methods 对象里。

![Vue 实例生命周期](https://v2.cn.vuejs.org/images/lifecycle.png)



![](https://img-blog.csdnimg.cn/c90d5582437e46c09b33249f1838d65c.png)

见鬼了，其它几个生命周期点呢？为啥没打印出来呢？

```html
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>Vue实例生命周期函数</title>
	<script src='../vue.js'></script>
</head>
<body>
	<div id="app">Hello World</div>

	<script>
		// 生命周期函数：Vue实例在某时间点自动执行的函数
		var vm = new Vue({
			el: "#app",
			template: "<div>{{test}}</div>",
			data: {
				test: "hello world"
			},
      // 第一个被调用
			beforeCreate: function() {
				console.log("beforeCreate");
			},
```

```html
			created: function() {
				console.log("created");
			},
```

### template



![](https://img-blog.csdnimg.cn/93c099dbe51f4e7cb2b4ef466f64c5c5.png)

```html
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>Vue实例生命周期函数</title>
	<script src='../vue.js'></script>
</head>
<body>
	<script>
		// 生命周期函数：Vue实例在某时间点自动执行的函数
		var vm = new Vue({
			el: "#app",
			template: "<div>{{test}}</div>",
			data: {
				test: "hello world"
			},
			beforeCreate: function() {
				console.log("beforeCreate");
			},
```

上面这么写和下面一样：

```html
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>Vue实例生命周期函数</title>
	<script src='../vue.js'></script>
</head>
<body>
	<script>
		// 生命周期函数：Vue实例在某时间点自动执行的函数
		var vm = new Vue({
			el: "#app",
			data: {
				test: "hello world"
			},
			beforeCreate: function() {
				console.log("beforeCreate");
			},
```

### vm.$destroy()

完全销毁一个实例。清理它与其它实例的连接，解绑它的全部指令及事件监听器。

触发 `beforeDestroy` 和 `destroyed` 的钩子。

在大多数场景中你不应该调用这个方法。最好用 `v-if` 和 `v-for` ，以数据驱动方式控制子组件的生命周期。

![](https://img-blog.csdnimg.cn/b0efa545c29c48b6b4936da31b1e03d0.png)

### update



![](https://img-blog.csdnimg.cn/6c3316647fbb4bad8982455b8ed8eac8.png)

## 3 Vue3实例生命周期

```properties
mapping vue2 to vue3

beforeCreate  ->   use setup( )
created -> 					use setup()
beforeMount -> onBeforeMount
mounted -> onMdunted
beforeUpdate -> onBeforeUpdate
updated-> onUpdated
beforeDestroy -> onBeforeUnmount
destroyed onUnmounted
activated onActivated
deactivated -> onDeactivated
errorCaptured onErrorCaptured

// add
edonRenderTracked
onRenderTriggered
```

