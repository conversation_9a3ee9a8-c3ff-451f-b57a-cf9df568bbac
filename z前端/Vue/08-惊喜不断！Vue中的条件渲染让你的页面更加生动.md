## 0 前言

**条件渲染**，根据某些条件来决定是否渲染（显示）DOM元素的技术。在Vue中，这类似于编程语言中的if-else语句，根据变量的真假值来控制页面元素的显示与隐藏。

### Vue的条件渲染指令

Vue提供三个主要的条件渲染指令：

1. **v-if** - 条件性地渲染元素
2. **v-else** - 与v-if配合使用
3. **v-else-if** - 与v-if配合使用
4. **v-show** - 切换元素的显示状态

## 1 v-if V.S v-show

```html
<div id="app">
    <div v-if="show" data-test="v-if">{{message}}</div>
    <div v-show="show" data-test="v-show">{{message}}</div>
</div>

<script>
    var vm = new Vue({
        el: "#app",
        data: {
            show: false,
            message: "Java Edge"
        }
    })
</script>
```

控制台更新：

![](https://img-blog.csdnimg.cn/f7f2a39afe5c42efabb09b50f5880120.png)

![](https://img-blog.csdnimg.cn/840d1a8f30d3466992988823982ca615.png)

初始用的 v-show

![](https://img-blog.csdnimg.cn/4d7eaa930c184a769fa8921c09427d46.png)

v-if、v-show都能控制一个模板标签是否展示：

- v-if 对应变量名为 false 时，就不存在该DOM
- v-show对应变量名 false 时，该 DOM 还存在页面，只是display:none

当频繁指定DOM元素是否显示，v-show性能更好，因其不会频繁删除增加DOM。

## 2 v-if、v-else

```html
<div id="app">
    <div v-if="show">
        用户名：<input key="username"/>
    </div>
    <div v-else>
        邮箱名：<input key="password"/>
    </div>
</div>

<script>
    var vm = new Vue({
        el: "#app",
        data: {
            show: false,
        }
    })
</script>
```

根据show值展示不同输入框。

在div里用v-if、v-else指令实现条件渲染:

- show=true，展示用户名输入框

- show=false，展示邮箱输入框

 1. 在script标签中，使用Vue构造函数创建了一个Vue实例，并将其挂载到id为"app"的div上。
 2. 在data中定义了一个名为show的属性，并将其初始值设为false。
    总之，这段代码展示了Vue中的条件渲染的使用方法。

注意，必须连在一起使用：

```html
<div id="app">
    <div v-if="show">
        用户名：<input key="username"/>
    </div>
    <span></span>
    <div v-else>
        邮箱名：<input key="password"/>
    </div>
</div>
```

中间加其他标签报错：

![](https://img-blog.csdnimg.cn/00ffd72c91c34642bd7299a90871e5a3.png)

## 3 v-else-if

```html
<div id="app">
    <div v-if="show === 'a'">This is Java</div>
    <div v-else-if="show === 'b'">This is Edge</div>
    <div v-else>关注公众号</div>
</div>

<script>
    var vm = new Vue({
        el: "#app",
        data: {
            show: 'a'
        }
    })
</script>
```

![](https://img-blog.csdnimg.cn/38b17ee1150340399c9a195c86997481.png)

![](https://img-blog.csdnimg.cn/fe16c53f68bf4940b1c1e704ea19d1c2.png)

## 4 key

```html
<div id="app">
    <div v-if="show">
        用户名：<input/>
    </div>
    <div v-else>
        邮箱名：<input/>
    </div>
</div>

<script>
    var vm = new Vue({
        el: "#app",
        data: {
            show: false,
        }
    })
</script>
```

更该show值，即可切换显示的标签：

![](https://img-blog.csdnimg.cn/0cd339ac80084d9586f292b7d45879cf.png)

一开始是用户名时，我输入了名字，更新 show 后，Vue 会复用input 标签的内容，导致如下现象：

![](https://img-blog.csdnimg.cn/8820b15472124dc682d7dcc892a16432.png)

咋实现切换标签后，把内容清空，方便重新输入？

```html
<div id="app">
    <div v-if="show">
        用户名：<input key="username"/>
    </div>
    <div v-else>
        邮箱名：<input key="email"/>
    </div>
</div>
```

给 input 标签加个唯一名称，Vue就不会再复用之前标签内容。
