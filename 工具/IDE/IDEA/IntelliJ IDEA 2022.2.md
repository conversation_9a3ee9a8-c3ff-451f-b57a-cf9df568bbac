IntelliJ IDEA 2022.2 为远程开发功能带来了多项质量改进，使其更美观、更稳定。 从 v2022.2 开始，IntelliJ IDEA 使用 JetBrains Runtime 17，后者可以从多方面增强 IDE 体验和性能。 IntelliJ IDEA Ultimate 添加了对 Spring 6 和 Spring Boot 3 功能的支持，也为多个其他框架引入了更新。 新版本还具有多项值得注意的升级和改进，下文将详细介绍。

[下载](https://www.jetbrains.com/zh-cn/idea/download/)

## 主要更新

![远程开发](https://www.jetbrains.com/idea/whatsnew/2022-2/img/RD.png)

### 远程开发改进 测试版 Ultimate

我们在 IntelliJ IDEA 2022.2 中为远程开发引入了大量重大升级，让体验更稳定、功能更丰富。 新发布的更新具有多项质量改进。 通过 SSH 将笔记本电脑连接到安装在远程服务器中的 IntelliJ IDEA，获得流畅的开发者体验。 如果您使用 [JetBrains Space](https://www.jetbrains.com/zh-cn/remote-development/space-dev-environments/) 在新版本中高效地编排后端，可以直接从 IntelliJ IDEA 管理开发环境。 在这篇[博文](https://blog.jetbrains.com/idea/2022/06/intellij-idea-2022-2-eap-7/)中了解详情。

![从 JBR 11 转换到 JBR 17](https://www.jetbrains.com/idea/whatsnew/2022-2/img/JBR17.png)

### 从 JBR 11 转换到 JBR 17

As of v2022.2, all IntelliJ IDEA updates come with JetBrains Runtime 17 (JBR 17), which brings a significant IDE performance improvement, better security, enhanced rendering performance on macOS thanks to the [Metal API](https://developer.apple.com/metal/), and more. 阅读此[博文](https://blog.jetbrains.com/idea/2022/05/intellij-idea-2022-2-eap-1/#JetBrains_Runtime)了解更多详细信息。

### 对 Spring 6 和 Spring Boot 3 功能支持

现完全支持 Spring 6 和 Spring Boot 3 功能，包括新的 `@AutoConfiguration` 类和 `@ConfigurationProperties` 类，涵盖新的构造函数绑定语法，无需显式 `@ConstructorBinding`。

![](https://www.jetbrains.com/idea/whatsnew/2022-2/img/AutoConfig_1.png)

![](https://www.jetbrains.com/idea/whatsnew/2022-2/img/ConstructorBinding_2.png)

## 用户体验

![运行当前文件](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Run_Current_File.png)

### *运行当前文件*

*Run/Debug*（运行/调试）微件新增了 *Run Current File*（运行当前文件）功能，可供在没有专门的运行配置的情况下轻松运行和调试单个文件。 通过它运行和调试当前打开的文件时，IDE 将自动使用最适合该文件的运行配置类型。

- ![全局更改字体大小的键盘快捷键](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Change_font_size_1.png)
- ![全局更改字体大小的键盘快捷键](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Zooming_indicator_2.png)
- ![全局更改字体大小的键盘快捷键](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Change_font_size_1.png)
- ![全局更改字体大小的键盘快捷键](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Zooming_indicator_2.png)
- ![全局更改字体大小的键盘快捷键](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Change_font_size_1.png)
- ![全局更改字体大小的键盘快捷键](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Zooming_indicator_2.png)

### 全局更改字体大小的键盘快捷键

新增的键盘快捷键可以更改编辑器中所有位置的字体大小。 要增大字体，请按 ⌃⇧Period。 要减小字体，请按 ⌃⇧Comma。 此外还有一个字体大小指示器，它会显示当前字体大小并提供将其恢复为默认值的选项。

![macOS 上的 Merge All Project Windows（合并所有项目窗口）操作](https://www.jetbrains.com/idea/whatsnew/2022-2/img/macOS_tabs_merge.png)

### macOS 上的 *Merge All Project Windows*（合并所有项目窗口）操作

我们为 macOS 用户引入了一项功能，利用此功能可以将所有打开的项目窗口合并成一个，将其变成标签页。 转到 *Window* | *Merge All Project Windows*（窗口 | 合并所有项目窗口）即可执行此操作。

- ![高亮显示检查的增强配置](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Highlighting_for_inspections_1.png)
- ![高亮显示检查的增强配置](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Styles_list_2.png)
- ![高亮显示检查的增强配置](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Highlighting_for_inspections_1.png)
- ![高亮显示检查的增强配置](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Styles_list_2.png)
- ![高亮显示检查的增强配置](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Highlighting_for_inspections_1.png)
- ![高亮显示检查的增强配置](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Styles_list_2.png)

### 高亮显示检查的增强配置

现在，无需更改严重性级别即可配置检查在编辑器中的显示方式。 如需更改检查高亮显示样式，可以使用新的 *Highlighting in editor*（编辑器中的高亮显示）下拉菜单进行设置，该菜单清楚显示了所有可用选项。

![欢迎屏幕上的 Cloning repository（正在克隆仓库）进度条](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Cloning_repository_progress_bar.png)

### 欢迎屏幕上的 *Cloning repository*（正在克隆仓库）进度条

*Cloning repository*（正在克隆仓库）进度条现在位于 IDE 的欢迎屏幕上，并直接显示在 Projects（项目）列表中，更清晰、更易用。

![助记书签的新 Description（描述）字段](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Mnemonic_bookmarks.png)

### 助记书签的新 *Description*（描述）字段

*Add Mnemonic Bookmark*（添加助记书签）对话框现已升级，增加了 *Description*（描述）字段，您现在可以使用该字段直接向书签添加描述。

## 编辑器

![禁用自动块注释结束的新设置](https://www.jetbrains.com/idea/whatsnew/2022-2/img/block_comment_closure.png)

### 禁用自动块注释结束的新设置

现在，可以在按 Enter 键后禁用自动块注释结束。 为此，请转到 *Settings / Preferences | Editor | Smart Keys*（设置/偏好设置 | 编辑器 | 智能按键），取消选中 *Enter* 版块中的 *Close block comment*（结束块注释）复选框。

![更快访问 Code Completion Settings（代码补全设置）](https://www.jetbrains.com/idea/whatsnew/2022-2/img/code_completion_settings.png)

### 更快访问 *Code Completion Settings*（代码补全设置）

现在，您可以直接从代码补全弹出窗口中的垂直省略号菜单按钮访问 *Code Completion Settings*（代码补全设置）并配置偏好设置。

![调整文件类型关联的新通知面板](https://www.jetbrains.com/idea/whatsnew/2022-2/img/File_type_warning.png)

### 调整文件类型关联的新通知面板

当文件错误地与纯文本显式关联时，IntelliJ IDEA 现在会在通知中说明错误的文件类型关联并建议直接从编辑器中将其重置，无需在 *Settings / Preferences*（设置/偏好设置）中手动操作。

![在 Markdown 文件中生成目录的新操作](https://www.jetbrains.com/idea/whatsnew/2022-2/img/ToC_in_md.png)

### 在 Markdown 文件中生成目录的新操作

现在，在 Markdown 文件中可以根据文档标题轻松生成目录。 新操作位于 *Insert*（插入）和 *Generate*（生成）弹出菜单中，可以通过 ⌘N 快捷键或右键点击调用。 IDE 将在当前文本光标处插入目录并以 `<!-- TOC -->` 标记将其括起，后续调用相同的菜单可以对其进行更新。

## Java

- ![改进的检查和代码补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/DiamondFix.png)
- ![改进的检查和代码补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/unboxing.png)
- ![改进的检查和代码补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Standard_Charset.png)
- ![改进的检查和代码补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/hide_fields.png)
- ![改进的检查和代码补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/requireNonNullElse.png)
- ![改进的检查和代码补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Java_Code_Completion.png)
- ![改进的检查和代码补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/DeepDataFlow.png)
- ![改进的检查和代码补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/remove_throws.png)
- ![改进的检查和代码补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/DiamondFix.png)
- ![改进的检查和代码补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/unboxing.png)
- ![改进的检查和代码补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Standard_Charset.png)
- ![改进的检查和代码补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/hide_fields.png)

### 改进的检查和代码补全

我们对 Java 检查实现了一系列更改，这些更改有助于跟踪潜在错误和简化代码。 例如， *Standard ‘Charset’ object can be used*（可以使用标准 'Charset' 对象）检查已得到改进，现在可以识别 `.name()` 和 `.toString()`。 IDE 现在可以在模式变量隐藏字段时发出警告，还会捕获无意义的 `Objects.requireNonNullElse` 调用。 大量 JUnit 相关 Java 检查已转换为 JVM 检查，因此，它们现在也可以在 Kotlin 中使用。 此外，代码补全现在会在适用时建议 *.class* 字面量。

要详细了解其他 Java 相关改进，请阅读我们的[博文](https://blog.jetbrains.com/idea/2022/06/intellij-idea-2022-2-eap-4/)。

## Groovy

![对 GINQ 的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Groovy_4.png)

### 对 GINQ 的支持

We’ve added support for [Groovy-Integrated Queries (GINQ)](https://groovy-lang.org/using-ginq.html#_ginq_a_k_a_groovy_integrated_query). IDE 现在为 Groovy 4 的此功能提供了语法高亮显示、代码补全和检查。

## 安全性

### 导入受信任的 SSL 证书

IntelliJ IDEA 2022.2 现在可以帮助您从系统受信任存储区导入受信任的 SSL 证书。 它将自动使用特定于企业环境的自定义证书。 一切都开箱即用，无需额外操作。

## 性能分析器

![剖析性能时可用的 CPU 和“堆内存”图表](https://www.jetbrains.com/idea/whatsnew/2022-2/img/CPU_and_Heap_Memory_charts.png)

### 剖析性能时可用的 *CPU* 和*堆内存*图表 Ultimate

我们升级了 IntelliJ Profiler，让快照更易收集。 现在，开始分析时，*CPU* 和*堆内存*实时图表将与控制台并排显示。 您可以查看应用程序的执行情况，还可以在所需分析期间从同一应用程序收集许多快照。

![对 Kotlin 内联函数的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Kotlin_inline_functions.png)

### 对 Kotlin 内联函数的支持 Ultimate

Kotlin 内联函数现在与其他函数一起显示在 *Flame Graph*（火焰图）、*Call Tree*（调用树）和 *Method List*（方法列表）中，这样，您可以查看 CPU 时间并根据需要进行优化。 该功能目前在 Linux 和 macOS 上运行，仅适用于 *Start profiling*（启动分析）选项。 我们正在努力添加对 Windows 的支持，也将很快把它添加到 *Attach* 模式。

## 框架和技术

- ![对 Spring 6 中新声明式 HTTP 客户端的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/client-endpoints_2.png)
- ![对 Spring 6 中新声明式 HTTP 客户端的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/run-http_3.png)
- ![对 Spring 6 中新声明式 HTTP 客户端的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/client-completion_1.png)
- ![对 Spring 6 中新声明式 HTTP 客户端的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/client-endpoints_2.png)
- ![对 Spring 6 中新声明式 HTTP 客户端的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/run-http_3.png)
- ![对 Spring 6 中新声明式 HTTP 客户端的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/client-completion_1.png)
- ![对 Spring 6 中新声明式 HTTP 客户端的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/client-endpoints_2.png)

### 对 Spring 6 中新声明式 HTTP 客户端的支持

可将 HTTP 客户端定义为带有注解方法的 Java 接口。 IntelliJ IDEA 2022.2 为此类接口提供了 URL 补全、导航以及与 HTTP 客户端的集成。

Spring Cloud Gateway 路由的 URL 补全和导航：

![](https://www.jetbrains.com/idea/whatsnew/2022-2/img/endpoints_1.png)

![](https://www.jetbrains.com/idea/whatsnew/2022-2/img/spring-cloud-ulrs_2.png)

![](https://www.jetbrains.com/idea/whatsnew/2022-2/img/endpoints_1.png)

![](https://www.jetbrains.com/idea/whatsnew/2022-2/img/spring-cloud-ulrs_2.png)

![](https://www.jetbrains.com/idea/whatsnew/2022-2/img/endpoints_1.png)

![](https://www.jetbrains.com/idea/whatsnew/2022-2/img/spring-cloud-ulrs_2.png)

### Spring Cloud Gateway 路由的 URL 补全和导航

为 Spring Cloud Gateway URL 提供了 URL 补全、*Search Everywhere*（随处搜索）功能和 *Find Usages*（查找用法）提示。 使用 *Endpoints*（端点）工具窗口可以轻松生成 HTTP 请求或查找所有网关路由。

- ![Spring Cloud Gateway 路由的 URL 补全和导航](https://www.jetbrains.com/idea/whatsnew/2022-2/img/spring-testing_2.png)
- ![Spring Cloud Gateway 路由的 URL 补全和导航](https://www.jetbrains.com/idea/whatsnew/2022-2/img/wiremock_3.png)
- ![Spring Cloud Gateway 路由的 URL 补全和导航](https://www.jetbrains.com/idea/whatsnew/2022-2/img/restassured_1.png)
- ![Spring Cloud Gateway 路由的 URL 补全和导航](https://www.jetbrains.com/idea/whatsnew/2022-2/img/spring-testing_2.png)
- ![Spring Cloud Gateway 路由的 URL 补全和导航](https://www.jetbrains.com/idea/whatsnew/2022-2/img/wiremock_3.png)
- ![Spring Cloud Gateway 路由的 URL 补全和导航](https://www.jetbrains.com/idea/whatsnew/2022-2/img/restassured_1.png)
- ![Spring Cloud Gateway 路由的 URL 补全和导航](https://www.jetbrains.com/idea/whatsnew/2022-2/img/spring-testing_2.png)

### JVM 微服务测试和模拟框架的代码洞察改进 Ultimate

IntelliJ IDEA 2022.2 使用 Spring WebTestClient、RestAssured、WireMock 和 MockServer 等流行 Java API 自动高亮显示测试中的 JSON/XML 体。 因此，这些数据片段具有更好的可读性并且更易编辑。 您还可以在测试中的这些位置使用 URL 补全和导航到声明。

- ![Java 的实验性 GraalVM 原生调试器](https://www.jetbrains.com/idea/whatsnew/2022-2/img/graalvm-vars_2.png)
- ![Java 的实验性 GraalVM 原生调试器](https://www.jetbrains.com/idea/whatsnew/2022-2/img/graalvm-debug_3.png)
- ![Java 的实验性 GraalVM 原生调试器](https://www.jetbrains.com/idea/whatsnew/2022-2/img/graalvm-steps_1.png)
- ![Java 的实验性 GraalVM 原生调试器](https://www.jetbrains.com/idea/whatsnew/2022-2/img/graalvm-vars_2.png)
- ![Java 的实验性 GraalVM 原生调试器](https://www.jetbrains.com/idea/whatsnew/2022-2/img/graalvm-debug_3.png)
- ![Java 的实验性 GraalVM 原生调试器](https://www.jetbrains.com/idea/whatsnew/2022-2/img/graalvm-steps_1.png)
- ![Java 的实验性 GraalVM 原生调试器](https://www.jetbrains.com/idea/whatsnew/2022-2/img/graalvm-vars_2.png)

### Java 的实验性 GraalVM 原生调试器 Ultimate

IntelliJ IDEA 2022.2 现在可以协助调试 GraalVM 原生映像二进制文件。 您可以将调试器附加到任何基于 GraalVM 的可执行文件，也可以使用附加的调试器启动应用程序。 将自动为 Maven/Gradle 项目创建相应的运行配置。 此集成仍处于实验性阶段，需要安装 GraalVM 的开发版本和[此插件](https://plugins.jetbrains.com/plugin/19237-graalvm-native-debugger)。

### Protobuf 与 Java 源代码之间改进导航

![](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Protobuf_and_Java_sources.png)

可在.proto文件和生成的代码之间轻松导航。 确保 gRPC 和 Protocol Buffers 插件均已启用。

![](https://p.ipic.vip/i03p53.png)

![](https://p.ipic.vip/p3twu4.png)

### Spring Shell 的代码洞察改进

![](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Spring_Shell.png)

IntelliJ IDEA 2022.2 现在可以识别使用 Spring Shell 库声明的 CLI 命令并检查其是否正确。

- ![对 JAX-RS 端点的改进支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/jaxrs-apppath_1.png)
- ![对 JAX-RS 端点的改进支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/subresource-http_2.png)
- ![对 JAX-RS 端点的改进支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/jaxrs-apppath_1.png)
- ![对 JAX-RS 端点的改进支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/subresource-http_2.png)
- ![对 JAX-RS 端点的改进支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/jaxrs-apppath_1.png)
- ![对 JAX-RS 端点的改进支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/subresource-http_2.png)

### 对 JAX-RS 端点的改进支持 Ultimate

IntelliJ IDEA 现在正确支持声明为 `@ApplicationPath` 的 JAX-RS 通用 Web 服务 URL 模式以及 URL 补全、导航和 *Find Usages*（查找用法）中的子资源。

- ![对 HTTP 客户端中 WebSocket 端点的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/WebSockets_1.png)
- ![对 HTTP 客户端中 WebSocket 端点的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/WebSockets_2.png)
- ![对 HTTP 客户端中 WebSocket 端点的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/WebSockets_1.png)
- ![对 HTTP 客户端中 WebSocket 端点的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/WebSockets_2.png)
- ![对 HTTP 客户端中 WebSocket 端点的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/WebSockets_1.png)
- ![对 HTTP 客户端中 WebSocket 端点的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/WebSockets_2.png)

### 对 HTTP 客户端中 WebSocket 端点的支持 Ultimate

IntelliJ IDEA Ultimate 2022.2 支持 WebSocket 连接，允许创建请求以及发送和接收消息。

![对 HTTP 客户端中 GraphQL 端点的支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/GraphQL.png)

### 对 HTTP 客户端中 GraphQL 端点的支持 Ultimate

IntelliJ IDEA Ultimate 现在可以原生通过 HTTP 和 WebSocket 协议发送 GraphQL 查询。 对于 *http://* 和 *https://*，使用的是简单的 HTTP 请求，*ws://* 和 *wss://* 则被委托给 WebSocket 执行器。

- ![针对 HTTP 客户端的 UI/UX 改进](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Config_before_run_1.png)
- ![针对 HTTP 客户端的 UI/UX 改进](https://www.jetbrains.com/idea/whatsnew/2022-2/img/http-Progress_bar_2.png)
- ![针对 HTTP 客户端的 UI/UX 改进](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Config_before_run_1.png)
- ![针对 HTTP 客户端的 UI/UX 改进](https://www.jetbrains.com/idea/whatsnew/2022-2/img/http-Progress_bar_2.png)
- ![针对 HTTP 客户端的 UI/UX 改进](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Config_before_run_1.png)
- ![针对 HTTP 客户端的 UI/UX 改进](https://www.jetbrains.com/idea/whatsnew/2022-2/img/http-Progress_bar_2.png)

### 针对 HTTP 客户端的 UI/UX 改进 Ultimate

我们在 HTTP 客户端上实现了一些 UI/UX 改进。 首先，我们引入了一种使用装订区域图标选择运行环境的便捷方式。 要启用此功能，请从 *Run with*（运行方式）组合框中选择 *Select Environment Before Run*（运行前选择环境）选项。 另外，新增的进度条改进了*响应*视图，您可以跟踪下载过程。

![JSON、YAML 和 .properties 字符串值中的可点击 URL](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Clickable_URLs.png)

### JSON、YAML 和 .properties 字符串值中的可点击 URL Ultimate

JSON、YAML 和 .properties 文件现在会在以 *http://* 和 *https://* 开头的值中自动插入 Web 引用。 只需点击一下，即可轻松地在 Web 浏览器中打开这些链接，或者您可以在 HTTP 客户端中通过 *Context Actions*（上下文操作）菜单 (⌥⏎) 生成一个请求。

![对 Bean Validation 注解的改进支持](https://www.jetbrains.com/idea/whatsnew/2022-2/img/beanvalidation.png)

### 对 Bean Validation 注解的改进支持 Ultimate

IntelliJ IDEA 2022.2 现在为 Java 和 Kotlin 的 Bean Validation 注解中的消息特性提供引用并支持折叠。 您可以为消息键使用补全或导航到 i18n *ValidationMessages.properties* 文件中的声明。

![改进的 AWS CloudFormation 插件](https://www.jetbrains.com/idea/whatsnew/2022-2/img/aws-types.png)

### 改进的 AWS CloudFormation 插件 Ultimate

我们通过更新元数据架构和改进属性补全重做了 AWS CloudFormation 插件。 您可以从 JetBrains Marketplace 安装[插件的更新版本](https://plugins.jetbrains.com/plugin/7371-aws-cloudformation)。

### 对 Jakarta Persistence 3.1 的支持 Ultimate

IntelliJ IDEA now supports all of the features in [Jakarta Persistence 3.1](https://projects.eclipse.org/projects/ee4j.jpa/releases/3.1), including the new JPQL syntax, functions, and types, and it provides language highlighting and code completion for them.

## Docker

![Testcontainers 的测试中 Docker 镜像补全](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Testcontainers.png)

### Testcontainers 的测试中 Docker 镜像补全

IntelliJ IDEA 2022.2 introduces image completion for the [Testcontainers](https://www.testcontainers.org/) API where you can find all available Docker images and their versions. 点击引用即可在 Web 浏览器中打开相应的 Docker Hub URL。

![上传本地 Docker 镜像到 Minikube 和其他连接](https://www.jetbrains.com/idea/whatsnew/2022-2/img/docker-copy.png)

### 上传本地 Docker 镜像到 Minikube 和其他连接

现在，您可以使用新的 *Copy Docker Image*（复制 Docker 镜像）操作将镜像从一个 Docker 守护进程复制到另一个，该操作会将镜像保存到文件中，然后将其推送到所选连接。

![IDE 重启时 Docker 自动连接](https://www.jetbrains.com/idea/whatsnew/2022-2/img/docker-copy.png)

### IDE 重启时 Docker 自动连接

重新启动 IDE 后，IntelliJ IDEA 2022.2 现在会自动连接到 Docker。 此新设置默认启用，也可以在 *Settings / Preferences | Advanced Settings | Docker*（设置/偏好设置 | 高级设置 | Docker）中进行切换。

### 不同 Docker 守护进程的 Docker 连接选项

从 v2022.2 开始，IntelliJ IDEA 与 Colima 和 Rancher 集成，支持更多用于建立与 Docker 守护进程的连接的选项。

## Kubernetes

![Kubernetes 和 Telepresence 集成](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Telepresence.png)

### Kubernetes 和 Telepresence 集成 Ultimate

The Kubernetes plugin for IntelliJ IDEA 2022.2 provides integration with the [Telepresence](https://www.telepresence.io/) tool in order to intercept HTTP requests from services in the Kubernetes cluster and route them to your service running locally. 您可以由此更轻松地调试这些服务，并仅在本地运行所需服务子集。

## QA 工具

![Page Object Editor 中的改进网页结构](https://www.jetbrains.com/idea/whatsnew/2022-2/img/QAT513_new_structure.png)

### Page Object Editor 中的改进网页结构

网页结构 UI 得到了显著改进。 得益于文本格式高亮显示，您可以轻松阅读最重要的部分，例如标记名称、ID 和应用的 CSS 类。

![通过装订区域图标轻松导航](https://www.jetbrains.com/idea/whatsnew/2022-2/img/QAT-486_new.png)

### 通过装订区域图标轻松导航

现在，只需点击装订区域中的图标即可轻松导航回页面元素。

![来自 URL 的页面对象命名](https://www.jetbrains.com/idea/whatsnew/2022-2/img/QAT-512.png)

### 来自 URL 的页面对象命名

创建新的页面对象文件时，向导现在将提供一个可选的 URL 字段。 如果包含 Web 地址，向导会根据链接地址建议页面对象文件名。 此外，当 Page Object Editor 打开时，会自动加载插入的 URL。

![文本光标自动移动到代码块末尾](https://www.jetbrains.com/idea/whatsnew/2022-2/img/QAT-507.png)

### 文本光标自动移动到代码块末尾

在代码中添加新的页面元素时，文本光标现在将自动移动到添加的代码块的末尾，这样，您可以轻松导航并继续编辑。

![从上下文菜单创建新的页面对象](https://www.jetbrains.com/idea/whatsnew/2022-2/img/222_QAT499_suggestion_POE.png)

### 从上下文菜单创建新的页面对象

在处理现有页面对象类时如果输入了新的非引用页面对象类，只需导航到警告的上下文菜单并创建新的页面对象即可修正未解决的代码警告。

## 数据库工具

![导入多个 CSV 文件的选项](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Copy_of_Multi_import_csv.png)

### 导入多个 CSV 文件的选项 Ultimate

对于 v2022.2，我们实现了选择多个 CSV 文件并将其一次导入的功能，以此增强文件导入过程。

![Modify（修改）对话框的新 UI](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Copy_of_Modify_UI.png)

### *Modify*（修改）对话框的新 UI Ultimate

*Modify*（修改）对话框的新 UI 已成为默认选项。 从 2022.2 版开始，可以使用新 UI 添加和编辑表的所有子对象。 上下文菜单将继续提供旧 UI。

[了解详情 ](https://blog.jetbrains.com/datagrip/2022/06/02/datagrip-2022-2-eap-1/#New_Modify_UI)

![Playground（演练场）和 Script（脚本）解析模式](https://www.jetbrains.com/idea/whatsnew/2022-2/img/Copy_of_Playground_and_Script.png)

### *Playground*（演练场）和 *Script*（脚本）解析模式 Ultimate

为了使 SQL 脚本中数据库对象的解析更加精确，我们实现了两种解析模式：*Playground*（演练场）和 *Script*（脚本）。 如果文件只是一组未连接的查询，彼此独立且没有特定顺序，前者效果更佳。 后者适合查询具有顺序逻辑且应该作为单个脚本运行的情况。

## 其他

- 与 IntelliJ IDEA 捆绑的 Android 插件现在提供了 Android Studio Chipmunk 的所有功能，包括对 Android Gradle 插件（AGP）7.2.x 的支持。
