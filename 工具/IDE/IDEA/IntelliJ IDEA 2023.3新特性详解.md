AI Assistant 持续演进，现已超越预览阶段，获得大量令人期待改进。 包括最新 Java 21 功能全面支持，引入带有编辑操作的直观浮动工具栏，并添加 *Run to Cursor*嵌入选项来增强调试工作流。 现提供无缝开箱即用 Kubernetes 开发体验。

## 关键亮点

### AI Assistant 预览阶段结束

![](https://www.jetbrains.com/idea/whatsnew/2023-3/img/AI_Assistant.png)

JetBrains AI Assistant 现已全面推出，搭载大量新功能和改进，助力提高您在 JetBrains IDE 中的工作效率。

最新更新包括编辑器中增强的直接代码生成、无需复制代码即可回答项目相关查询的上下文感知 AI 聊天，以及使用扩展上下文提供更全面结果的项目感知 AI 操作。 新的差异查看器有助于更轻松地识别 AI 操作对代码所做的更改。

订阅 [JetBrains AI Service](https://www.jetbrains.com/zh-cn/ai/)，在 IntelliJ IDEA Ultimate 中以补充功能的形式使用 AI Assistant。

![对 Java 21 功能的完全支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Java_21_3.png)

![对 Java 21 功能的完全支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Java_21_1.png)

![对 Java 21 功能的完全支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Java_21_2_preview.png)

![对 Java 21 功能的完全支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Java_21_3.png)

![对 Java 21 功能的完全支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Java_21_1.png)

### 对 Java 21 功能的完全支持

IntelliJ IDEA 2023.3 提供了对最新 Java 21 功能的完全支持。 这些更新包括虚拟线程、记录模式、`switch` 表达式的模式匹配和序列化集合等重要新特性，以及对字符串模板、作用域值等新引入的语言功能的预览。

![调试器中的 Run to Cursor（运行到光标）嵌入选项](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Run_to_cursor_preview.png)

### 调试器中的 *Run to Cursor*（运行到光标）嵌入选项

IntelliJ IDEA 2023.3 引入了一个新的 *Run to Cursor*（运行到光标）嵌入选项，该选项允许在调试时执行到特定代码行。 程序挂起后，您可以将鼠标悬停在要执行到的代码行上，然后点击 *Run to Cursor*（运行到光标）弹出窗口。 您还可以在将文本光标置于所需行上后调用键盘快捷键 (⌥F9) 来使用此功能。

![带有编辑操作的浮动工具栏](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Floating_toolbar_preview.png)

### 带有编辑操作的浮动工具栏

IntelliJ IDEA 2023.3 引入了一个浮动工具栏，该工具栏会在所选代码段旁边显示，提供对 *Extract*（提取）、*Surround*（包围）、*Reformat*（重新格式化）和 *Comment*（注释）等编辑操作以及上下文操作的轻松访问。 您可以使用竖三点菜单自定义浮动工具栏。 要将其隐藏，您可以使用相同的菜单或转到 *Settings/Preferences | Advanced Settings | Editor*（设置/偏好设置 | 高级设置 | 编辑器），选中 *Hide floating toolbar for code editing*（隐藏代码编辑的浮动工具栏）选项。

![开箱即用的 Kubernetes 开发体验](https://www.jetbrains.com/idea/whatsnew/2023-3/img/k8s_preview.png)

### 开箱即用的 Kubernetes 开发体验 Ultimate

IntelliJ IDEA Ultimate 2023.3 捆绑了 Kubernetes 插件，因此，用于开发基于 Kubernetes 的应用程序的所有插件功能均开箱即用。 这项集成简化了直接从 IDE 部署、调试和管理 Kubernetes 集群的工作流。

## 用户体验

![在默认查看模式下隐藏主工具栏的选项](https://www.jetbrains.com/idea/whatsnew/2023-3/img/hide_main_toolbar_preview.png)

### 在默认查看模式下隐藏主工具栏的选项

为了响应大家对新 UI 的反馈，我们实现了在使用 IDE 的默认查看模式时隐藏主工具栏的选项，与旧 UI 相同。 要移除工具栏，首先转到 *View | Appearance*（视图 | 外观），然后取消选中 *Toolbar*（工具栏）选项。

![Default（默认）工具窗口布局选项](https://www.jetbrains.com/idea/whatsnew/2023-3/img/default_layout.png)

### *Default*（默认）工具窗口布局选项

IntelliJ IDEA 2023.3 进一步增强了 v2023.1 中引入的保存多个工具窗口布局并在它们之间切换的功能。 新的 *Default*（默认）布局选项提供了将工作区外观还原到默认状态的快速方式。 此布局不可自定义，位于 *Window | Layouts*（窗口 | 布局）下。

![默认颜色编码编辑器标签页](https://www.jetbrains.com/idea/whatsnew/2023-3/img/color-coded_tabs_2.png)

![默认颜色编码编辑器标签页](https://www.jetbrains.com/idea/whatsnew/2023-3/img/color-coded_tabs_1.png)

![默认颜色编码编辑器标签页](https://www.jetbrains.com/idea/whatsnew/2023-3/img/color-coded_tabs_2.png)

![默认颜色编码编辑器标签页](https://www.jetbrains.com/idea/whatsnew/2023-3/img/color-coded_tabs_1.png)

### 默认颜色编码编辑器标签页

为了增强您在编辑器中同时处理多种文件类型的导航体验，我们为编辑器标签页引入了默认颜色编码，反映它们在 *Project*（项目）工具窗口中的外观。 您可以在 *Settings/Preferences | Appearance & Behavior | File Colors*（设置/偏好设置 | 外观与行为 | 文件颜色）中管理此设置。

![适用于 macOS 的新产品图标](https://www.jetbrains.com/idea/whatsnew/2023-3/img/icon_for_macos.png)

### 适用于 macOS 的新产品图标

对于 IntelliJ IDEA 2023.3 版本，我们重新设计了适用于 macOS 的 IntelliJ IDEA 图标，使其与操作系统的标准样式指南保持一致。

![Speed Search（快速搜索）快捷键](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Speed_search_2.png)

![Speed Search（快速搜索）快捷键](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Speed_search_1.png)

![Speed Search（快速搜索）快捷键](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Speed_search_2.png)

![Speed Search（快速搜索）快捷键](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Speed_search_1.png)

### *Speed Search*（快速搜索）快捷键

*Speed Search*（快速搜索）功能允许您在工具窗口和对话框中快速导航，现在可以通过快捷键使用。 将焦点置于树或列表上后，即可从工具窗口的 *Options*（选项）菜单轻松调用搜索。 您也可以使用快捷键 ⌘F，或者直接输入查询。 您可以在 *Settings / Preferences | Keymap*（设置/偏好设置 | 按键映射）中为 *Speed Search*（快速搜索）功能指定自定义快捷键。

## Java

![用于从字符串串联到字符串模板的迁移的检查](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Java_templates_inspection_preview.png)

### 用于从字符串串联到字符串模板的迁移的检查

随着 Java 21 中字符串模板的发布，我们实现了一项新的检查来简化串联字符串的替换。 IDE 现在将提供快速修复建议，用 `STR` 模板处理器替换串联。

![局部类的 Move（移动）重构](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Move_refactoring_v2_preview.png)

### 局部类的 *Move*（移动）重构

*Move*（移动）重构现在可以应用于局部类，从而允许您在代码库中重新定位局部类。 为此，首先通过上下文操作或按 F6 应用 *Convert local to inner*（将局部转换为内部）重构。 转换完成后，再次按 F6 调用 *Move*（移动）重构，为类选择所需目标。

![重做的 static 方法代码补全](https://www.jetbrains.com/idea/whatsnew/2023-3/img/code_completion_for_static_methods_preview.png)

### 重做的 static 方法代码补全

我们增强了实用方法的代码补全，这些方法通常为 static 并且位于其他类中。 现在，当目标类是第一个形参时，static 方法将被视为与实例方法相同，这使得相关补全建议更易被发现。 为了使代码补全弹出窗口保持整洁，以及实用方法的建议更易查找，我们将其收集到一个列表中，这个列表会在您第二次调用代码补全时显示。 随着功能的发展，未来可能发生变化。

![改进的常量条件表达式检查](https://www.jetbrains.com/idea/whatsnew/2023-3/img/inspections_for_constant_conditional_expressions_3.png)

![改进的常量条件表达式检查](https://www.jetbrains.com/idea/whatsnew/2023-3/img/inspections_for_constant_conditional_expressions_1.png)

![改进的常量条件表达式检查](https://www.jetbrains.com/idea/whatsnew/2023-3/img/inspections_for_constant_conditional_expressions_2.png)

![改进的常量条件表达式检查](https://www.jetbrains.com/idea/whatsnew/2023-3/img/inspections_for_constant_conditional_expressions_3.png)

![改进的常量条件表达式检查](https://www.jetbrains.com/idea/whatsnew/2023-3/img/inspections_for_constant_conditional_expressions_1.png)

### 改进的常量条件表达式检查

识别和高亮显示始终计算为相同值的条件表达式中的潜在错误时，IntelliJ IDEA 的代码分析现已涵盖更多场景。 例如，IDE 现在可以识别可能的字符串长度或估计字符串串联结果，在检测到冗余或可能有问题的代码时提示检查。 [了解详情](https://blog.jetbrains.com/idea/2023/11/intellij-idea-2023-3-eap-7/#improved-inspections-for-constant-conditional-expressions)。

## Scala

![更出色的 Scala 3 支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/enums.png)

### 更出色的 Scala 3 支持

IntelliJ IDEA 2023.3 提供了增强的 Scala 3 支持，特别关注枚举。 IDE 现在可以检测不可扩展的枚举并发出警告，改进了对生成符号的注解和修饰符的传播，并提供 `apply` 方法的精确类型扩展，仅考虑直接 super 方法。 由重复合成元素引起的错误现在可以正确高亮显示，枚举的自动补全也得到增强。 此外，我们还改进了 Scala 3 代码分析，消除了在看似随机的情况下偶尔出现的无效错误高亮显示。 除了增强 *Parameter Info*（形参信息）弹出窗口外，我们还改进了 `inline` 形参修饰符的代码高亮显示。

![改进的针对 Scala 的语法高亮显示](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Unreachable_code_Scala_preview.png)

### 改进的针对 Scala 的语法高亮显示

IntelliJ IDEA 在检测无法访问的代码方面有所改进，并且许多红色代码误报已被消除。 IDE 现在可以准确识别在抛出的异常以及 `try`、`catch` 和 `finally` 块的上下文中使用的代码，同时考虑隐式 import。 此外，我们还引入了对工作表等非标准 Scala 文件禁止 *Unused declaration*（未使用的声明） 和 *Method can be private*（方法可为 private）检查的功能。

![增强的嵌入提示设置](https://www.jetbrains.com/idea/whatsnew/2023-3/img/inlay_hints.png)

### 增强的嵌入提示设置

在 IntelliJ IDEA 2023.3 中，我们改进了为 Scala 配置[嵌入提示](https://www.jetbrains.com/help/idea/inlay-hints.html)的工作流，让您可以更轻松地了解可用的嵌入提示类型以及使用方式。 这些提示现已对齐并位于屏幕右侧，确保不会遮挡代码。

![X-Ray 功能](https://www.jetbrains.com/idea/whatsnew/2023-3/img/xray_preview.png)

### *X-Ray* 功能

编辑器中显示的嵌入提示和其他信息旨在增强代码可读性和简化编码。 但是，详细信息过多可能会使界面变得混乱。 借助针对 Scala 的新 *X-Ray* 功能，您可以禁用这些提示，然后在需要额外信息时双击并按住 ⌘ 将其重新打开。 这可以保持编辑器整洁，直到您想要查阅提示为止。

![针对 Scala 的 UI 改进](https://www.jetbrains.com/idea/whatsnew/2023-3/img/find_usages_file_structure.png)

### 针对 Scala 的 UI 改进

Scala 设置现在可以通过文本搜索轻松查找。 在 *Find Usages*（查找用法）窗格中，我们添加了 *File Structure*（文件结构）按钮，更全面地展示特定元素在代码库中如何使用。 为了提高代码的可读性，*Structure*（结构）工具窗口现在使用 `?=>` 区分隐式形参和常规形参。 我们还在 *Structure*（结构）和 *Project*（项目）工具窗口中为可运行对象、测试类、异常类和枚举引入了新图标。

![Using 指令](https://www.jetbrains.com/idea/whatsnew/2023-3/img/using_directives.png)

### `Using` 指令

我们通过多种方式增强了 IntelliJ IDEA 对 Scala CLI 的支持。 首先，您现在可以对 `using` 指令使用自动补全，当您执行自动补全时，它们的元素将自动格式化并正确高亮显示。 其次，自动导入符号时，它将无缝集成到文件中的 `using` 指令下，产生更干净、更整齐的代码。

### sbt 改进

此前，Scala 插件在直接依赖项中包含传递依赖项，导致考虑不必要的项目依赖项。 现在，插件在导入或重新加载时会计算所有传递依赖项。 修改后的方式可以让您更好地控制项目结构。 我们确保 sbt 模块正确依赖于标准 Scala 库，消除了它们可能错误依赖 Scala SDK 的情况。 此外，我们还解决了导入或重新加载 sbt 项目时导致 sbt 命令历史记录中断的问题，并且改进了创建新项目时获取可用 Scala 和 sbt 版本的流程。 我们还决定，从这个版本开始放弃对 [sbt-android 插件](https://github.com/scala-android/sbt-android)的支持。

## 版本控制系统

![对 GitLab 代码段的支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/GitLab_snippet_preview.png)

### 对 GitLab 代码段的支持

为了扩展 IntelliJ IDEA 2023.2 版本中引入的 [GitLab 集成](https://blog.jetbrains.com/blog/2023/07/26/gitlab-support-in-jetbrains-ide/)，我们添加了对 [GitLab 代码段](https://docs.gitlab.com/ee/user/snippets.html)的支持。 您现在可以直接在 IDE 中创建公共或私有代码段。 要创建新代码段，首先在编辑器中选择一个代码段（或在 *Project*（项目）工具窗口中选择文件或文件夹），右键点击所选内容以调用上下文菜单，然后选择 *Create Snippet*（创建代码段）选项。 随后将出现一个对话框，提示您提供有关代码段的常规信息并定义其隐私设置。

<video controls="" playsinline="" muted="" width="1232" class="idea-video" id="html5_video_hirpg9uaqgt" style="margin: 0px; padding: 0px; border: 0px; font-size: 16px; vertical-align: baseline; max-width: 100%; display: block; place-self: start;"></video>

### 全功能差异查看器

IntelliJ IDEA 2023.3 引入了一种改进的更改审查方式。 现在，您可以在单个可滚动框架中查看来自更改集的所有已修改文件，无需逐一查看每个文件。 新的差异查看器与 GitLab、GitHub 和 JetBrains Space 审查兼容。

## 生成工具

![更快的 Gradle 导入和索引](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Gradle_import_preview.png)

### 更快的 Gradle 导入和索引

Gradle 项目现在打开速度更快，因为 IDE 不再自动下载所有依赖项的源 JAR 文件。 如需访问特定源文件，您可以使用 ⌘B 快捷键导航，并在 IDE 提示时选择下载。

![针对所有项目的快速 Maven 导入](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Fast_Maven_import_for_all_projects.png)

### 针对所有项目的快速 Maven 导入

我们为所有项目启用了新的快速 Maven 项目导入机制，包括使用旧导入实现创建的项目。 这将显著缩短导入时间。 当您第一次在 2023.3 版本中打开项目时，IDE 将执行完全重新同步并通知您有关迁移的信息。 您可以在 *Settings/Preferences | Build, Execution, Deployment | Build Tools | Maven | Importing*（设置/偏好设置 | 构建、执行、部署 | 构建工具 | Maven | 导入）中调整此设置。

### Maven 项目模块基于限定名称的分组

我们改进了 IDE 处理项目模块分组的方式。 现在，它会根据限定名称对模块自动分组。 如果需要，您可以手动重命名模块，这些名称将在 Maven 项目的后续重新加载期间保留。

### Maven 支持更新

Maven 3.9.3 现与 IntelliJ IDEA 捆绑。

IDE 不再支持 Maven 3.0。

## 框架和技术

![HTTP 客户端中增强的 Structure（结构）工具窗口](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Enhanced_Structure_tool_window_in_HTTP_Client.png)

### HTTP 客户端中增强的 *Structure*（结构）工具窗口 Ultimate

我们改进了 HTTP 客户端中的 *Structure*（结构）工具窗口，以简化大型 `.http` 文件中的导航。 此更新为请求类型引入了颜色编码标签，可以更清晰地列出内容。

![HTTP 客户端中对就地变量的支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/in-place_variables_in_the_HTTP_Client.png)

### HTTP 客户端中对就地变量的支持

HTTP 客户端支持用 `@` 声明变量。 不再需要任何额外环境文件来跨请求重用值，并且 IDE 为值提供了代码补全，让您轻松添加。

### OAuth 2.0 支持



![](https://www.jetbrains.com/idea/whatsnew/2023-3/img/OAuth_2.0_Support_4_preview.png)



![](https://www.jetbrains.com/idea/whatsnew/2023-3/img/OAuth_2.0_Support_preview.png)



![](https://www.jetbrains.com/idea/whatsnew/2023-3/img/OAuth_2.0_Support_2_preview.png)



![](https://www.jetbrains.com/idea/whatsnew/2023-3/img/OAuth_2.0_Support_3_preview.png)



![](https://www.jetbrains.com/idea/whatsnew/2023-3/img/OAuth_2.0_Support_4_preview.png)



![](https://www.jetbrains.com/idea/whatsnew/2023-3/img/OAuth_2.0_Support_preview.png)

HTTP客户端支持用 OAuth 2.0 密码和客户端凭据授予类型进行身份验证。 

可用新语法 `{$auth.token("my-keycloak1")}` 在请求中引用身份验证数据，及导航到包含身份验证详细信息的 JSON 文件。 这项更改简化了请求执行过程，无需手动检索令牌。

[详情](https://blog.jetbrains.com/idea/2023/10/intellij-idea-2023-3-eap-3/#oauth-2.0-support)。

### HTTP 客户端中增强的 JSON 请求主体补全

![](https://www.jetbrains.com/idea/whatsnew/2023-3/img/JSON_request_body_completion_in_the_HTTP_Client_preview.png)

生成 HTTP 请求时可以更轻松地处理 JSON 请求主体。 IDE 现在会在请求主体的第一层插入所有必需形参。 它还为嵌套 JSON 对象和数组提供了自动补全。

![OpenAPI 规范的可视化编辑](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Visual_editing_for_OpenAPI_specifications_preview.png)

### OpenAPI 规范的可视化编辑 Ultimate

我们实现了可视化编辑功能，帮助您使用实时模板快速编写格式良好的 OpenAPI 规范。 这些操作适用于 YAML 和 JSON 文件。 要为对象插入模板，只需将鼠标悬停在装订区域的相关行上，然后点击 + 图标。

![OpenAPI 规范 3.1 和 Swagger UI 5.0 的预览](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Preview_for_OpenAPI_specifications_3.1_Swagger_UI_5.0_preview.png)

### OpenAPI 规范 3.1 和 Swagger UI 5.0 的预览 Ultimate

Swagger UI 的集成版本已更新至 5.0。 您可以通过浮动工具栏操作在 OpenAPI 文件中切换 Redoc 和更新的 Swagger UI 预览。 从 v5.0 开始，Swagger UI 还支持 OpenAPI 3.1 规范。

![URL 路径引用的自动代码补全](https://www.jetbrains.com/idea/whatsnew/2023-3/img/code_completion_for_URL_path_references_preview.png)

### URL 路径引用的自动代码补全 Ultimate

我们改进了处理 URL 的代码补全功能。 现在，在需要 URL 路径的位置输入 `/` 时，IDE 将自动使用服务器端点的 URL 填充代码补全弹出窗口。 操作时，它会考虑应用程序中的可用 API 和[附加的 OpenAPI 规范](https://www.jetbrains.com/help/idea/2023.2/openapi.html#remote-spec)，帮助您更快指定 URL 路径。

![Beans（Bean）工具窗口中的 Quarkus 和 Micronaut Bean](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Quarkus_and_Micronaut_beans.png)

### *Beans*（Bean）工具窗口中的 Quarkus 和 Micronaut Bean Ultimate

除了 Spring 组件之外，*Beans*（Bean）工具窗口现在还包括 Quarkus 和 Micronaut，提供了项目 Bean 配置的全面且整洁的概览。

![对 Micronaut 表达式语言的支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Micronaut_expression_language.png)

### 对 Micronaut 表达式语言的支持 Ultimate

IntelliJ IDEA Ultimate 现在为 [Micronaut 表达式语言](https://docs.micronaut.io/latest/guide/#evaluatedExpressions)提供代码高亮显示、补全和检查。 它还在所有相关注解中提供相应的语法高亮显示。

![对 Quarkus Qute 类型安全模板的支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Quarkus_Qute_type-safe_templates.png)

### 对 Quarkus Qute 类型安全模板的支持 Ultimate

IntelliJ IDEA Ultimate 现已支持 [Quarkus Qute 类型安全模板](https://quarkus.io/guides/qute-reference#typesafe_templates)，这意味着它现在可为形参声明、可用形参和形参实例成员提供代码补全。 您还可以使用 *Find Usages*（查找用法，⌘⌥⇧F7）和 *Go to Declaration*（转到声明，⌘B）导航到模板中的形参用法。

![Run with Coverage（使用覆盖率运行）和 Profile with IntelliJ Profiler（使用 IntelliJ Profiler 分析）可用于 Quarkus 项目](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Run_with_coverage_and_Profile_with_IntelliJ_Profiler_available_for_Quarkus_projects_2.png)

![Run with Coverage（使用覆盖率运行）和 Profile with IntelliJ Profiler（使用 IntelliJ Profiler 分析）可用于 Quarkus 项目](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Run_with_coverage_and_Profile_with_IntelliJ_Profiler_available_for_Quarkus_projects_1.png)

![Run with Coverage（使用覆盖率运行）和 Profile with IntelliJ Profiler（使用 IntelliJ Profiler 分析）可用于 Quarkus 项目](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Run_with_coverage_and_Profile_with_IntelliJ_Profiler_available_for_Quarkus_projects_2.png)

![Run with Coverage（使用覆盖率运行）和 Profile with IntelliJ Profiler（使用 IntelliJ Profiler 分析）可用于 Quarkus 项目](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Run_with_coverage_and_Profile_with_IntelliJ_Profiler_available_for_Quarkus_projects_1.png)

PreviousNext

- 
- 

### *Run with Coverage*（使用覆盖率运行）和 *Profile with IntelliJ Profiler*（使用 IntelliJ Profiler 分析）可用于 Quarkus 项目 Ultimate

您可以为 Quarkus 项目使用 *Run with Coverage*（使用覆盖率运行）和 *Profile with IntelliJ Profiler*（使用 IntelliJ Profiler 分析）。 两种运行配置都可以直接从 *Run*（运行）微件轻松访问。

![Spring 6.1 功能支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Spring_6.1_feature_support_2.png)

![Spring 6.1 功能支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Spring_6.1_feature_support_1.png)

![Spring 6.1 功能支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Spring_6.1_feature_support_2.png)

![Spring 6.1 功能支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Spring_6.1_feature_support_1.png)

PreviousNext

- 
- 

### Spring 6.1 功能支持 Ultimate

对 Spring 6.1 功能的支持已得到扩展。 对于新的 [RestClient](https://spring.io/blog/2023/07/13/new-in-spring-6-1-restclient)，IDE 现在提供全面支持，包括 URL 自动补全、*Find Usages*（查找用法）功能以及在 *Endpoints*（端点）工具窗口中查看所有客户端使用情况的功能。 此外，我们还为新的 [JdbcClient](https://github.com/spring-projects/spring-framework/issues/30931) 实现了 SQL 高亮显示和形参名称代码补全。 IntelliJ IDEA Ultimate 现在还可以识别 `@Scheduled` 调度程序特性，为此上下文中使用的 Bean 名称提供补全建议和验证。

![Spring GraphQL 支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Spring_for_GraphQL_support_3.png)

![Spring GraphQL 支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Spring_for_GraphQL_support_1.png)

![Spring GraphQL 支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Spring_for_GraphQL_support_2.png)

![Spring GraphQL 支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Spring_for_GraphQL_support_3.png)

![Spring GraphQL 支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Spring_for_GraphQL_support_1.png)

PreviousNext

- 
- 
- 

### Spring GraphQL 支持 Ultimate

我们引入了新的 [Spring GraphQL 插件](https://plugins.jetbrains.com/plugin/22807-spring-graphql)，为开发使用 GraphQL Java 构建的 Spring 应用程序添加了多个有价值的功能。 安装插件后，IDE 会为 Spring GraphQL 提供代码导航和补全建议以及架构映射。 您还可以导航到 GraphQL 架构实现并在 *Endpoints*（端点）工具窗口中查看。

![GraphQL 插件与 HTTP 客户端的集成](https://www.jetbrains.com/idea/whatsnew/2023-3/img/GraphQL_plugin_and_HTTP_Client_preview.png)

### GraphQL 插件与 HTTP 客户端的集成 Ultimate

IntelliJ IDEA Ultimate 2023.3 将 [GraphQL 插件](https://plugins.jetbrains.com/plugin/8097-graphql)与 HTTP 客户端集成。 您现在可以在 HTTP 客户端中生成 GraphQL 请求，将变量注入引入消息主体，并在处理 GraphQL 注入时使用代码补全功能。

![通过 Spring Initializr 添加和编辑 Spring Boot 启动器的选项](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Spring_Boot_starters_via_Spring_Initializr.png)

### 通过 Spring Initializr 添加和编辑 Spring Boot 启动器的选项 Ultimate

现在，您可以在项目既有模块中添加和修改 Spring Boot 启动器，更轻松地管理 Maven 和 Gradle 依赖项以及调整项目配置。 要添加或移除启动器依赖项，请按 Ctrl 并点击分别出现在 `pom.xml` 或 `build.gradle` 文件的 `<dependencies>` 或 `dependencies {` 块旁边的 *Edit Starters*（编辑启动器）嵌入微件。

![从 Spring Boot 配置创建数据源的选项](https://www.jetbrains.com/idea/whatsnew/2023-3/img/create_data_sources_from_Spring_Boot_configurations_preview.png)

### 从 Spring Boot 配置创建数据源的选项 Ultimate

现可根据 Spring Boot可根据application.yml文件自动检测并添加数据源配置。 点击装订区域中的数据源图标开启：

![在 Spring 项目中建立 Kafka 连接的选项](https://www.jetbrains.com/idea/whatsnew/2023-3/img/establish_a_Kafka_connection_in_Spring_projects_preview.png)

### 在 Spring 项目中建立 Kafka 连接的选项

现能在基于 Spring 的应用程序中创建 Kafka 连接，利用来自application.yaml配置文件的数据。 要连接到 Kafka 集群，首先确保已安装

[Kafka 插件](https://plugins.jetbrains.com/plugin/21704-kafka)

然后使用装订区域中的相应图标。

### Bicep 支持

![](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Bicep_preview.png)

我们引入了对

[Bicep](https://learn.microsoft.com/en-us/azure/azure-resource-manager/bicep/) 

的初步支持，这是专为 Azure 定制的基础架构即代码语言。 它可以转换为 Azure Resource Manager (ARM) 模板，旨在与 Azure 服务紧密集成。 IDE 现在提供代码高亮显示以及通过 Bicep 的语言服务器协议实现的代码补全。

## 远程开发和协作

### 改进的 Dev Container 支持

![改进的 Dev Container 支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Dev_Containers_3_preview.png)

![改进的 Dev Container 支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Dev_Containers.png)

![改进的 Dev Container 支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Dev_Containers_2.png)

![改进的 Dev Container 支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Dev_Containers_3_preview.png)

![改进的 Dev Container 支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Dev_Containers.png)

在 IntelliJ IDEA 2023.3 中，显著扩展了对 Dev Container 支持。 现在，您可以使用 [Dev Container 功能](https://containers.dev/implementors/features/)轻松添加额外的开发工具、运行时和库，从而简化基本组件的设置。 

为 Dev Container 引入 Docker Compose 支持，允许您从 IDE 启动主容器和依赖容器。 还实现了自动端口转发，即应用程序在 Dev Container 中开始侦听的任何端口都会被无缝转发。 [了解详情](https://blog.jetbrains.com/idea/2023/10/intellij-idea-2023-3-eap-6/#improved-support-for-dev-containers)。

## Kubernetes

![在 Kubernetes 中使用数据库](https://www.jetbrains.com/idea/whatsnew/2023-3/img/databases_in_k8s.png)

### 在 Kubernetes 中使用数据库 Ultimate

在处理 Kubernetes 中托管的数据库时，IntelliJ IDEA Ultimate 2023.3 提供了增强的用户体验。 例如，您现在可以在建立数据库连接时在 Kubernetes 中配置端口转发。

![对 YAML 文件中注释内 $schema 的支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Support_for_schema_in_YAML_files_preview.png)

### 对 YAML 文件中注释内 `$schema` 的支持 Ultimate

IDE 现在可以识别对作为注释包含的特定 YAML 架构的引用。 它还将根据指定架构在 YAML 文件中提供代码补全和语法验证，无论它是本地存储还是远程访问。

![从文件添加集群上下文的选项](https://www.jetbrains.com/idea/whatsnew/2023-3/img/KubernetesAddFromFile_preivew.png)

### 从文件添加集群上下文的选项 Ultimate

借助 IntelliJ IDEA Ultimate 2023.3，您可以从位于所需系统外的目录中的集群轻松添加 `kubeconfig` 上下文。 项目将包含指向 `kubeconfig` 文件的链接，使这些集群在您处理项目时可用。 以这种方式添加的 `kubeconfig` 内容不会被复制到系统 (`~/.kube/config`) 中，也不会被修改。 频繁从云或本地部署环境添加和移除 Kubernetes 集群时，此功能特别实用。 [了解详情](https://blog.jetbrains.com/idea/2023/11/intellij-idea-2023-3-eap-7/#option-to-add-cluster-contexts-from-a-file)。

### 改进的 Telepresence 调试 Ultimate

Telepresence 允许您在本地调试 Kubernetes 应用程序的微服务，就好像您的工作站是集群的一部分。 使用 Telepresence 连接到集群后，您可以从工作站访问集群的命名空间，并可以与其他微服务的 DNS 名称进行交互。 通过 Telepresence 拦截，您可以将来自其他集群服务的流量重定向到工作站的所选端口，并使用熟悉的 IDE 工具完全在本地调试微服务。

在这个版本中，我们将 Telepresence 工作流移动到集群的上下文菜单，使其更易于访问。 我们还简化了拦截管理，提供更流畅的调试体验。

### 对 Terraform 中 `import` 块的支持 Ultimate

IntelliJ IDEA Ultimate 2023.3 支持 1.5 版 Terraform 配置语言的顶层 `import` 块。 `import` 块允许您将当前基础架构纳入 Terraform 的管理范围。 当 `import` 在代码块中使用时，它将成为标准方案和 apply 阶段的一部分，而不再是状态操作。 Terraform 会为导入的资源执行自动代码生成，节省开发基础架构即代码的时间。

## 性能

### 默认启用的共享 JDK 索引下载

为了提高 IDE 的启动速度，我们默认启用了共享 JDK 索引的下载。 IntelliJ IDEA 现在将在后台无缝连接到专属资源，自动为 JDK 获取和应用共享索引。

## 数据库工具

![数据可视化](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Plots.png)

### 数据可视化 Ultimate

IntelliJ IDEA Ultimate 2023.3 集成了 [Lets-Plot 库](https://lets-plot.org/)，可以实现简化的无代码数据可视化。

![重做的导入功能](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Import.png)

### 重做的导入功能 Ultimate

IntelliJ IDEA 2023.3 提供了重做的导入功能，标志着向新 [*Modify Object*（修改对象）UI](https://blog.jetbrains.com/datagrip/2022/08/23/datagrip-2022-2-2-improves-the-modify-object-ui/) 的过渡已经完成。 现在可以导入到多个目标并同时进行编辑，例如更改多个文件的格式或编码或者更改多个目标的架构。

![内省改进](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Redshit_mat_view.png)

![内省改进](https://www.jetbrains.com/idea/whatsnew/2023-3/img/AutoSelect_introspection.png)

![内省改进](https://www.jetbrains.com/idea/whatsnew/2023-3/img/New_objects_in_SQL_Server.png)

![内省改进](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Redshit_mat_view.png)

![内省改进](https://www.jetbrains.com/idea/whatsnew/2023-3/img/AutoSelect_introspection.png)

PreviousNext

- 
- 
- 

### 内省改进 Ultimate

这个版本为内省引入了多项更新：

- 每个数据源的内省间隔。
- Oracle 的自动内省级别。
- 对 SQL Server 中新对象的支持：分区函数、分区方案、分区以及相关表和索引属性、分类表和文件组。
- 对 Redshift 中具体化视图的支持，这种视图现在可以内省并在专属节点中显示。

![数据编辑器中的可自定义数字格式](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Customizable_number_formats.png)

### 数据编辑器中的可自定义数字格式 Ultimate

这个版本为数据编辑器中数字的显示方式带来了更多灵活性。 最重要的是，您可以指定小数点和分组分隔符。

![DynamoDB 支持](https://www.jetbrains.com/idea/whatsnew/2023-3/img/DynamoDB.png)

### DynamoDB 支持

在 2023.3 版本中，我们引入了备受期待的 DynamoDB 支持，包括数据查看器、编辑器中的 PartiQL 支持，以及对具有键和索引的表的内省。

## Space 集成

![直接从仓库或代码审查开始编码](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Space-plugin-IDEA-start-coding-button.png)

### 直接从仓库或代码审查开始编码

通过 *Start coding*（开始编码）按钮，从 Space 中的仓库或代码审查导航到 IDE 中的相应文件。

![搜索 Maven 软件包](https://www.jetbrains.com/idea/whatsnew/2023-3/img/Space-plugin-IDEA-maven-autocomplete.png)

### 搜索 Maven 软件包

IntelliJ IDEA 2023.3 现在可以识别 Space 仓库中托管的 Maven 软件包，并在代码补全弹出窗口中给出建议。

## 其他

### 插件更新

2023.3 开始，对插件分发做出一些更改。 Android、Ant 和 GlassFish 插件现已通过 JetBrains Marketplace 提供。 也适用于 IntelliJ IDEA Community Edition 的 XPathView 插件。
