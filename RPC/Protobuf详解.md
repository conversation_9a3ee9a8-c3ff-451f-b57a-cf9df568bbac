## 0 前言

Google 公司内部的混合语言数据标准，是一种轻便、高效的结构化数据存储格式，可以用于结构化数据序列化，支持Java、Python、C++、Go等语言。Protobuf使用的时候需要定义IDL（Interface description language），然后使用不同语言的IDL编译器，生成序列化工具类。

## 1 优点

- 序列化后体积相比 JSON、Hessian小很多
- IDL能清晰地描述语义，所以足以帮助并保证应用程序之间的类型不会丢失，无需类似 XML 解析器
- 序列化反序列化速度很快，无需通过反射获取类型
- 消息格式升级和兼容性不错，可以做到向后兼容

## 2 使用

```java
/**
 *
 * // IDl 文件格式
 * synax = "proto3";
 * option java_package = "com.test";
 * option java_outer_classname = "StudentProtobuf";
 *
 * message StudentMsg {
 * //序号
 * int32 no = 1;
 * //姓名
 * string name = 2;
 * }
 * 
 */
 
StudentProtobuf.StudentMsg.Builder builder = StudentProtobuf.StudentMsg.newBuilder();
builder.setNo(103);
builder.setName("protobuf");

//把student对象转化为byte数组
StudentProtobuf.StudentMsg msg = builder.build();
byte[] data = msg.toByteArray();

//把刚才序列化出来的byte数组转化为student对象
StudentProtobuf.StudentMsg deStudent = StudentProtobuf.StudentMsg.parseFrom(data);

System.out.println(deStudent);
```

Protobuf非常高效，但对有反射和动态能力的语言，用起来费劲，这点不如Hessian，如用Java，这个预编译过程不是必须的，可考虑用Protobuf。

Protobuf不需要依赖IDL文件，可以直接对Java领域对象进行反/序列化操作，在效率上跟Protobuf差不多，生成的二进制格式和Protobuf是完全相同的，可以说是一个Java版本的Protobuf序列化框架。

## 3 不支持的情况

- 不支持null
- Protobuf不支持单纯的Map、List集合对象，需要包在对象里

## 错误案例

```java
String json = "{\n" + ""msgCnt": 1,\n" + ""nodes": [],\n" + ""timestamp": 1749710343787\n" + "}";
// 为啥mapData反序列化得到空对象
MAP mapData = JSON.parseObject(json, MAP.class);
```

Protobuf 类不支持直接 JSON 反序列化：MAP 是通过 protobuf 编译器生成的 Java 类，它继承自 com.google.protobuf.GeneratedMessageV3。这种类型的对象不能直接通过 FastJSON 的 JSON.parseObject() 方法进行反序列化。

构造方式不同：Protobuf 生成的类使用 Builder 模式创建对象，而不是普通的 setter 方法，FastJSON 无法识别这种特殊的构造方式。

字段访问方式不匹配：Protobuf 类使用特殊的 getter 方法（如 getMsgCnt()、getTimestamp()），而 FastJSON 期望的是标准的 JavaBean 属性。

### 解决方案

#### 方法1：使用 Protobuf 的 JSON 支持

使用 JsonFormat.parser()：这是 Google Protobuf 提供的官方 JSON 解析工具，专门用于将 JSON 字符串转换为 protobuf 对象。

使用 Builder 模式：通过 MAP.newBuilder() 创建构建器，然后使用 merge() 方法将 JSON 数据合并到构建器中，最后调用 build() 创建最终对象。

```java
import com.google.protobuf.util.JsonFormat;

String json = "{\n" +
        "\"msgCnt\": 1,\n" +
        "\"nodes\": [],\n" +
        "\"timestamp\": 1749710343787\n" +
        "}";

try {
    // 使用 Protobuf 的 JsonFormat 来解析 JSON
    MAP.Builder mapBuilder = MAP.newBuilder();
    JsonFormat.parser().merge(json, mapBuilder);
    road.data.proto.MAP mapData = mapBuilder.build();

    System.out.println("解析成功:");
    System.out.println("msgCnt: " + mapData.getMsgCnt());
    System.out.println("timestamp: " + mapData.getTimestamp());
    System.out.println("nodes size: " + mapData.getNodesCount());

    // 调用转换方法
    com.javaedge.dto.map.MapData result = toMapData(mapData);
    System.out.println("转换结果: " + result);
} catch (Exception e) {
    e.printStackTrace();
}
```

JsonFormat 是 Google Protobuf 官方提供的 JSON 支持工具。它了解 protobuf 的内部结构和字段映射规则，可正确处理 protobuf 特有的数据类型和嵌套结构，支持 proto3 的 JSON 映射规范。