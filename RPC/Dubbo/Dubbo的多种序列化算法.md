RPC 框架需要通过网络通信实现跨 JVM 的调用。既然需要网络通信，那就必然会使用到序列化与反序列化的相关技术，Dubbo 也不例外。
#  1 JDK序列化操作

## 1.1 实现 Serializable 接口
被序列化对象实现 Serializable 接口。

```java
public class Student implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String name;
    
    private int age;
    
    private transient StudentUtil studentUtil;
}

```
- transient 关键字的作用
在对象序列化过程中忽略被其修饰的成员属性变量。可用来修饰一些非数据型的字段以及一些可以通过其他字段计算得到的值。合理使用 transient，可降低序列化后的数据量，提高网络传输效率。

## 1.2 生成 serialVersionUID
生成一个序列号 serialVersionUID。该序列号非必需，但推荐生成。serialVersionUID 字面含义是序列化的版本号，只有序列化和反序列化的 serialVersionUID 都相同的情况下，才能够成功地反序列化。若类中没有定义 serialVersionUID，那么 JDK 也会随机生成一个 serialVersionUID。如果在某些场景中，你希望不同版本的类序列化和反序列化相互兼容，那就需要定义相同的 serialVersionUID。

## 1.3 重写 writeObject()/readObject() 
根据需求决定是否要重写 writeObject()/readObject() 方法，实现自定义序列化。

## 1.4 调用writeObject()/readObject()
调用 java.io.ObjectOutputStream 的 writeObject()/readObject() 进行序列化与反序列化。

Java 本身的序列化操作简单，但第三方序列化框架的速度更快、序列化的效率更高，而且支持跨语言操作。

# 2 常见序列化算法
## Apache Avro
与编程语言无关的序列化格式。Avro 依赖于用户自定义的 Schema，在进行序列化数据的时候，无须多余的开销，就可以快速完成序列化，并且生成的序列化数据也较小。当进行反序列化的时候，需要获取到写入数据时用到的 Schema。在 Kafka、Hadoop 以及 Dubbo 中都可以使用 Avro 作为序列化方案。

## FastJson
阿里开源的 JSON 解析库，可以解析 JSON 格式的字符串。它支持将 Java 对象序列化为 JSON 字符串，反过来从 JSON 字符串也可以反序列化为 Java 对象。FastJson 是 Java 程序员常用到的类库之一，正如其名，“快”是其主要卖点。从官方的测试结果来看，FastJson 确实是最快的，比 Jackson 快 20% 左右，但是近几年 FastJson 的安全漏洞比较多，所以你在选择版本的时候，还是需要谨慎一些。

## Fst（全称是 fast-serialization）
高性能 Java 对象序列化工具包，100% 兼容 JDK 原生环境，序列化速度大概是JDK 原生序列化的 4~10 倍，序列化后的数据大小是 JDK 原生序列化大小的 1/3 左右。目前，Fst 已经更新到 3.x 版本，支持 JDK 14。

## Kryo
高效Java序列化/反序列化库，Twitter、Yahoo、Apache等都在用，特别Spark、Hive等大数据领域。

提供快速、高效和易用的序列化 API。无论数据库存储、网络传输，都可用来完成 Java 对象序列化。

还可执行自动深拷贝和浅拷贝，支持环形引用。

### 特点

API 代码简单，序列化速度快，并且序列化之后得到的数据比较小。还提供了 NIO 的网络通信库——KryoNet。

## Hessian2
支持动态类型、跨语言的序列化协议，Java 对象序列化的二进制流可以被其他语言使用。Hessian2 序列化之后的数据可以进行自描述，不会像 Avro 那样依赖外部的 Schema 描述文件或者接口定义。Hessian2 可以用一个字节表示常用的基础类型，这极大缩短了序列化之后的二进制流。需要注意的是，在 Dubbo 中使用的 Hessian2 序列化并不是原生的 Hessian2 序列化，而是阿里修改过的 Hessian Lite，它是 Dubbo 默认使用的序列化方式。其序列化之后的二进制流大小大约是 Java 序列化的 50%，序列化耗时大约是 Java 序列化的 30%，反序列化耗时大约是 Java 序列化的 20%。

## Protobuf（Google Protocol Buffers）
Google开发的灵活、高效、自动化的、对结构化数据序列化的协议。

相比JSON，Protobuf有更高转化效率，时间效率和空间效率都是 JSON 5 倍左右。

可用于通信协议、数据存储等领域，本是语言无关、平台无关、可扩展的序列化结构数据格式。目前提供 C++、Java、Python、Go 等多种语言的 API，gRPC底层就用 Protobuf 实现序列化。

# 3 dubbo-serialization
Dubbo 为了支持多种序列化算法，单独抽象了一层 Serialize 层，在整个 Dubbo 架构中处于最底层，对应的模块是 dubbo-serialization 模块。 
- dubbo-serialization 模块
![](https://img-blog.csdnimg.cn/20201014033809352.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_1,color_FFFFFF,t_70#pic_center)

定义了 Dubbo 序列化层的核心接口，其中最核心的是 Serialization 接口，它是一个扩展接口，被 @SPI 接口修饰，默认扩展实现Hessian2Serialization。

Serialization 接口

![](https://img-blog.csdnimg.cn/20201014034547698.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_1,color_FFFFFF,t_70#pic_center)

Dubbo 提供了多个 Serialization 接口实现，用于接入各种各样的序列化算法
![](https://img-blog.csdnimg.cn/20201014034413953.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_1,color_FFFFFF,t_70#pic_center)


这里我们以默认的 hessian2 序列化方式为例，介绍 Serialization 接口的实现以及其他相关实现。 
![](https://img-blog.csdnimg.cn/20201014034921921.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_1,color_FFFFFF,t_70#pic_center)

Hessian2Serialization 中的 serialize() 方法创建的 ObjectOutput 接口实现为 Hessian2ObjectOutput，继承关系如下图所示：



在 DataOutput 接口中定义了序列化 Java 中各种数据类型的相应方法，如下图所示，其中有序列化 boolean、short、int、long 等基础类型的方法，也有序列化 String、byte[] 的方法。



ObjectOutput 接口继承了 DataOutput 接口，并在其基础之上，添加了序列化对象的功能，具体定义如下图所示，其中的 writeThrowable()、writeEvent() 和 writeAttachments() 方法都是调用 writeObject() 方法实现的。



Hessian2ObjectOutput 中会封装一个 Hessian2Output 对象，需要注意，这个对象是 ThreadLocal 的，与线程绑定。在 DataOutput 接口以及 ObjectOutput 接口中，序列化各类型数据的方法都会委托给 Hessian2Output 对象的相应方法完成，实现如下：
```java
public class Hessian2ObjectOutput implements ObjectOutput {
    private static ThreadLocal<Hessian2Output> OUTPUT_TL = ThreadLocal.withInitial(() -> {
        // 初始化Hessian2Output对象
        Hessian2Output h2o = new Hessian2Output(null);        h2o.setSerializerFactory(Hessian2SerializerFactory.SERIALIZER_FACTORY);
        h2o.setCloseStreamOnClose(true);
        return h2o;
    });
    private final Hessian2Output mH2o;
    public Hessian2ObjectOutput(OutputStream os) {
        mH2o = OUTPUT_TL.get(); // 触发OUTPUT_TL的初始化
        mH2o.init(os);
    }
    public void writeObject(Object obj) throws IOException {
        mH2o.writeObject(obj);
    }
    ... // 省略序列化其他类型数据的方法
}
```

Hessian2Serialization 中的 deserialize() 方法创建的 ObjectInput 接口实现为 Hessian2ObjectInput，继承关系如下所示：



Hessian2ObjectInput 具体的实现与 Hessian2ObjectOutput 类似：在 DataInput 接口中实现了反序列化各种类型的方法，在 ObjectInput 接口中提供了反序列化 Java 对象的功能，在 Hessian2ObjectInput 中会将所有反序列化的实现委托为 Hessian2Input。
