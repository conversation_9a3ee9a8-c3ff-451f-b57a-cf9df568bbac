## 1 案例一

用户注册后，发消息到MQ，然后会员服务监听消息异步处理。有时发现，虽然用户服务先落库再发MQ，但会员服务收到消息后去查询DB，却发现DB还没有新用户信息。why？

### 1.1 可能原因

用户注册的代码端把异常都吞了，没抛出，而用户注册又报错，但还是继续执行并发了MQ消息。

可能数据写到主库，再查询的从库。但因主从延迟，导致没查到。

有时因为业务代码 数据落库 和 发MQ消息 放在一个事务，有一定概率收到消息时，事务还没提交完成。当时处理方式是收到MQ消息时sleep 1s，或许应先提交事务，完成后再发MQ消息。

Q：若发消息失败怎么办？

A：建立本地消息表，确保MQ消息可补偿，把业务处理和保存MQ消息到本地消息表操作放在同一事务内处理，再异步发送和补偿发送消息表中的消息到MQ。

1. 先保存用户注册的数据，同时记录下要发送MQ的消息，这两个入库在一个事务

1. 异步任务，定时拉取MQ的消息表，发送到MQ进行处理

这就是本地事务消息的实现，第2步不一定非得定时任务拉取到。第1步完成后直接发MQ即可， 定时任务拉取只是用来做补偿。

- 若有多个补偿实例，会不会造成消息重复？
  补偿需要配合幂等，生产应用肯定用DB做幂等（如消息id）。


- Pro发消息给MQ，即使异步发送，也会有listener来监听投递消息是否成功。若失败，也可重试。

## 2 案例二

除了使用Spring AMQP实现死信消息的重投递外，RabbitMQ 2.8.0后支持的死信交换器DLX也可实现类似功能。

自定义的死信队列，其实是发送失败，主要是生产者发送到MQ时，发送失败，进入自定义的死信队列。

技术分享RabbitMQ的资料：
http://note.youdao.com/noteshare?id=e9f2f88c6c7fcb7ac690463eb230650a

## 3 案例三 @EqualsAndHashCode注解使用

按某几个字段去重(acctId,billingCycleId,prodInstId,offerId)
最简单的，遍历集合areaDatas 后用contains方法判断 重写AcctItemYzfBean实体类的equals方法实现，
请问有没有更好的方法？ 代码如下

```java
List<AcctItemYzfBean> newList = new CopyOnWriteArrayList<>();
//循环过滤、增强翼支付数据
Optional.ofNullable(areaDatas)//集合判空
.orElse(new ArrayList<>())
.stream()//转化为流 便于下面过滤和增强数据
.filter(Objects::nonNull)//元素判空
.filter(yzfBean -> this.judgeIfOfferId(yzfBean))//判断销售品ID是否相同
.filter(yzfBean -> this.enhanceYzfBean(yzfBean))//增强过滤accNbr和acctId
.filter(yzfBean -> this.judgeIfArrears(yzfBean))//判断是否不欠费
.filter(yzfBean -> this.judgeIfCancel(yzfBean))//判断是否销账金额大于0
.filter(yzfBean -> this.judgeIfReturn(yzfBean))//判断是否上月未返还
.forEach(yzfBean -> {
    //去重 重写AcctItemYzfBean.equals方法
    if(!newList.contains(yzfBean)) {
    //增强latnName
    yzfBean.setLatnName(commonRegionMap.get(yzfBean.getRegionId()));
    //增强areaCode
    yzfBean.setAreaCode(areaCode);
    //数据封装
    newList.add(yzfBean);
}
});
```


重写的equals方法
```java
@Override
public boolean equals(Object yzfBeanObj) {
    if(yzfBeanObj instanceof AcctItemYzfBean) {
    AcctItemYzfBean yzfBean = (AcctItemYzfBean) yzfBeanObj;
    if(Tools.isEmpty(yzfBean.getAcctId(), yzfBean.getBillingCycleId(), yzfBean.getProdInstId(), yzfBean.getOfferId())) {
    return false;
    }
    if(yzfBean.getAcctId().equals(this.acctId) && yzfBean.getBillingCycleId().equals(this.billingCycleId)
    && yzfBean.getProdInstId().equals(this.prodInstId) && yzfBean.getOfferId().equals(this.offerId)) {
    return true;
    }
    }
    return super.equals(yzfBeanObj);
}
```
如下面的类，id1和id2重复认为是重复的，id3不需要考虑
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
static class Test {
    private String id1;
    private String id2;
    @EqualsAndHashCode.Exclude
    private String id3;
}
```
通过Set去重或者通过distinct去重即可：
```java
List<Test> list = new ArrayList<>();
list.add(new Test("a","b","c"));
list.add(new Test("a","b","d"));
System.out.println(list.stream().collect(Collectors.toSet()));
System.out.println(list.stream().distinct().collect(Collectors.toList()));
```
