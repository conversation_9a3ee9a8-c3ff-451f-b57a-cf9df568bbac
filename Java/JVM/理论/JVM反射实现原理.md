## 0 前言

反射允许正在运行的 Java 程序观测甚至修改程序的动态行为。如：

- 通过 Class 对象枚举该类中的所有方法
- 通过 Method.setAccessible绕过 Java 语言访问权限，在private方法所在类之外也能调用该方法

为保证框架可扩展性，反射根据配置文件加载不同类。如Spring IoC即依赖反射。

很多人觉得反射机制较慢，甚至甲骨文关于反射的教学网页，也强调反射性能开销大，为啥？

## 1 反射调用的实现

### 1.1 Method.invoke咋实现的？

```java
@CallerSensitive
public Object invoke(Object obj, Object... args)
    throws IllegalAccessException, IllegalArgumentException,
       InvocationTargetException
{
    if (!override) {
        if (!Reflection.quickCheckMemberAccess(clazz, modifiers)) {
            Class<?> caller = Reflection.getCallerClass();
            checkAccess(caller, clazz, obj, modifiers);
        }
    }
    MethodAccessor ma = methodAccessor;             // read volatile
    if (ma == null) {
        ma = acquireMethodAccessor();
    }
    return ma.invoke(obj, args);
}
```

委派给 MethodAccessor：

```java
// 提供了Java.lang.reflect.method.invoke（）的声明。每个方法对象都配置为实现此接口的（可能动态生成的）类（可能动态生成的）类。
public interface MethodAccessor {
    /** 匹配java.lang.reflect.Method 的标准*/
    public Object invoke(Object obj, Object[] args)
        throws IllegalArgumentException, InvocationTargetException;
}
```

MethodAccessor有两个实现：

- 本地实现，通过本地方法，实现反射调用
- 委派实现，使用委派模式

每个 Method 实例的第一次反射调用都会生成一个委派实现，委派的具体实现就是个本地实现。进入JVM内部后，便拥有 Method 实例所指向方法的具体地址。这时反射调用无非就是将入参准备好，再调用进入目标方法。

```java
// v0 版本
import java.lang.reflect.Method; // 导入反射相关的Method类

public class Test {
    public static void target(int i) {
        new Exception("#" + i).printStackTrace();
    }

    public static void main(String[] args) throws Exception {
        Class<?> klass = Class.forName("Test"); // 通过类名获取Class对象
        Method method = klass.getMethod("target", int.class); // 获取名为"target"，参数为int的方法对象
        method.invoke(null, 0); // 通过反射调用target方法，传入参数0（静态方法对象为null）
    }
}

# Java 10
$ java Test
java.lang.Exception: #0
    at Test.target(Test.java:5) // 堆栈跟踪，定位到Test.target方法
    at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) // 反射调用的本地方法实现
    at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) // 3. 反射调用的本地方法实现
    at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) // 2. 反射调用的委派实现
    at java.base/java.lang.reflect.Method.invoke(Method.java:564) // 1. 反射调用的入口
    at Test.main(Test.java:13) // 0. 主方法入口
```

打印反射调用到目标方法时的栈轨迹。获取一个指向 Test.target 方法的 Method 对象，并用它进行反射调用：

- 先调Method.invoke
- 再进入委派实现
- 再进入本地实现
- 最后到达目标方法

#### 为何反射要委派实现中间层？

直接给本地实现不好？Java 反射机制还设立另一种动态生成字节码的实现（称为动态实现），直接用 invoke 指令调用目标方法。

采用委派是为了能在本地实现及动态实现切换。

```java
package jdk.internal.reflect;
 
public class GeneratedMethodAccessor1 extends ... {
  @Overrides    
  public Object invoke(Object obj, Object[] args) throws ... {
    Test.target((int) args[0]);
    return null;
  }
}
```

### 1.2 动态实现

和本地实现相比，运行效率快20倍。因无需经Java到C++到Java切换，但生成字节码很耗时，仅调用一次，反而是本地实现要快3~4倍。

许多反射调用仅执行一次，JVM置阈值15（可通过 -Dsun.reflect.inflationThreshold调整）：

- 当某反射调用的调用次数＜15，采用本地实现
- ≥15，开始动态生成字节码，并将委派实现的委派对象切换至动态实现，这过程即Inflation

```java
// "Inflation" “膨胀”机制.加载字节码来实现
// Method.invoke() 和 Constructor.newInstance() 当前成本是首次调用通过本地代码（native code）调用的3-4倍（尽管后续调用已被基准测试为超过20倍更快）。不幸的是，这种成本增加了某些密集使用反射的应用的启动时间（但每个类只一次）来引导它们自己。
// 为了避免这种惩罚，我们为Methods和Constructors的前几次调用重用现有的JVM入口点，然后切换到基于字节码的实现。

// 包私有，以便NativeMethodAccessorImpl和NativeConstructorAccessorImpl 可以访问
private static boolean noInflation = false;
private static int inflationThreshold = 15;
```

反射是Java动态性的核心，但它引入了性能开销。"Inflation"是JDK为平衡反射的启动性能和长期性能而设计的优化策略。 

#### 1. **背景和问题引入**

Java反射API（如`java.lang.reflect.Method.invoke()`和`java.lang.reflect.Constructor.newInstance()`）允许在运行时动态调用方法或创建实例，这是许多框架（如Spring IOC、Hibernate ORM、动态代理）的基础。然而，反射的性能一直是痛点：

- **本地代码（Native Code）实现**：反射最初通过JVM的C++本地代码实现（例如，通过JNI调用）。这很高效，因为它直接访问JVM内部结构，但缺乏灵活性。
- **字节码（Bytecode）实现**：为了优化，JDK可以动态生成字节码来实现反射调用。这种方式在首次执行时需要加载和编译字节码，导致开销高（注释中提到首次调用成本是native的3-4倍），但后续调用由于JIT（Just-In-Time）编译优化，能达到native的20倍以上速度。
- **问题**：在应用启动阶段，如果有大量反射调用（如Spring容器初始化时扫描Bean），首次加载字节码的开销会显著增加启动时间（尤其在反射密集型应用中，每个类可能只“引导”一次，但累积起来很可观）。

"Inflation"机制就是为了解决这个权衡：**在启动期使用高效的native实现，前几次调用后“膨胀”到更快的字节码实现**。这个机制从JDK 1.4开始引入，并在后续版本（如JDK 8+）优化。

#### 2. **核心工作原理**

"Inflation"（膨胀）指的是反射访问器（Accessor）从native实现逐步“膨胀”到字节码实现的动态切换过程。关键组件包括：

- **NativeMethodAccessorImpl** 和 **NativeConstructorAccessorImpl**：这些是反射的初始实现，使用native代码。它们是“瘦”的（高效但简单）。
- **DelegatingMethodAccessorImpl**：一个委托类，负责在native和字节码之间切换。
- **GeneratedMethodAccessor**：动态生成的字节码类（使用ASM或类似工具生成），这是“膨胀”后的高效实现。

**工作流程**（以`Method.invoke()`为例）：

1. **首次调用**：
   - 当你调用`method.invoke(obj, args)`时，反射工厂（`ReflectionFactory`）创建一个`NativeMethodAccessorImpl`实例。
   - 这个实例通过native代码直接调用JVM的内部方法（例如，访问方法句柄或直接执行）。
   - 性能好，无需加载额外字节码。
2. **计数和阈值检查**：
   - 每次调用时，内部有一个调用计数器（per-method或per-constructor）。
   - 阈值由`inflationThreshold`控制，默认15（注释中所示）。这个值是经验调优的：太低会导致过早膨胀，增加启动开销；太高则延迟了字节码优化的好处。
   - 如果`noInflation = true`（默认false），可以禁用膨胀，始终使用native（用于调试或特定场景）。
3. **膨胀切换**：
   - 当调用次数达到阈值（e.g., 15）时，JVM触发“膨胀”：
     - 使用`sun.reflect.ClassFileAssembler`或类似工具动态生成一个字节码类（e.g., `GeneratedMethodAccessor1`），这个类是针对特定方法的优化字节码实现。
     - 委托器切换：`DelegatingMethodAccessorImpl`将后续调用委托给这个生成的字节码类。
   - 切换后，字节码会被JIT编译成机器码，性能大幅提升（注释中提到20x更快），因为它避免了native的间接调用，并利用了JVM的热点优化。
4. **包私有访问**：
   - 注释中提到这些变量是包私有的，以便`NativeMethodAccessorImpl`和`NativeConstructorAccessorImpl`访问。这确保了只有JDK内部类能控制膨胀，而外部代码不能随意修改。

伪代码表示：

```java
class DelegatingMethodAccessorImpl implements MethodAccessor {
    private MethodAccessor delegate; // 初始为 NativeMethodAccessorImpl
    private int numInvocations = 0;
    
    public Object invoke(Object obj, Object[] args) {
        if (++numInvocations > ReflectionFactory.inflationThreshold && !ReflectionFactory.noInflation) {
            // 触发膨胀：生成字节码并切换
            delegate = new GeneratedMethodAccessor(...); // 动态生成
        }
        return delegate.invoke(obj, args);
    }
}
```

#### 3. **设计动机和好处**

- **动机**：反射在启动期（如应用引导、框架初始化）是瓶颈，但运行期需要高吞吐。膨胀机制是“懒加载”思想的应用：延迟昂贵的字节码生成，直到证明该方法会被频繁调用。
- 好处
  - **启动时间优化**：反射密集应用（如Spring Boot启动时扫描注解）受益最大。首次15次用native，启动快；之后切换到高速字节码。
  - **长期性能提升**：字节码实现更灵活，能被JIT深度优化，适合高并发场景（如Web服务）。
  - **资源效率**：只为“热点”反射方法生成字节码，避免无谓开销。
  - **兼容性**：对开发者透明，无需修改代码。

#### 4. **潜在缺点和注意事项**

- 缺点
  - **首次切换开销**：达到阈值时的膨胀过程本身有开销（生成字节码、类加载），可能导致短暂的性能抖动。
  - **内存消耗**：每个膨胀的方法都会生成一个新类，占用PermGen/Metaspace（在JDK 8+中）。
  - **调试复杂**：膨胀后的字节码是动态生成的，调试工具（如jstack）可能不易追踪。
  - **不适合所有场景**：如果应用反射调用很少（<15次），永远不会膨胀，错失潜在优化。
- 注意事项：
  - **配置**：可以通过系统属性调整，如`-Dsun.reflect.noInflation=true`禁用膨胀，或`-Dsun.reflect.inflationThreshold=5`降低阈值（不推荐随意改，会影响稳定性）。
  - **JDK版本差异**：在JDK 9+的模块化系统中，反射受限（e.g., illegal reflective access），膨胀机制仍有效，但需注意模块导出。
  - **性能监控**：使用工具如VisualVM、JMH基准测试反射性能；或启用`-XX:+PrintCompilation`查看JIT日志。

#### 5. **实际应用场景和架构影响**

在业务软件架构中，反射常用于动态特性，但需谨慎：

- 场景示例
  - **Spring框架**：Bean注入、AOP代理大量使用反射。膨胀确保启动快（e.g., 微服务冷启动<5s），运行期高性能。
  - **ORM工具**（如Hibernate）：实体映射反射调用多，膨胀优化查询性能。
  - **动态代理**（e.g., JDK Proxy）：代理方法调用受益于字节码实现。
  - **云原生应用**：在Kubernetes中，快速启动至关重要；膨胀减少了反射导致的Pod启动延迟。
- 架构建议
  - **避免过度反射**：优先使用Lambda/MethodHandle（JDK 7+引入，更高效替代反射）。
  - **性能调优**：在高负载系统中，监控Metaspace使用率；如果反射是瓶颈，考虑GraalVM（native image编译，消除反射开销）。
  - **测试策略**：在架构设计时，进行压力测试反射路径，确保膨胀不引起抖动。
  - **业务影响**：对于SaaS平台，反射优化能提升用户体验（更快响应）；但在低端硬件上，膨胀可能增加内存压力，需权衡。

### 1.3 观察Inflation

将刚才的例子更改为如下，将反射调用循环 20 次：

```java
import java.lang.reflect.Method;
 
public class Test {
  public static void target(int i) {
    new Exception("#" + i).printStackTrace();
  }
 
  public static void main(String[] args) throws Exception {
    Class<?> klass = Class.forName("Test");
    Method method = klass.getMethod("target", int.class);
    for (int i = 0; i < 20; i++) {
      method.invoke(null, i);
    }
  }
}
 
# 使用 -verbose:class 打印加载的类
$ java -verbose:class Test
...
java.lang.Exception: #14
        at Test.target(Test.java:5)
        at java.base/jdk.internal.reflect.NativeMethodAccessorImpl .invoke0(Native Method)
        at java.base/jdk.internal.reflect.NativeMethodAccessorImpl .invoke(NativeMethodAccessorImpl.java:62)
        at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl .invoke(DelegatingMethodAccessorImpl.java:43)
        at java.base/java.lang.reflect.Method.invoke(Method.java:564)
        at Test.main(Test.java:12)
[0.158s][info][class,load] ...
...
[0.160s][info][class,load] jdk.internal.reflect.GeneratedMethodAccessor1 source: __JVM_DefineClass__
java.lang.Exception: #15
       at Test.target(Test.java:5)
       at java.base/jdk.internal.reflect.NativeMethodAccessorImpl .invoke0(Native Method)
       at java.base/jdk.internal.reflect.NativeMethodAccessorImpl .invoke(NativeMethodAccessorImpl.java:62)
       at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl .invoke(DelegatingMethodAccessorImpl.java:43)
       at java.base/java.lang.reflect.Method.invoke(Method.java:564)
       at Test.main(Test.java:12)
java.lang.Exception: #16
       at Test.target(Test.java:5)
       at jdk.internal.reflect.GeneratedMethodAccessor1 .invoke(Unknown Source)
       at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl .invoke(DelegatingMethodAccessorImpl.java:43)
       at java.base/java.lang.reflect.Method.invoke(Method.java:564)
       at Test.main(Test.java:12)
...
```

第 15 次（从 0 开始数）反射调用时，触发了动态实现。这时，JVM额外加载了不少类。

最重要的GeneratedMethodAccessor1（第 30 行）。且从第 16 次反射调用开始，便切换至刚生成的动态实现（第 40 行）。

反射调用的 Inflation 机制可通过参数（-Dsun.reflect.noInflation=true）关闭，反射调用一开始便会直接生成动态实现，而不会使用委派实现或本地实现。

## 2 性能开销

刚才先后进行 Class.forName，Class.getMethod 以及 Method.invoke：

- Class.forName：调用本地方法
- Class.getMethod：遍历该类的公有方法。若没匹配到，还将遍历父类的public方法，俩操作都很费时

getMethod为代表的查找方法操作，会返回查找得到结果的一份拷贝。因此，应避免在热点代码使用返回 Method 数组的 getMethods 或 getDeclaredMethods 方法，以减少不必要堆空间消耗。

实践往往会在应用程序中缓存 Class.forName 和 Class.getMethod 结果。因此，下面只关注反射调用本身性能开销。

为比较直接调用、反射调用性能差距，案例如下：反射调用循环二十亿次。记录每跑一亿次的时间。取最后五个记录的平均值，作为预热后峰值性能。PC一亿次直接调用耗费的时间大约120ms。这和不调用的时间是一致的。原因在于这段代码属热循环，同样会触发即时编译。且即时编译会将对 Test.target 的调用内联，消除调用开销。

```java
mport java.lang.reflect.Method;
 
public class Test {
  public static void target(int i) {
    // 空方法
  }
 
  public static void main(String[] args) throws Exception {
    Class<?> klass = Class.forName("Test");
    Method method = klass.getMethod("target", int.class);
 
    long current = System.currentTimeMillis();
    for (int i = 1; i <= 2_000_000_000; i++) {
      if (i % 100_000_000 == 0) {
        long temp = System.currentTimeMillis();
        System.out.println(temp - current);
        current = temp;
      }
 
      method.invoke(null, 128);
    }
  }
}
 
```

以120ms为基准比较反射调用性能开销。

由于目标方法 Test.target 接收一个 int 类型的参数，传128作反射调用的参数，测得的结果约为基准的 2.7 倍。先看反射调用之前字节码都做了什么。

```bash
   59: aload_2                         // 加载 Method 对象
   60: aconst_null                     // 反射调用的第一个参数 null
   61: iconst_1
   62: anewarray Object                // 生成一个长度为 1 的 Object 数组
   65: dup
   66: iconst_0
   67: sipush 128
   70: invokestatic Integer.valueOf    // 将 128 自动装箱成 Integer
   73: aastore                         // 存入 Object 数组中
   74: invokevirtual Method.invoke     // 反射调用
```

截取循环中反射调用编译而成的字节码。这段字节码除反射调用，还额外做了：

- Method.invoke是变长参数方法，字节码层最后一个参数是 Object 数组。Java 编译器会在方法调用处生成一个长度为传入参数数量的 Object 数组，并将传入参数一一存储进该数组

- Object数组不能存储基本类型，Java 编译器会对传入的基本类型参数自动装箱。Java 缓存[-128, 127]所有整数所对应Integer对象。当需要自动装箱的整数在这个范围之内时，便返回缓存的 Integer，否则需新建一个Integer对象


这两操作带来性能开销，还可能占用堆内存，使GC更频繁。

## 3 咋消除这部分开销？

将缓存范围扩大至覆盖 128（即-Djava.lang.Integer.IntegerCache.high=128），避免要新建 Integer 对象的场景。或在循环外缓存 128 自动装箱得到的 Integer 对象，并直接传入反射调用中。这两种方法约为基准的 1.8 倍。

第一个因变长参数而自动生成的 Object 数组。既然每个反射调用对应的参数个数是固定的，可选择在循环外新建一个 Object 数组，设置好参数，直接交给反射调用：

```java
import java.lang.reflect.Method;
 
public class Test {
  public static void target(int i) {
    // 空方法
  }
 
  public static void main(String[] args) throws Exception {
    Class<?> klass = Class.forName("Test");
    Method method = klass.getMethod("target", int.class);
 
    Object[] arg = new Object[1]; // 在循环外构造参数数组
    arg[0] = 128;
 
    long current = System.currentTimeMillis();
    for (int i = 1; i <= 2_000_000_000; i++) {
      if (i % 100_000_000 == 0) {
        long temp = System.currentTimeMillis();
        System.out.println(temp - current);
        current = temp;
      }
 
      method.invoke(null, arg);
    }
  }
}
```

结果反而更糟，为基准 2.9 倍。why？如果你在上一步解决了自动装箱之后查看运行时的 GC 状况，你会发现这段程序不会触发 GC。原因在于，原本的反射调用被内联了，从而使得即时编译器中的逃逸分析将原本新建的 Object 数组判定为不逃逸的对象。

如果一个对象不逃逸，那么即时编译器可以选择栈分配甚至是虚拟分配，也就是不占用堆空间。

如在循环外新建数组，即时编译器无法确定这个数组会不会中途被更改，因此无法优化掉访问数组的操作，得不偿失。

目前最好记录 1.8 倍。还能再进一步吗？可关闭Inflation机制，取消委派实现，直接使用动态实现。

每次反射调用都会检查目标方法权限，而这检查同样可在 Java 代码关闭，关闭这两项机制后，即得到如下代码，为基准 1.3 倍。

```java
 
import java.lang.reflect.Method;
 
// 在运行指令中添加如下两个虚拟机参数：
// -Djava.lang.Integer.IntegerCache.high=128
// -Dsun.reflect.noInflation=true
public class Test {
  public static void target(int i) {
    // 空方法
  }
 
  public static void main(String[] args) throws Exception {
    Class<?> klass = Class.forName("Test");
    Method method = klass.getMethod("target", int.class);
    method.setAccessible(true);  // 关闭权限检查
 
    long current = System.currentTimeMillis();
    for (int i = 1; i <= 2_000_000_000; i++) {
      if (i % 100_000_000 == 0) {
        long temp = System.currentTimeMillis();
        System.out.println(temp - current);
        current = temp;
      }
 
      method.invoke(null, 128);
    }
  }
}
 
```

至此，基本把反射调用榨干。接下来，把反射调用的性能开销提回去。

例子中的反射调用够快，主要因为即时编译器中的方法内联。关闭Inflation时，内联瓶颈在Method.invoke中对MethodAccessor.invoke的调用。

![](https://static001.geekbang.org/resource/image/93/b5/93dec45b7af7951a2b6daeb01941b9b5.png)

生产环境中，拥有多个不同反射调用，对应多个 GeneratedMethodAccessor，即动态实现。

由于JVM关于上述调用点的类型 profile（对 invokevirtual 或 invokeinterface，JVM会记录调用者的具体类型，称之为类型profile）无法同时记录这么多个类，因此可能造成所测试的反射用没有被内联的情况。

```java
import java.lang.reflect.Method;
 
public class Test {
  public static void target(int i) {
    // 空方法
  }
 
  public static void main(String[] args) throws Exception {
    Class<?> klass = Class.forName("Test");
    Method method = klass.getMethod("target", int.class);
    method.setAccessible(true);  // 关闭权限检查
    polluteProfile();
 
    long current = System.currentTimeMillis();
    for (int i = 1; i <= 2_000_000_000; i++) {
      if (i % 100_000_000 == 0) {
        long temp = System.currentTimeMillis();
        System.out.println(temp - current);
        current = temp;
      }
 
      method.invoke(null, 128);
    }
  }
 
  public static void polluteProfile() throws Exception {
    Method method1 = Test.class.getMethod("target1", int.class);
    Method method2 = Test.class.getMethod("target2", int.class);
    for (int i = 0; i < 2000; i++) {
      method1.invoke(null, 0);
      method2.invoke(null, 0);
    }
  }
  public static void target1(int i) { }
  public static void target2(int i) { }
}
 
```

测试循环之前调用了 polluteProfile。该方法将反射调用另外两个方法，并且循环上 2000 遍。

而测试循环则保持不变。测得的结果约为基准的 6.7 倍。也就是说，只要误扰了 Method.invoke 方法的类型 profile，性能开销便会从 1.3 倍上升至 6.7 倍。

之所以这么慢，除了没有内联之外，另外一个原因是逃逸分析不再起效。这时候，我们便可以采用刚才 v3 版本中的解决方案，在循环外构造参数数组，并直接传递给反射调用。这样子测得的结果约为基准的 5.2 倍。

除此之外，我们还可以提高 Java 虚拟机关于每个调用能够记录的类型数目（对应虚拟机参数 -XX:TypeProfileWidth，默认值为 2，这里设置为 3）。最终测得的结果约为基准的 2.8 倍，尽管它和原本的 1.3 倍还有一定的差距，但总算是比 6.7 倍好多了。

## 4 反射 API 简介

使用反射 API 第一步，获取 Class 对象，三种方式：

- 静态方法Class.forName
- 对象的 getClass() 方法
- 类名 +“.class”访问：
  - 基本类型的包装类型（wrapper classes）有个名为“TYPE”的 final 静态字段，指向该基本类型对应的 Class 对象。如Integer.TYPE 指向 int.class
  - 数组类型，类名 +“[ ].class”来访问，如 int[ ].class

Class 类和 java.lang.reflect 包还提供许多返回 Class 对象的方法。如数组类的 Class 对象，调用 Class.getComponentType() 可获得数组元素的类型。

得到Class对象，便可用

## 5 反射功能

- newInstance()生成一个该类的实例。它要求该类中拥有一个无参构造器
- isInstance(Object) 判断一个对象是否该类的实例，语法等同instanceof关键字
- Array.newInstance(Class,int)构造该类型的数组
-  getFields()/getConstructors()/getMethods()访问该类的成员。方法名中带 Declared 的不返回父类成员，但返回私有成员；不带 Declared 的则相反

获得类成员后，可进一步：

- Constructor/Field/Method.setAccessible(true) 绕开 Java 语言访问限制
- Constructor.newInstance(Object[]) 来生成该类的实例
- Field.get/set(Object) 来访问字段值
- Method.invoke(Object, Object[]) 调用方法

## 6 总结

默认，方法的反射调用为委派实现，委派给本地实现来进行方法调用。在调用超过 15 次之后，委派实现便会将委派对象切换至动态实现。动态实现的字节码是自动生成的，它直接用 invoke 指令调用目标方法。

方法的反射调用会带来不少性能开销，原因主要有：

- 变长参数方法导致的 Object 数组
- 基本类型的自动装箱、拆箱
- 方法内联

将最后一段代码中 polluteProfile 方法的两个 Method 对象，都改成获取名字为“target”的方法。这两个获得的 Method 对象是同一个吗（==）？他们 equal 吗（.equals(…)）？对运行结果有何影响？

```java
import java.lang.reflect.Method;
 
public class Test {
  public static void target(int i) {
    // 空方法
  }
 
  public static void main(String[] args) throws Exception {
    Class<?> klass = Class.forName("Test");
    Method method = klass.getMethod("target", int.class);
    method.setAccessible(true);  // 关闭权限检查
    polluteProfile();
 
    long current = System.currentTimeMillis();
    for (int i = 1; i <= 2_000_000_000; i++) {
      if (i % 100_000_000 == 0) {
        long temp = System.currentTimeMillis();
        System.out.println(temp - current);
        current = temp;
      }
 
      method.invoke(null, 128);
    }
  }
 
  public static void polluteProfile() throws Exception {
    Method method1 = Test.class.getMethod("target", int.class);
    Method method2 = Test.class.getMethod("target", int.class);
    for (int i = 0; i < 2000; i++) {
      method1.invoke(null, 0);
      method2.invoke(null, 0);
    }
  }
  public static void target1(int i) { }
  public static void target2(int i) { }
}
```

[1] : https://docs.oracle.com/javase/tutorial/reflect/

[2]: http://hg.openjdk.java.net/jdk10/jdk10/jdk/file/777356696811/src/java.base/share/classes/jdk/internal/reflect/ReflectionFactory.java#l80
[3]: http://hg.openjdk.java.net/jdk10/jdk10/jdk/file/777356696811/src/java.base/share/classes/jdk/internal/reflect/ReflectionFactory.java#l78
[4]: https://docs.oracle.com/javase/tutorial/reflect/class/classMembers.html

[5]: https://docs.oracle.com/javase/10/docs/api/java/lang/reflect/package-summary.html