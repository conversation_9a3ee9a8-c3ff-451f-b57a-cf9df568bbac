2020 年 12 月 22 日Spring官方博客宣布，Spring Cloud 2020.0.0正式发布。2020.0.0是第一个使用新版本号命名方案Spring Cloud发行版本。

此前Spring Cloud使用英国伦敦地铁站命名一个大版本（train version），如果不按照新的版本号命名的话，本次的版本号应是Ilford。

## 1 Netflix OSS 被移除原因

更新版本没啥大惊小怪，但本次更新却正式开启Spring Cloud Netflix体系终结进程。Netflix公司是目前微服务落地中最成功的公司。它开源如Eureka、Hystrix、Zuul、Feign、Ribbon等知名微服务套件，统称Netflix OSS。在当时Netflix OSS成为微服务组件上事实的标准。但是微服务兴起不久，2018年前后Netflix公司宣布其核心组件Hystrix、Ribbon、Zuul、Eureka等进入维护状态，不再进行新特性开发，只修BUG。

这直接影响Spring Cloud项目发展，Spring官方不得不采取应对措施，SpringOne 2019 大会中，Spring Cloud宣布 Spring Cloud Netflix 项目进入维护模式，并在 2020 年移除相关Netflix OSS组件。

## 2 哪些 Netflix 被移除了

本次更新中，以下组件被从Spring Cloud Netflix中移除：

![](https://p3-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/74b8c734aad44fe09628bd2546c7cc01~tplv-k3u1fbpfcp-zoom-in-crop-mark:4536:0:0:0.awebp)

Spring Cloud官方尤其着重指出ribbon、hystrix 和 zuul从Spring Cloud 2020.0正式版发布后将不再被Spring Cloud支持。在Spring Cloud 2020.0仅剩下Eureka。但是留给Eureka的时间也不多了。

Feign虽是Netflix公司开源，但从 9.x 版本开始就移交给OpenFeign组织管理，不从属于Netflix OSS范畴。

## 替代方案

Netflix OSS的突生变故让Spring官方感到“不能在一棵树上吊死”。在开发维护Spring Cloud基础标准和组件的同时，引入一些云厂商分散风险，目前有：

- 微软的Spring Cloud Azure
- 阿里的Spring Cloud Alibaba
- 亚马逊的Spring Cloud for Amazon Web Services
- 谷歌云平台的 Spring Cloud GCP

## 何去何从？

继续使用Sprng Cloud Netflix问题不大，但长期来看，显然是不合适的。官方建议替代项目：

| 当前                        | 替代项目                                                     |
| :-------------------------- | :----------------------------------------------------------- |
| Hystrix                     | [Resilience4j](https://github.com/resilience4j/resilience4j) |
| Hystrix Dashboard / Turbine | Micrometer + Monitoring System                               |
| Ribbon                      | Spring Cloud Loadbalancer                                    |
| Zuul 1                      | Spring Cloud Gateway                                         |
| Archaius 1                  | Spring Boot external config + Spring Cloud Config            |

## 替代项目

Hystrix的替代Resilience4j：目前在https://github.com/spring-cloud-incubator/spring-cloud-circuitbreaker 中孵化。**原名叫`spring-cloud-r4j` ，最近改名为`spring-cloud-circuitbreaker` 。**

> 估计是Spring要抽象一个断路器统一规范，让不同断路器实现去实现，从而实现相同注解（如`EnableCircuitBreaker` ，然后不同实现如Hystrix、Resilience4j、Sentinel等想要接入只需更换不同的starter依赖，使用则完全一样）。

### Hystrix Dashboard /Turbine的替代

由于官方建议用Resilience4j替代Hystrix，所以你再也不需要Hystrix的那一堆监控轮子了！Resilience4j自带整合了Micrometer！。

> - 为了Hystrix的监控，得搞Hystrix Dashboard；为了监控微服务集群实例，又得搭Turbine；微服务整合Turbine又有HTTP方式&MQ方式，**两种方式还不能共存，不能兼容**……蛋疼！
> - Micrometer是Pivotal公司（Spring所在公司）**开源的监控门面**，类似监控世界的Slf4j；它可以和各种监控系统/监控画板/时序数据库配合使用，如Graphite、Influx、Ganglia、Prometheus。
> - Micrometer官网：http://micrometer.io/
> - Spring Boot 2的Spring Boot Actuator底层是Micrometer—如用Resilience4j，监控体验和Actuator一致

### Ribbon的替代

`Spring Cloud Loadbalancer` ：之前`spring-cloud-loadbalancer` 在`spring-cloud-loadbalancer` 项目（https://github.com/spring-cloud-incubator/spring-cloud-loadbalancer）中孵化，现在，该项目已经成为`spring-cloud-commons` 的子项目。**使用上，`spring-cloud-loadbalancer` 和Ribbon区别不大**。

![](https://img-blog.csdnimg.cn/b9b3925654bd41fb8cf868910719fd4e.png)

### Zuul 1的替代Spring Cloud Gateway

基本玩Spring Cloud的都知道。由于Zuul持续跳票1年多，Spring Cloud索性开发Spring Cloud Gateway。Zuul已经发布了Zuul 2.x，基于Netty，也是非阻塞的，支持长连接，但Spring Cloud没有整合计划。

### Archaius 1的替代

Spring Boot external config + Spring Cloud Config，Spring Cloud有N多组件，N多N多配置属性（1000+），其中很多配置是不给提示的。原因在于Spring Boot/Cloud的配置需要借助`spring-boot-configuration-processor` 读取代码的注释，并生成metadata.json文件才能有提示。而Netflix开源的组件（例如Ribbon/Hystrix等）都没有使用Spring Boot的规范，而是自己用Archaius管理配置（那代码风格，个人很不喜欢），根本没有metadata.json文件，于是这部分配置IDE无法给你提示。以后全面废弃Archaius，统一使用Spring Boot external config + Spring Cloud Config，**这意味着未来Spring Boot的编程体验更加统一的同时，配置提示还杠杠的。**

## 未来&其他的候选者

相信未来Spring Cloud的生态会越来越好。Spring Cloud生态还有其他替换项目&更多选择：

| 作用             | 业界用得最多        | 已孵化成功的替代项目     | 孵化中的替代项目           |
| :--------------- | :------------------ | :----------------------- | :------------------------- |
| 服务发现         | Eureka              | Consul、Zookeeper、Nacos |                            |
| 负载均衡器       | Ribbon              | -                        | -（Hoxton M3才会有替代品） |
| 断路器           | Hystrix             | Sentinel                 | Resilience4j               |
| 声明式HTTP客户端 | Feign               | -                        | Retrofit                   |
| API网关          | Zuul 1              | Spring Cloud             | -                          |
| 配置管理         | Spring Cloud Config | Consul、zk、Nacos        |                            |

参考：

- https://spring.io/blog/2018/12/12/spring-cloud-greenwich-rc1-available-now#spring-cloud-netflix-projects-entering-maintenance-mode
- https://www.itmuch.com/spring-cloud-sum/spring-cloud-netflix-in-maintenance-mode/
