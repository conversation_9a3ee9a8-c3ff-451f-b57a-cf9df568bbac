## 0 概述

###  一个问题

咋把对象变成字节流，最终写到socket底层？

writeAndFlush()：

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/c93c72036c9ff23c948e5cbc72c24dae.png)

编码器实现了` ChannelOutboundHandler`，并将出站数据从 一种格式转换为另一种格式，和解码器功能正好相反。Netty提供一组类帮你编写有以下功能的编码器：

- 将消息编码为字节
- 将消息编码为消息 

先从抽象基类 MessageToByteEncoder 开始考察。
## 1 抽象类 MessageToByteEncoder

MessageToByteEncoder API：

```java
/**
 * Encode a message into a {@link ByteBuf}. This method will be called for each written message that can be handled
 * by this encoder.
 *
 * @param ctx           the {@link ChannelHandlerContext} which this {@link MessageToByteEncoder} belongs to
 * @param msg           the message to encode
 * @param out           the {@link ByteBuf} into which the encoded message will be written
 * @throws Exception    is thrown if an error accour
 */
protected abstract void encode(ChannelHandlerContext ctx, I msg, ByteBuf out) throws Exception;
```

encode()方法是唯一需实现的抽象方法。它被调用时将传入要被该类编码为 ByteBuf 的(类型为I)出站消息。该 ByteBuf 随后将会被转发给 ChannelPipeline中的下一个ChanneloutboundHandler。

解码器通常需在` Channel `关闭后产生最后一个消息(因此也就有了 `decodeLast()`方法)。这显然不适于编码器场景——在连接被关闭之后仍产生一个消息，这毫无意义。

### 1.1 ShortToByteEncoder

其接受一` Short` 型实例作为消息，编码为`Short`的原子类型值，并写入`ByteBuf`，随后转发给`ChannelPipeline`中的下一个 `ChannelOutboundHandler`
每个传出的 Short 值都将会占用 ByteBuf 中的 2 字节

 ![ShortToByteEncoder](https://img-blog.csdnimg.cn/img_convert/50ca58eeb375226a628203d544322f2f.png)



![](https://img-blog.csdnimg.cn/img_convert/5404a716ae507f74309853be03d2c19c.png)

### 1.2 Encoder



![](https://img-blog.csdnimg.cn/img_convert/fb13e8649882fcdd598d276f5065ec55.png)

Netty 提供了一些专门化的 `MessageToByteEncoder`，可基于此实现自己的编码器。

## 2 抽象类 MessageToMessageEncoder

对于出站数据将如何从一种消息编码为另一种。`MessageToMessageEncoder `类的 `encode() `提供这种能力

![MessageToMessageEncoderAPI](https://img-blog.csdnimg.cn/img_convert/8a0e33a1b8550bdefbef5f5230a2f1a1.png)

用` IntegerToStringEncoder` 扩展了 `MessageToMessageEncoder`

编码器将每个出站 Integer 的 String 表示添加到了该 List 中
![](https://img-blog.csdnimg.cn/img_convert/777bec842429099cbc420afb1a3e2234.png)



![IntegerToStringEncoder的设计](https://img-blog.csdnimg.cn/img_convert/800724ea36f41466f8a668030c2b00fa.png)

 `io.netty.handler. codec.protobuf.ProtobufEncoder `类，它处理了由 Google 的 Protocol Buffers 规范所定义 的数据格式。
# 一个java对象最后是如何转变成字节流，写到socket缓冲区中去的

## writeAndFlush

- 从tail节点开始往前传播
- 逐个调用channelHandler#write
- 逐个调用channelHandler#flush

java对象编码过程
write：写队列
flush：刷新写队列
writeAndFlush: 写队列并刷新

## pipeline中的标准链表结构



![](https://img-blog.csdnimg.cn/img_convert/617986ae684862982559d8e8ebca51ae.png)
数据从head节点流入，先拆包，然后解码成业务对象，最后经过业务`Handler`处理，调用`write`，将结果对象写出去
而写的过程先通过`tail`节点，然后通过`encoder`节点将对象编码成`ByteBuf`，最后将该`ByteBuf`对象传递到`head`节点，调用底层的Unsafe写到JDK底层管道

## Java对象编码过程
pipeline中添加encoder节点，java对象就转换成netty可以处理的ByteBuf，写到管道里？

先看调用write的code

![](https://img-blog.csdnimg.cn/img_convert/fd89afb060e116b8bcc00d8ae9ea1192.png)

业务处理器接受到请求之后，做一些业务处理，返回一个`user`

然后，user在pipeline中传递

![AbstractChannel#](https://img-blog.csdnimg.cn/img_convert/0aa5869759594de2f6442ec5312ad36c.png)
![DefaultChannelPipeline#](https://img-blog.csdnimg.cn/img_convert/46ac885ca11db5a688ff1b770ce12d58.png)
![AbstractChannelHandlerContext#](https://img-blog.csdnimg.cn/img_convert/c83d15919da8983f347b8276db48e91b.png)
![AbstractChannelHandlerContext#](https://img-blog.csdnimg.cn/img_convert/2ce44e963143c01286c2be8f536dae4d.png)

情形一

![AbstractChannelHandlerContext#](https://img-blog.csdnimg.cn/img_convert/b0cbad58511705adc05eb03f813eb849.png)
![AbstractChannelHandlerContext#](https://img-blog.csdnimg.cn/img_convert/3529802d6d2a0ca42c529bb8d5a9c38f.png)

情形二

![AbstractChannelHandlerContext#](https://img-blog.csdnimg.cn/img_convert/04199ad3251dcae7e01165f460d6fb5e.png)
![](https://img-blog.csdnimg.cn/img_convert/2038d848f39e0e308c8996df43305b6e.png)
![AbstractChannelHandlerContext#invokeWrite0](https://img-blog.csdnimg.cn/img_convert/65f4312d46a6cd146cd03540034c6dda.png)
![AbstractChannelHandlerContext#invokeFlush0](https://img-blog.csdnimg.cn/img_convert/bc0322bf52c9ef561850033857ddb7df.png)

handler 如果不覆盖 flush 方法,就会一直向前传递直到 head 节点

![](https://img-blog.csdnimg.cn/img_convert/8130c68fe710735119135f53443bf3ce.png)

落到 `Encoder`节点，下面是 `Encoder` 的处理流程
![](https://img-blog.csdnimg.cn/img_convert/43a64f51a29e5dbd052bfa985c24d4e9.png)
按照简单自定义协议，将Java对象 User 写到传入的参数 out中，这个out到底是什么？

需知` User `对象，从`BizHandler`传入到 `MessageToByteEncoder`时，首先传到 `write` 
![](https://img-blog.csdnimg.cn/img_convert/6ba20dca3861857e15f7c50c1a2d0973.png)

### 1. 判断当前Handelr是否能处理写入的消息(匹配对象)
![](https://img-blog.csdnimg.cn/img_convert/0122610bca59ce51a25f5666f3211aeb.png)
![](https://img-blog.csdnimg.cn/img_convert/2f440202709d2ae40c6d510b96ba1829.png)
![](https://img-blog.csdnimg.cn/img_convert/6b51ee9233a4a7a9eba8e66bb7501128.png)
  - 判断该对象是否是该类型参数匹配器实例可匹配到的类型
![TypeParameterMatcher#](https://img-blog.csdnimg.cn/img_convert/614a763e12994bfdb4b491265df743e6.png)
![具体实例](https://img-blog.csdnimg.cn/img_convert/1c1ff0c9a1b8f6e860132668e8dcaf96.png)
### 2 分配内存
![](https://img-blog.csdnimg.cn/img_convert/7a019d8ba01d3f4dce255eee35abe008.png)
![](https://img-blog.csdnimg.cn/img_convert/77ec55d6a3a36b6c01ace2224f5bd45b.png)
### 3 编码实现
调用`encode`，这里就调回到  `Encoder` 这个`Handler`中
![](https://img-blog.csdnimg.cn/img_convert/94e5b659a644202aae1dc84c19f4cd97.png)

其为抽象方法,因此自定义实现类实现编码方法

![](https://img-blog.csdnimg.cn/img_convert/4969a5b730789d2e9e3436f74b0b0e35.png)
![](https://img-blog.csdnimg.cn/img_convert/e6fda151d61f99b7bc5c2b7b58198622.png)

### 4  释放对象
既然自定义Java对象转换成`ByteBuf`了，那么这个对象就已经无用，释放掉 (当传入的`msg`类型是`ByteBuf`时，就不需要自己手动释放了)
![](https://img-blog.csdnimg.cn/img_convert/a520a2bf8058b206195d5c24edf5dc68.png)
![](https://img-blog.csdnimg.cn/img_convert/07f7731ce3c5e72c3c82ddbd0cb83538.png)

### 5 传播数据

112 如果buf中写入了数据，就把buf传到下一个节点,直到 header 节点

![](https://img-blog.csdnimg.cn/img_convert/e031a74af16e0f1c59632cb6795a6f70.png)

### 6 释放内存
//115  否则，释放buf，将空数据传到下一个节点    
// 120 如果当前节点不能处理传入的对象，直接扔给下一个节点处理
// 127 当buf在pipeline中处理完之后，释放
![](https://img-blog.csdnimg.cn/img_convert/d6032c6db5579d95b2f6ea755768c27f.png)
#### Encoder处理传入的Java对象
- 判断当前`Handler`是否能处理写入的消息
    - 如果能处理，进入下面的流程
    - 否则，直接扔给下一个节点处理
- 将对象强制转换成`Encoder ` 可以处理的 `Response`对象
- 分配一个`ByteBuf`
- 调用`encoder`，即进入到 Encoder 的 encode方法，该方法是用户代码，用户将数据写入ByteBuf
- 既然自定义Java对象转换成ByteBuf了，那么这个对象就已经无用了，释放掉(当传入的msg类型是ByteBuf时，无需自己手动释放)
- 如果buf中写入了数据，就把buf传到下一个节点，否则，释放buf，将空数据传到下一个节点
- 最后，当buf在pipeline中处理完之后，释放节点

总结就是，`Encoder`节点分配一个`ByteBuf`，调用`encode`方法，将Java对象根据自定义协议写入到ByteBuf，然后再把ByteBuf传入到下一个节点，在我们的例子中，最终会传入到head节点
```
public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
    unsafe.write(msg, promise);
}
```
这里的msg就是前面在Encoder节点中，载有java对象数据的自定义ByteBuf对象
## write - 写buffer队列
![](https://img-blog.csdnimg.cn/img_convert/1b664c8f6493055ea4b4bd7218c1e8f0.png)
![ChannelOutboundInvoker#](https://img-blog.csdnimg.cn/img_convert/122b7f59c4171bbc4d2ad8b0ae742225.png)
![](https://img-blog.csdnimg.cn/img_convert/7f257d9ffaaca7d750da9d48cf3dcb8f.png)
![write(Object msg, boolean flush, ChannelPromise promise)](https://img-blog.csdnimg.cn/img_convert/cb2a3f855ea344c156b6152b73b1682c.png)
![](https://img-blog.csdnimg.cn/img_convert/df505bb667d37faf1920b2cb69d37bce.png)
![](https://img-blog.csdnimg.cn/img_convert/203292d06f2760dbba1932a9b963e862.png)
![](https://img-blog.csdnimg.cn/img_convert/bcae320224db3fd769a4e6e743efee07.png)
![HeadContext in DefaultChannelPipeline#write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise)](https://img-blog.csdnimg.cn/img_convert/4da6ba3ede584fcc983bc73dfa985950.png)
![Unsafe in Channel#write(Object msg, ChannelPromise promise)](https://img-blog.csdnimg.cn/img_convert/896291aae37cd695df33c5deebe9703b.png)
以下过程分三步讲解
![](https://img-blog.csdnimg.cn/img_convert/8943b39299d8f34ac2b4e849dcf0193f.png)
#### direct ByteBuf
![](https://img-blog.csdnimg.cn/img_convert/5e460377b1b779427a2cf7b266f69e62.png)
![AbstractChannel#filterOutboundMessage(Object msg)](https://img-blog.csdnimg.cn/img_convert/0c6ca96fc105a7555ec8f6c2814c99dd.png)

- 首先，调用` assertEventLoop `确保该方法的调用是在`reactor`线程中
- 然后，调用 `filterOutboundMessage() `，将待写入的对象过滤，把非`ByteBuf`对象和`FileRegion`过滤，把所有的非直接内存转换成直接内存`DirectBuffer`
![](https://img-blog.csdnimg.cn/img_convert/eb6a2c8cec6c900377343d4430e4e3cd.png)

AbstractNioChannel#newDirectBuffer

![](https://img-blog.csdnimg.cn/img_convert/8a06541aaa0d64abd1bc25f49f9da499.png)

#### 插入写队列
- 接下来，估算出需要写入的ByteBuf的size

![](https://img-blog.csdnimg.cn/img_convert/237a068d6521da682fb587d013d77516.png)
- 最后，调用 ChannelOutboundBuffer 的addMessage(msg, size, promise) 方法，所以，接下来，我们需要重点看一下这个方法干了什么事情
![ChannelOutboundBuffer](https://img-blog.csdnimg.cn/img_convert/a1e02f35baef3b0651a679697a2b9c0a.png)

想要理解上面这段代码，须掌握写缓存中的几个消息指针
![](https://img-blog.csdnimg.cn/img_convert/e50b295d07c315afa62a7380fd228f30.png)
ChannelOutboundBuffer 里面的数据结构是一个单链表结构，每个节点是一个 Entry，Entry 里面包含了待写出ByteBuf 以及消息回调 promise下面分别是
### 三个指针的作用
- flushedEntry 
表第一个被写到OS Socket缓冲区中的节点
![ChannelOutboundBuffer](https://img-blog.csdnimg.cn/img_convert/756f01e50a61f47a128a5b739c27ef3c.png)
- unFlushedEntry 
表第一个未被写入到OS Socket缓冲区中的节点
![ChannelOutboundBuffer](https://img-blog.csdnimg.cn/img_convert/94e5398a0f0673f36932914dbae5023d.png)
- tailEntry
表`ChannelOutboundBuffer`缓冲区的最后一个节点
![ChannelOutboundBuffer](https://img-blog.csdnimg.cn/img_convert/a130491c80859c35f6e35bfd7cdbc8bc.png)
###  图解过程
- 初次调用write 即 `addMessage` 后
![](https://img-blog.csdnimg.cn/img_convert/75a744279e475ecd87edbbc060bbc226.png)
`fushedEntry`指向空，`unFushedEntry`和 `tailEntry `都指向新加入节点

- 第二次调用 `addMessage`后
![](https://img-blog.csdnimg.cn/img_convert/17965d4b7471ee1a6250534c480a2898.png)

- 第n次调用 `addMessage`后
![](https://img-blog.csdnimg.cn/img_convert/a5561a77f98eff98b03724ca9c0c2a24.png)


可得,调用n次`addMessage`后
- `flushedEntry`指针一直指向`null`，表此时尚未有节点需写到Socket缓冲区
- `unFushedEntry`后有n个节点，表当前还有n个节点尚未写到Socket缓冲区

#### 设置写状态
![ChannelOutboundBuffer#addMessage](https://img-blog.csdnimg.cn/img_convert/2e842ca98fb0ef282347ac71780edc5e.png)

- 统计当前有多少字节需要需要被写出
![ChannelOutboundBuffer#addMessage(Object msg, int size, ChannelPromise promise)](https://img-blog.csdnimg.cn/img_convert/a7d916a3c4d746fe4b07e7e80cd9c298.png)

- 当前缓冲区中有多少待写字节
![ChannelOutboundBuffer#](https://img-blog.csdnimg.cn/img_convert/5b34a997eed16a8b14d18b98e123fcf9.png)

![](https://img-blog.csdnimg.cn/img_convert/c5e3d6c0fb6944749d7b329a409b7bd9.png)
![ChannelConfig#getWriteBufferHighWaterMark()](https://img-blog.csdnimg.cn/img_convert/88c29741c7f83368090ce78cd6a05f79.png)
![](https://img-blog.csdnimg.cn/img_convert/d9539ee7cad5828dcc5381eef0b3580e.png)
![](https://img-blog.csdnimg.cn/img_convert/7c25efd8f41c9545ea3cfc9b4936da8d.png)
- 所以默认不能超过64k
![WriteBufferWaterMark](https://img-blog.csdnimg.cn/img_convert/8f61e6c964685cc30bafc9eb72fd69c7.png)

![](https://img-blog.csdnimg.cn/img_convert/a48fe57fa6970c8ee8066b3c6c1e22c6.png)
- 自旋锁+CAS 操作,通过 pipeline 将事件传播到channelhandler 中监控
![](https://img-blog.csdnimg.cn/img_convert/14c9cd6110b76f1fd97873b62fb311f9.png)


## flush：刷新buffer队列
### 添加刷新标志并设置写状态
- 不管调用`channel.flush()`，还是`ctx.flush()`，最终都会落地到`pipeline`中的`head`节点
![DefaultChannelPipeline#flush](https://img-blog.csdnimg.cn/img_convert/88c6b84d9074e73a521c1b5c5a914485.png)

- 之后进入到`AbstractUnsafe`
![AbstractChannel#flush()](https://img-blog.csdnimg.cn/img_convert/ae89880c15149d3f834d6b0cbaa6dc06.png)

- flush方法中，先调用
![ChannelOutboundBuffer#addFlush](https://img-blog.csdnimg.cn/img_convert/e2622192885ad833425f66a832d76405.png)
![ChannelOutboundBuffer#decrementPendingOutboundBytes(long size, boolean invokeLater, boolean notifyWritability)](https://img-blog.csdnimg.cn/img_convert/d45f252cc1aabeb60c228cd627ab5204.png)
![](https://img-blog.csdnimg.cn/img_convert/f9fc48893565c4c7400e84a6d9efc835.png)
![和之前那个实例相同,不再赘述](https://img-blog.csdnimg.cn/img_convert/63ce47209818eb5f3b0e1efdc2e33060.png)

- 结合前面的图来看，上述过程即 
首先拿到 `unflushedEntry` 指针，然后将` flushedEntry `指向`unflushedEntry`所指向的节点，调用完毕后
![](https://img-blog.csdnimg.cn/img_convert/76526c11828fe46296e27b0aeecb6b87.png)
### 遍历 buffer 队列,过滤bytebuf
- 接下来，调用 `flush0()`
![](https://img-blog.csdnimg.cn/img_convert/e4120e6a3ae85343f7fb261573b45483.png)

- 发现这里的核心代码就一个 `doWrite`
![AbstractChannel#](https://img-blog.csdnimg.cn/img_convert/ef9549ee12ba0d4f15a939014a6186c9.png)

# AbstractNioByteChannel
- 继续跟
```java
protected void doWrite(ChannelOutboundBuffer in) throws Exception {
    int writeSpinCount = -1;

    boolean setOpWrite = false;
    for (;;) {
        // 拿到第一个需要flush的节点的数据
        Object msg = in.current();

        if (msg instanceof ByteBuf) {
            boolean done = false;
            long flushedAmount = 0;
            // 拿到自旋锁迭代次数
            if (writeSpinCount == -1) {
                writeSpinCount = config().getWriteSpinCount();
            }
            // 自旋，将当前节点写出
            for (int i = writeSpinCount - 1; i >= 0; i --) {
                int localFlushedAmount = doWriteBytes(buf);
                if (localFlushedAmount == 0) {
                    setOpWrite = true;
                    break;
                }

                flushedAmount += localFlushedAmount;
                if (!buf.isReadable()) {
                    done = true;
                    break;
                }
            }

            in.progress(flushedAmount);

            // 写完之后，将当前节点删除
            if (done) {
                in.remove();
            } else {
                break;
            }
        } 
    }
}
```
- 第一步，调用`current()`先拿到第一个需要`flush`的节点的数据
![ChannelOutboundBuffer#current](https://img-blog.csdnimg.cn/img_convert/eb9fc4d1b287622368e37aab6000056b.png)

- 第二步,拿到自旋锁的迭代次数
![](https://img-blog.csdnimg.cn/img_convert/2c10957d50d21a00ec2e780230617091.png)


- 第三步 调用 JDK 底层 API 进行自旋写
自旋的方式将`ByteBuf`写到JDK NIO的`Channel`
强转为ByteBuf，若发现没有数据可读，直接删除该节点
![](https://img-blog.csdnimg.cn/img_convert/e8f2ab3d26a2d1b2947b1494d259abd1.png)

- 拿到自旋锁迭代次数

![image.png](https://img-blog.csdnimg.cn/img_convert/21f255459c7d72d7d814efb77cb5ddd5.png)
- 在并发编程中使用自旋锁可以提高内存使用率和写的吞吐量,默认值为16
![ChannelConfig](https://img-blog.csdnimg.cn/img_convert/f2e8e75ed9d6239fb225a973fa1de65c.png)

- 继续看源码
![](https://img-blog.csdnimg.cn/img_convert/ccb5013175d052060166a3745205a1d2.png)
![AbstractNioByteChannel#](https://img-blog.csdnimg.cn/img_convert/12d87420d2da04f652930f0ce665e905.png)
-  `javaChannel()`，表明 JDK NIO Channel 已介入此次事件
![NioSocketChannel#](https://img-blog.csdnimg.cn/img_convert/2b7d400d7762169a9caa397607e49e18.png)
![ByteBuf#readBytes(GatheringByteChannel out, int length)](https://img-blog.csdnimg.cn/img_convert/31aa90e9e7d47d079e6ccf1840e4aeeb.png)

- 得到向JDK 底层已经写了多少字节
![PooledDirectByteBuf#](https://img-blog.csdnimg.cn/img_convert/48e5bc8e8189865c8bf799a4559d2ca2.png)
![](https://img-blog.csdnimg.cn/img_convert/bdd3bc5e4d1a45c4831fb8c0145e1502.png)
- 从 Netty 的 bytebuf 写到 JDK 底层的 bytebuffer
![](https://img-blog.csdnimg.cn/img_convert/24771292e6ca33ec4fe405396061db40.png)
![](https://img-blog.csdnimg.cn/img_convert/a6a6374926a9504140cff9ec1bb63bac.png)
- 第四步,删除该节点
节点的数据已经写入完毕，接下来就需要删除该节点
![](https://img-blog.csdnimg.cn/img_convert/3e79cdd236af8362f9288f7525008fcd.png)
首先拿到当前被`flush`掉的节点(`flushedEntry`所指)
然后拿到该节点的回调对象 `ChannelPromise`, 调用 `removeEntry()`移除该节点
![](https://img-blog.csdnimg.cn/img_convert/733c61cfd0d09ddc89691bd082425988.png)
这里是逻辑移除，只是将flushedEntry指针移到下个节点，调用后
![](https://img-blog.csdnimg.cn/img_convert/875b8d68dfb9f2ebb6669954b05b1ccf.png)

随后，释放该节点数据的内存，调用` safeSuccess `回调，用户代码可以在回调里面做一些记录，下面是一段Example
![](https://img-blog.csdnimg.cn/20210712173705596.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

最后，调用 `recycle`，将当前节点回收
## writeAndFlush - 写队列并刷新
**writeAndFlush**在某个`Handler`中被调用后，最终会落到 `TailContext `节点
![](https://img-blog.csdnimg.cn/20210712174234294.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)
![](https://img-blog.csdnimg.cn/20210712174957410.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)
![](https://img-blog.csdnimg.cn/2021071217491391.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)
![](https://img-blog.csdnimg.cn/20210712175404361.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)
通过一个boolean变量flush，表明调用` invokeWriteAndFlush` or ` invokeWrite`，`invokeWrite`便是我们上文中的write过程。
![AbstractChannelHandlerContext#](https://img-blog.csdnimg.cn/img_convert/64385883b5cb1463c090cdff552229eb.png)
可以看到，最终调用的底层方法和单独调用` write `和` flush `一样的
![](https://img-blog.csdnimg.cn/img_convert/10ab6574dd819241ac506b65c243e19e.png)
![](https://img-blog.csdnimg.cn/img_convert/809e30d9dc7da8065408c15898a52654.png)
由此看来，`invokeWriteAndFlush`基本等价于`write`之后再来一次`flush`。

## 总结

- 调用`write`并没有将数据写到Socket缓冲区中，而是写到了一个单向链表的数据结构中，`flush`才是真正的写出
- `writeAndFlush`等价于先将数据写到netty的缓冲区，再将netty缓冲区中的数据写到Socket缓冲区中，写的过程与并发编程类似，用自旋锁保证写成功
- netty中的缓冲区中的ByteBuf为DirectByteBuf

## 如何把对象变成字节流,最终写到socket底层?
当 BizHandler 通过 writeAndFlush 方法将自定义对象往前传播时,其实可以拆分成两个过程
- 通过 pipeline逐渐往前传播,传播到其中的一个 encode 节点后,其负责重写 write 方法将自定义的对象转化为 ByteBuf,接着继续调用 write 向前传播
- pipeline中的编码器原理是创建一个`ByteBuf`,将Java对象转换为`ByteBuf`，然后再把`ByteBuf`继续向前传递,若没有再重写了,最终会传播到 head 节点,其中缓冲区列表拿到缓存写到 JDK 底层 ByteBuffer
