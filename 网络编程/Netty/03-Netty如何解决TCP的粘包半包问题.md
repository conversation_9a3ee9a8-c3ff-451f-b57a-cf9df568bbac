##  0 现象演示

### 0.1 服务端

```java
public class Server {

    private int port;

    public Server(int port) {
        this.port = port;
    }

    public void start(){
        EventLoopGroup bossGroup = new NioEventLoopGroup();
        EventLoopGroup workGroup = new NioEventLoopGroup();

        ServerBootstrap server = new ServerBootstrap().group(bossGroup,workGroup)
                .channel(NioServerSocketChannel.class)
                .childHandler(new ServerChannelInitializer());

        try {
            ChannelFuture future = server.bind(port).sync();
            future.channel().closeFuture().sync();
        } catch (InterruptedException e) {
            System.out.println("server start fail");
        }finally {
            bossGroup.shutdownGracefully();
            workGroup.shutdownGracefully();
        }
    }

    public static void main(String[] args) {
        Server server = new Server(8090);
        server.start();
    }
}
```
### 0.2 客户端



名为 `Client` 的客户端类，是一个独立的程序，用于连接指定【ip:端口】的服务器，并发送一条消息。

 `Client` 类的 `start()` 方法中，使用 Netty#Bootstrap 类创建客户端，并将 `ClientChannelInitializer` 处理器添加到客户端通道的管道中，以便在新通道被创建时初始化它。

因此，这段代码的 `.handler(new ClientChannelInitializer())` 对应的对象是 `ClientChannelInitializer` 处理器，它被添加到客户端通道的管道中。

```java
package io.netty.example.sticky;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;

/**
 * <AUTHOR>
 * @date 2021/2/8
 */
public class Client {

  	// 要连接的服务器地址和端口
    private  int port;
    private  String address;
		
  	// 客户端构造方法,传入连接的服务器地址和端口
    public Client(int port, String address) {
        this.port = port;
        this.address = address;
    }

    public void start(){
      	// 创建事件循环组，处理客户端事件
        EventLoopGroup group = new NioEventLoopGroup();
				 
      	// 使用 Bootstrap 启动客户端
        Bootstrap bootstrap = new Bootstrap();
      	// 将事件循环组添加到 Bootstrap
        bootstrap.group(group)
          			 // 指定通道类型
                .channel(NioSocketChannel.class)
                .option(ChannelOption.TCP_NODELAY, true)
          			 // 为客户端通道设置 handler。Netty 中，handler 负责处理入、出站数据。此时，ClientChannelInitializer 是个扩展了 ChannelInitializer 的类，ChannelInitializer 是一种特殊handler，用于初始化新的channel
                .handler(new ClientChannelInitializer());
        try {
          	// 连接到服务器,发送一条消息,等待通道关闭,然后关闭事件循环组
            ChannelFuture future = bootstrap.connect(address,port).sync();
            future.channel().writeAndFlush("Hello world, i'm online");
            future.channel().closeFuture().sync();
        } catch (Exception e) {
            System.out.println("client start fail");
        } finally {
            group.shutdownGracefully();
        }

    }

    public static void main(String[] args) {
      	// 启动客户端，连接到本地的 8090 端口
        Client client = new Client(8090,"127.0.0.1");
        client.start();
    }
}
```

### 0.3 粘包现象



![](https://img-blog.csdnimg.cn/20210208170119996.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_16,color_FFFFFF,t_70)

### python 客户端

```python
import socket

s=socket.socket(socket.AF_INET,socket.SOCK_STREAM)
s.connect(("127.0.0.1",9001))

for i in range(100):
    print(i)
    string = "hello1哈_";
    body = bytes(string, 'utf-8')
    s.sendall(body)

```

发送为一整串数据：

![](https://img-blog.csdnimg.cn/74591bfcc2074f5d8dd918c45a6a9746.png)

而客户端期望打印 100 次，显然粘包了！

## 1 TCP为何有粘包半包?

### 1.1 粘包

- 发送方每次写入数据  < socket缓冲区大小
- 接收方读取socket缓冲区数据不够及时

应用程序写入数据小于套接字缓冲区大小，网卡将应用多次写入的数据发送到网络上，这将会发生粘包。

接收方法不及时读取套接字缓冲区数据，这将发生粘包。

这些都是在 TCP 层发生粘包，而我们应用层是和socket交互，即和一个缓冲区交互，里面都是字节。

### 1.2 半包

- 发送方写入数据 > 套接字缓冲区大小
- 发送的数据大于协议的MTU ( Maximum Transmission Unit,最大传输单元)，必须拆包

应用程序写入的数据大于套接字缓冲区大小，这将会发生拆包。

进行MSS（最大报文长度，一般 1460 或 1480）大小的TCP分段：
$$
TCP报文长度-TCP头部长度>MSS
$$
发生拆包。

而且
- 一个发送可能被多次接收，多个发送可能被一次接收
- 一个发送可能占用多个传输包，多个发送可能公用一个传输包

![](/Users/<USER>/Downloads/IDEAProjects/java-edge-master/assets/image-20241014214736665.png)

本质是因为 TCP 流式协议，消息无边界。
>  UDP就像快递，虽然一次运输多个，但每个包都有边界，一个个签收，所以无此类问题。

清楚问题本质，就知如何避免，咋获取完整的应用数据报文呢？

- 使用带消息头的协议, 头部写入包长度, 然后再读取包内容
- 设置定长消息, 每次读取定长内容, 长度不够时空位补固定字符
- 设置消息边界, 服务端从网络流中按消息边界分离出消息内容, 一般使用 `\n`
- 更复杂的协议，如 json、protobuf

## 2 解决方案

### 2.1  改为短连接

一个请求一个短连接。建立连接到释放连接之间的信息即为传输信息。
简单，但效率低下，不推荐。

### 2.2 封装成帧

#### ① 固定长度（不推荐）

字节的长度，FixedLengthFrameDecoder
![](https://img-blog.csdnimg.cn/20201212211018332.png)

满足固定长度即可：
![](https://img-blog.csdnimg.cn/20201212210901784.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_SmF2YUVkZ2U=,size_1,color_FFFFFF,t_70)

简单，但空间浪费，不推荐。

python 客户端中要发送字符串 "hello1哈_"

UTF-8 下，一个中文三个字节，所以总计 10 字节。设置参数为 10 即可：

```java
.childHandler(new ChannelInitializer<SocketChannel>() {
  @Override
  public void initChannel(SocketChannel ch) throws Exception {
    Charset encode = StandardCharsets.UTF_8;
    ch.pipeline().addLast(new FixedLengthFrameDecoder(10));
    // out
    ch.pipeline().addLast("encoder", new StringEncoder(encode));
    // in
    ch.pipeline().addLast("decoder", new StringDecoder(encode));
    ch.pipeline().addLast(new DiscardServerHandler());
  }
})
```

就正常了，不再粘包：

![](https://img-blog.csdnimg.cn/cab4ff962ec84181acb7503eda7d263a.png)

#### ② 分割符（推荐度低）

DelimiterBasedFrameDecoder，分隔符之间即为消息：
![](https://img-blog.csdnimg.cn/20201212211217335.png)

空间不浪费，也简单，但内容本身出现分隔符时需转义，所以需扫描内容。

```java
.childHandler(new ChannelInitializer<SocketChannel>() {
  @Override
  public void initChannel(SocketChannel ch) throws Exception {
    Charset encode = StandardCharsets.UTF_8;
    
    // out
    ch.pipeline().addLast("encoder", new StringEncoder(encode));
    // in
    ch.pipeline().addLast("decoder", new StringDecoder(encode));
    ch.pipeline().addLast(new DiscardServerHandler());
  }
})
```

#### ③  固定长度字段存个内容的长度信息（推荐）
- 解码：LengthFieldBasedFrameDecoder
- 编码：LengthFieldPrepender
先解析固定长度的字段获取长度，然后读取后续内容。这就没有之前的缺点了

精确定位用户数据，内容也不用转义。
但长度理论上有限制，需提前预知可能的最大长度，从而定义长度占用字节数。
如果直接定义成最大长度，但实际上每次传输的又远没达到最大值，不就浪费空间啦，所以根据需要设置最大长度。

其他方式比如 json 可看{}是否已经成对。但这种明显要扫描全部内容才知道是否成对。

python 客户端请求单条数据：

```python
import uuid
import socket

s=socket.socket(socket.AF_INET,socket.SOCK_STREAM)
s.connect(("127.0.0.1",9001))
data = "哈哈哈asdasdadfasdfasdfa哈123"
print(data)
dataBytes = bytes(data,"utf-8")
dataLen = len(dataBytes) 

data_len = dataLen.to_bytes(4, byteorder='big')
s.sendall(data_len + dataBytes)
```

```java
@Override
public void initChannel(SocketChannel ch) throws Exception {
  ch.pipeline().addLast(new MyDecoder());
}
```

```java
/**
 * <AUTHOR>
 */
public class MyDecoder extends ByteToMessageDecoder {


    /**
     * 数据长度 + 数据
     */
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {

        if (in.readableBytes() < 4) {
            return;
        }

        // 我们一次只获得9999
        // 但数据长度 4 + 10000,才是完整的数据
        int i = in.readInt();
        byte[] data = new byte[i];
        in.readBytes(data);
        System.out.println(new String(data));
    }
}
```



python 客户端请求发送多条数据：

```python
import uuid
import socket
 
s=socket.socket(socket.AF_INET,socket.SOCK_STREAM)
s.connect(("127.0.0.1",9001))
data = "哈哈哈asdasdadfasdfasdfa哈123"
print(data)
dataBytes = bytes(data,"utf-8")
dataLen = len(dataBytes) 

data_len = dataLen.to_bytes(4, byteorder='big')

for i in range(100):
    print(i)
    s.sendall(data_len + dataBytes)
```

发现报错了：

![](https://img-blog.csdnimg.cn/ea0e6bf6b8574f6591e28ef3e379d341.png)

是因为对 ByteBuf 理解不到位！
