## 0 前言

本次调研适用于新能源汽车智能网联数据场景的隐私计算平台，聚焦 GitHub 开源、支持 Web UI 的开源项目及境内商业成熟可用的隐私计算平台案例。

本文详细分析这些平台的功能特性、核心技术、部署与使用方式、用户权限管理能力，以及是否支持外部公司部署算法模型与算法规则，并会解释相关隐私计算原理，便于理解评估。

汽车和交通数据场景中，隐私计算平台利用多方安全计算（MPC）、联邦学习（FL）、可信执行环境（TEE）、同态加密（HE）、差分隐私（DP）等技术，实现“数据可用不可见”。

## 1 开源隐私计算平台

开源项目功能各有侧重：

- PrimiHub 和 FATE 偏重联邦学习和 MPC，适合多参与方协作
- Teaclave 偏重硬件TEE
- WeDPR 强调场景方案和易用性
- Sophon P²C 则结合大数据平台

总体而言，开源方案免费、可定制性强，但需自行部署运维；若需快速上手或深度定制，目前无社区活跃、文档完备的项目。

## 2 商用隐私计算平台及产品

### 蚂蚁隐私计算服务平台（阿里云）

![](https://p.ipic.vip/xzzn4b.png)

Ant Group 面向金融风控、联合营销等场景打造的工业级平台。核心功能包含**多方安全建模（MPC）**、**联邦学习**、**隐私求交（PSI）\**和\**多方安全分析（SCQL，类似安全SQL）**。平台提供 Web 控制台（可视化建模、算子组件）和API接口，支持一站式数据注册、建模、结果评估和模型部署。用户通过阿里云账号和权限体系管理项目访问，数据全程加密。支持外部机构上传自定义数据表和模型训练脚本，但算法组件相对固定。平台性能优化较好（百万级样本建模，亿级 PSI 分钟级完成）。适合金融、医疗、政务等大规模协作场景。

### 腾讯机密计算平台（T-Sec，腾讯云）

基于可信执行环境的**端到端数据机密性保护平台**。其核心是 Intel SGX 等 TEE 技术，通过 LibOS 技术支持用户任意程序镜像在可信环境中运行。产品提供**全托管服务**，用户只需上传容器镜像或程序，系统负责应用隔离、远程证明和密钥管理。支持多语言（包括 Java）应用接入，可通过控制台或 API 发起任务。用户权限由云账号控制，运行时数据与用户密钥绑定，物理层面采用双向远程证明和加密存储等机制。适用于医疗数据分析、模型训练等需要硬件级安全隔离的场景。缺点是功能较专一（侧重 TEE），不提供像 MPC/FL 那样的联合建模框架。

### 锘崴信隐私计算平台（南京锘崴科技）

软硬件结合的一体机隐私计算解决方案。核心集成多种隐私技术：软件层面采用自研隐私计算内核和联邦学习系统，硬件层面内置国产TEE、同态加速卡、国密加速芯片等。实现了“开箱即用”的一站式服务，用户以文件、数据库等形式接入数据，即可在一体机内安全协同分析。典型应用于政务、医疗、金融等领域。系统提供图形化界面和 SDK，兼容 Docker 部署；支持算法上传与沙箱运行，可根据场景定制协议。平台通过区块链技术保证计算过程可追溯，数据使用前后均有完整审计。**优势**：高性能（自研加速卡、TEE、并行计算）；**劣势**：成本高（需购买硬件设备），对快速迭代有限制。

- **华控清交“青椒算台”隐私计算平台**：基于清华背景的高安全平台。核心功能涵盖**安全多方计算**、**联邦学习**和**可信执行环境（TEE）**。底层拥有600+自研密文算子和丰富的密码协议库，封装了 PIR、PSI、联合建模/统计等常用功能模块。平台支持零代码可视化操作，算法自动匹配优化，性能优异（亿级数据 PSI/PIR 可秒级完成）。采用模块化架构，提供前端后台多层接口，用户权限与日志审计机制完备。支持接入外部算法和协议扩展，可部署在国产信创环境。主要应用于金融联合风控、运营商数据共享等场景。**特点**：软硬结合（TEE+同态芯片加速）、千人级算法库、安全机制全面。
- **上海富数科技**：专注于 MPC 和联邦学习的科技公司，推出**双引擎隐私计算平台**（结合区块链）。其核心在于实现数据“不离开源头、数据可用不可见”的安全计算。主要技术：MPC（各方加密状态协同计算）、水平联邦学习（数据不出门、算法共享）、隐匿查询（PSI变体）和联盟链授权等。平台提供云端产品和 SDK，具备日志追踪及合规计量功能。应用于金融反欺诈、电信反欺诈、供应链金融等场景。**优点**：覆盖MPC全流程、强大的区块链监管能力；**不足**：相较开源需要验收和交付周期。
- **星环科技 Sophon P²C 平台**：前文已述，专注于大数据环境下的隐私计算。**图聆抱朴**（科大讯飞）等国内也有联邦学习平台通过安全计算推进智能化应用。其他安全厂商如**亚信安全**、**安恒信息**等亦布局隐私计算产品，但偏向安全服务和咨询。

目前国内隐私计算市场已初具规模，典型成熟企业和平台包括：

  | 公司/平台                    | 主要技术                      | 代表案例                                           | 备注                                                         |
  | :--------------------------- | :---------------------------- | :------------------------------------------------- | :----------------------------------------------------------- |
  | 蚂蚁集团（Ant Group）        | 多方安全计算、联邦学习、TEE   | 摩斯多方安全计算平台、隐语框架、蚂蚁隐私计算一体机 | 已在金融风控、联合营销、医疗等场景商用，支持算法模型部署和规则控制，提供一站式服务和控制台[3](https://jingji.cctv.com/2022/05/05/ARTIFYyrEwZU0Ho8afWslykK220505.shtml)[4](https://www.aliyun.com/product/applicationservice/antppc) |
  | 华控清交（Huakong Qingjiao） | 多方安全计算                  | 光大银行多方安全计算平台                           | 银行生产系统首个多方安全计算平台，支持跨集团联合风控[3](https://jingji.cctv.com/2022/05/05/ARTIFYyrEwZU0Ho8afWslykK220505.shtml) |
  | 锘威科技（NuoWei Tech）      | 多方安全计算、国产CPU硬件加速 | 隐私计算一体机                                     | 结合硬件安全加速，提升性能，支持复杂算法部署[3](https://jingji.cctv.com/2022/05/05/ARTIFYyrEwZU0Ho8afWslykK220505.shtml) |
  | 创业公司如星云Clustar        | 联邦学习、多方安全计算        | 多行业联合建模                                     | 主要面向中小企业和特定行业，提供SaaS服务[3](https://jingji.cctv.com/2022/05/05/ARTIFYyrEwZU0Ho8afWslykK220505.shtml) |

  这些商业平台通常具备以下功能：

  - **数据注册与管理**：数据提供方可在平台上注册数据资产，定义数据权限。
  - **算法模型部署**：支持外部公司或合作方上传算法模型，定义算法规则，平台进行安全计算。
  - **多方联合计算**：支持多方数据在加密状态下联合统计、训练模型、分析。
  - **用户权限管理**：细粒度权限控制，保证不同角色访问和操作权限合规。
  - 合规审计：日志记录、计算过程可追溯，满足合规要求。
  - **一体机或云服务部署**：支持本地私有部署（如一体机）或云端SaaS，满足“数据不出域”要求[3](https://jingji.cctv.com/2022/05/05/ARTIFYyrEwZU0Ho8afWslykK220505.shtml)[4](https://www.aliyun.com/product/applicationservice/antppc)。

## 隐私计算平台部署
### 部署形态
- **私有部署（本地数据中心）**：适合严格不出域需求，部署隐私计算一体机或软件平台，所有计算节点均在本地环境。
- **云端服务**：部分平台提供云端隐私计算服务，适合数据允许出域或有混合云需求的场景。
### 操作流程
1. 数据提供方在平台注册数据资产，配置访问权限
2. 算法提供方上传算法模型及规则，通过平台审核
3. 各方通过隐私计算协议在加密状态下联合计算
4. 计算结果输出，保证数据隐私和合规
5. 平台提供操作日志和审计功能

**用户权限管理**
- 角色划分（数据提供方、算法提供方、平台管理员等）
- 权限细化到数据访问、算法调用、结果查看等
- 支持基于策略的动态权限调整

## 开源平台 V.S 商业平台

  | 维度         | 开源平台（如SecretFlow）           | 商业平台（如蚂蚁隐私计算服务平台）   |
  | :----------- | :--------------------------------- | :----------------------------------- |
  | 技术成熟度   | 技术前沿，社区支持，适合研发和验证 | 工业级稳定，支持大规模生产环境       |
  | 部署灵活性   | 自主部署，代码可控，需自建运维     | 提供一体机或云端，运维简便           |
  | 功能完整性   | 基础框架，需二次开发完善权限和管理 | 完善的权限管理、合规审计、用户界面   |
  | 算法模型支持 | 支持自定义算法，需开发集成         | 支持外部算法上传和规则配置，商业支持 |
  | 学习曲线     | 需要一定隐私计算理论基础和开发能力 | 适合快速业务落地，技术门槛较低       |
  | 合规保障     | 需自行评估和实现合规功能           | 通常已通过安全测评，合规保障更强     |

如团队具备隐私计算相关算法和安全开发能力，可以先基于SecretFlow等开源项目进行技术验证和原理学习，理解多方安全计算和联邦学习的实现机制；

若业务对稳定性、合规性和运维要求较高，建议选用国内成熟商业平台，尤其是蚂蚁集团的隐私计算服务平台，已在金融、医疗等多场景验证，支持严格数据不出域和算法模型外部部署。

商业平台在用户权限管理、合规审计和算法模型部署支持上更成熟，适合严格数据不出域的场景。

以上商业平台特点各异：

- **云服务类**（蚂蚁、腾讯）提供快速上线和维护，适合不想自建的用户；
- **软硬件一体类**（锘崴、华控清交）适合对性能和安全要求极高的行业用户；
- **专业垂直类**（富数等）提供针对性方案

选择时应权衡可定制性与易用性：若需要灵活接入自有算法并自行运维，可优先考虑开源项目；如希望即刻使用、少量开发，商业平台的 SDK/API 会更便捷。

## 4 选型建议

- **开源 vs 商业**：开源项目（如 PrimiHub、FATE、Teaclave）免费且可深度定制，适合研发团队评估或基础研究。但需要投入部署运维成本。商业平台（蚂蚁、锘崴、腾讯等）提供了更完善的产品支持和界面，适合快速落地或生产环境，但往往费用较高，且对算法改动有限制。
- **编程语言与接口**：如果团队以 Java 为主，可优先考虑支持 Java SDK 或 API 的平台。例如 PrimiHub 自身包含 Java SDK；腾讯 TEE 平台直接支持 Java 应用；Huakong 清交平台前端界面和 API 可适配 Java 环境。反之，FATE/SecretFlow 等以 Python 为主，Java 调用需要额外适配层。
- **部署环境**：若需要在**本地或私有云**部署，应关注系统对 Docker/Kubernetes 的支持、平台稳定性和可扩展性。例如 PrimiHub、FATE、Huakong 等都支持容器化部署。若更倾向**云服务**，可直接使用蚂蚁或腾讯提供的 PaaS，不必自行搭建环境。
- **自定义算法和数据控制**：对于需要上传自研算法的需求，应选择支持“算法沙箱”或脚本扩展的平台。PrimiHub、FATE、Huakong 清交等支持用户自定义模型和协议扩展；商业云产品通常只提供固定算子，也可通过SDK接口进行二次开发。
- **性能与安全要求**：若数据量巨大或安全级别要求极高，可考虑软硬件结合方案（锘崴、清交）或专用加速卡。普通业务场景下，纯软件方案（开源或云服务）即可满足需求。

综上，根据团队技术栈和业务场景平衡可控性与易用性：**测试评估阶段**可选择开源平台快速搭建原型；**生产应用阶段**则可视实际情况选用商业平台或混合方案。阅读上述各平台文档和案例，结合项目需求进行深度对比，是快速决策的关键。
