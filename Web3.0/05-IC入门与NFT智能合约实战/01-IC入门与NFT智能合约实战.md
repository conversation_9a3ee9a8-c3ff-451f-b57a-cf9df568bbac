## 1 什么是互联网计算机

引入无限区块链概念：

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/eb7266320f6f873485a86d7bcb2c1bb8.png)

互联计算机（Internet Computer，lC）由互联网计算机协议（Internet Computer Protocol，ICP）创建的区块链。

ICP 给开发者提供了让通用计算任务在互联网上直接运行的开发通信协议，同时为计算任务提供了计算能力，例如硬件、CPU、内存等。

ICP 创造了世界上第一个以网络速度运行的区块链。并且是可以无限扩容的和自治的。4.1C的节点机器是运行在分布在世界各地的独立数据中心的。

就像其他区块链，它不可停止，且它的智能合约里托管的代码不可篡改。

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/5285110dd359b7bca73bb78fcc155411.png)

创始团队：

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/3719154cf2d8a6f23d8c4ada7facbbef.png)



## 2 数据中心、节点、子网

IC并非实体硬件，是由全球独立运营的数据中心提供的计算资源组成的。IC的架构是让多个计算机组成一个强大的虚拟机。组成这个虚拟机的计算机分布在全球的多个子网数据中心。分布式架构上的安全通信不需要借助防火墙和防攻击技术。独立数据中心提供商通过贡献计算资源、运行应用服务，从而获得相应奖励。

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/5853ea960ed36ef17d6d05c3ccba87ed.png)

IC的子网分两类：

- System子网
- Application子网

IC的每个子网都是对等的，不存在主链或者中继链的概念。

每个子网的最少节点个数是13个节点。Catch Up Packge，子网每隔200个区块进行一次“打包”内部2f+1节点确认后，删除旧的状态。

Packge可以用来恢复数据，同步数据，重组子网等。

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/67d97446c9511950c7ed5702f395ff79.png)

Hierarchy of network building blocks：

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/b64e619bf63076a2e0597ece9526327a.png)



https://dashboard.internetcomputer.org/：

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/7af3f626afd1db7cb1e8e63d7d7087f7.png)

## 3 Canister介绍

IC上的智能合约Canister，除存储代码外，还存储了与当前状态相关的信息以及先前时间和用户交互的记录。



![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/774b9496962b633b492342a88ca44670.png)







![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/da27e5aa6633c7d5b1af2fe61d9be167.png)





![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/768c0c27f30124d1b2fe21c5a12e72a2.png)

### 查询调用

允许用户查询Canister当前状态或调用一个Canister的不改变状态的函数。

- 同步并且立即响应
- 可被持有Canister且无需共识即可验证结果的任何节点调用。在安全性和性能之间存在内在平衡，因为来自单个节点的答复可能是不可信的或不准确的。

不允许修改Canister的状态，本质上，程序使用查询调用来执行只读操作。

不允许被调用Canister调用其他Canister作为容器间调用暴露的方法，这个限制是临时的，在将来Canister可以调用其他Canister暴露的方法。
### 更新调用

允许用户修改Canister状态并保留更改。

- 异步
- 必须通过共识才能返回结果。 由于需要达成共识，因此更改容器的状态可能会花费些时间。因此，更新调用使用基于Actor的编程模型（状态隔离）来允许并发和异步处理。 在安全性和性能之间存在内在权衡，因为子网中三分之二的副本必须就结果达成一致
- 被调用的Canister可以调用其他Caniste公开的函数



Canister资源消耗

- Canister资源消耗类型:CPU执行、路由消息带宽和持久数据内存
- Canister维护帐户的Cycles余额，以支付其应用程序消耗的通信，计算和存储成本
- 程序必须能够支付完整执行费用(全部或全部不付)
- 通过设置Canister可以消耗多少个Cycle的限制，该平台可以防止恶意代码消耗资源

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/7fb9cc84ab730224d86a5a89a5d20e51.png)

## 4 Tokens && Cycles

### Tokens

1.互联网计算机生态系统的原生态代币是ICP
2.ICP代币在互联网计算机的治理和经济方面均发挥关键作用
3.ICP代币的获取：交易所购买、投资认领、Dfinity基金会、以节点提供商提供节点、以数据中心提供商提供算力
4.ICP代币的消耗：转换为Cycles、购买其他数字资产、锁进神经元里进行治理

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/e1ac1c870db43663691fdb0866b7e376.png)

### Cycles

- 运行智能合约（Canister容器）的成本通过 cycles 来计量
- 每个容器消耗各自的 cycles 余额
- 价格稳定 1*10**12 cycles=1SDR(约US$1.41)
- 通常由开发者为自己的容器充值
- 尽管可以使用ICP代币向Canister添加Cycles，但是Cycles本身不是货币，也没有流动性或资产属性
- Cycles不能转换回到ICP代币，但可以在Canister之间转移

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/e657543ddf4753a50a40ab119f6ea290.png)

## 5 网络神经元系统（NNS）

Network Nervous System：

1. Governance canister
2. Registry canister
3. Ledger canister

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/7b8c3f6aa0d2a38445ebc241b120b65e.png)



![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/20a33960d8f177bae18e4317b73af1fc.png)

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/420571322ccd44309b7eaf86af0d330f.png)
