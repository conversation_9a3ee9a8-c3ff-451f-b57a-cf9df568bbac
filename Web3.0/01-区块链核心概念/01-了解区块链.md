

## 0 链式存储结构

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/9c34a9cb022cde78a4e0f972af8c0cc9.png)

区块链是一种分布式账本技术，一种分布式数据存储技术，其核心是以去中心化方式存储和管理数据。

区块链由一个个区块通过哈希指针逻辑连接，每个区块包含：

- 区块头
- 交易数据

最初应用于金融场景，防止双花问题。

![](https://my-img.javaedge.com.cn/javaedge-blog/2024/05/f0df9c7686feac3a65f7efb351530e40.png)



![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/6d825cd53de9ab1ac8295d461de3b543.png)

每个区块（Block）包含一个：

- 区块头（Block Header），区块头中包含一些块的元数据信息，如区块编号、区块Hash值、区块创建时间、父区块的Hash值和Nonce（做工作量证明）等
- 一个区块体（Block Body），区块体中包含数据交易信息和账户信息等，不同区块链有不同实现

每个Block会计算一个Hash值，该Hash值会被下一个Block包含。这样前一个Block就被固定，所以只需简单计算前一 Block 的 Hash 值和自己包含的前一Block的Hash值是否一致，就能判断前一Block是否被修改，每个Block都确定前一个Block，就形成一个链式结构。

## 1 交易发起与验证

- 发起交易：用户在计算机上发起交易请求。交易可以是转账、资产交换或其他类型的信息交换
- 节点传播：交易请求会通过网络传播到多个节点。这些节点是区块链网络中的计算机，每个节点都有一份完整的区块链账本

## 2 节点与区块

**节点**：每个节点都拥有一份完整的区块链账本。图中展示多个节点（节点1、节点2、节点n、节点101等），这些节点通过网络相互通信，确保账本的一致性。

**区块**：区块链中的基本数据存储单位，每个区块包含若干交易信息。区块内的交易信息包括发送者、接收者、交易金额、时间戳等具体业务信息。

块头里都有一个 hash 值，hash 值指向前一个区块，以此形成链条。

这么多区块保存在一个节点里，而又有大量的节点。

## 3 打包区块

节点会将多个交易打包成一个区块。涉及对交易进行验证，确保交易的合法性。

## 4 共识确认与区块链更新

共识确认：当一个节点完成工作量证明并打包好一个区块后，其他节点会验证这个区块的有效性。一旦达成共识（即多数节点认可该区块），这个区块就会被添加到区块链的末端。

区块链更新：新的区块添加到区块链末端后，会广播到所有节点，然后所有节点都会更新各自的区块链账本，确保账本的一致性。

## 5 读取数据

**读取数据**：用户可以读取区块链上的数据，这些数据是公开透明的，任何人都可以查看。例如，用户可以查询某笔交易是否成功，查看某个地址的交易记录等。

## 6 实际应用

- **金融领域**：区块链技术被广泛应用于金融领域，如比特币、以太坊等数字货币，通过去中心化进行点对点交易，降低交易成本和风险。
- 供应链管理：在供应链中，区块链可以记录产品从生产到销售的整个流程，确保每个环节的数据透明和不可篡改，提升供应链效率和安全性。
- **数字身份认证**：区块链技术可以用于数字身份认证，确保个人身份信息的安全和隐私，防止身份盗用和信息泄露。

## 7 交易信息只能新增入区块链，而无法删除吗？

A：是的，区块链的一个重要特点是其数据的不可篡改性和不可删除性。具体来说：

### 交易信息只能新增，无法删除
- **新增交易**：一旦交易信息被打包成区块并添加到区块链中，这些信息就被永久记录在链上。新的交易会随着新的区块不断添加到区块链的末端。
- **不可删除性**：区块链中的数据不可删除。每个区块都包含前一个区块的哈希值，形成链式结构。如尝试删除或篡改某区块中的数据，链条完整性就会被破坏，导致整个链条失效。

### 不可篡改性

- 共识机制：区块链采用共识机制（如工作量证明PoW或权益证明PoS）来验证和确认交易。仅经过多数节点确认的区块才能被添加到区块链中，这使得单个节点无法轻易篡改数据
- 加密算法：区块链使用加密算法来保护数据的完整性和安全性。每个区块包含前一个区块的哈希值，这确保了链条的连贯性。如果尝试篡改某个区块中的数据，后续所有区块的哈希值都会改变，节点会识别出不一致，从而拒绝篡改

### 实际应用中的优势
- **数据透明和可追溯**：由于区块链中的数据不可删除，所有交易记录都是透明和可追溯。这在金融、供应链管理和数字身份认证等领域具有重要意义。例如，在供应链管理中，可以追溯产品的生产、运输和销售的每一个环节，确保数据的真实性和透明度。
- 安全性：不可篡改性提高数据安全性，防止恶意攻击和欺诈行为。金融领域意味着交易记录安全性得到保障，用户可放心进行点对点交易

综上，区块链的不可删除性和不可篡改性是其核心特点，这些特点确保了数据的完整性、安全性和透明性，广泛应用于多个实际领域。

## 8 传统交易模式 V.S 区块链交易模式



![](https://my-img.javaedge.com.cn/javaedge-blog/2024/07/1daea33e625c10594477740b272f4e86.png)

### 传统交易模式

1. **中心化**：
   - 传统交易依赖于第三方机构（如银行、支付平台）来处理和验证交易。
   - 所有的交易记录都存储在第三方机构的中心化数据库中。

2. **交易流程**：
   - 消费者发起交易请求，交易请求首先到达第三方机构。
   - 第三方机构验证交易的合法性和资金的充足性。
   - 第三方机构将验证通过的交易信息发送给商家。
   - 商家确认交易，商品或服务交付给消费者。

3. **问题**：
   - **单点故障**：如果第三方机构的系统出现故障，交易会中断。
   - **效率低下**：交易处理速度可能受到第三方机构的工作时间和效率限制。
   - **透明性不足**：交易记录存储在中心化数据库中，外部无法直接访问，透明性不足。
   - **安全性问题**：中心化数据库容易成为黑客攻击的目标，存在数据泄露风险。

### 区块链交易模式
1. **去中心化**：
   - 区块链交易模式不依赖于第三方机构，所有交易在去中心化的网络中进行。
   - 所有节点共同参与交易验证和记录，每个节点都存储完整的区块链账本。

2. **交易流程**：
   - 消费者发起交易请求，交易请求在区块链网络中广播。
   - 多个节点参与交易的验证，确保交易的合法性和资金的充足性。
   - 经过共识机制确认的交易被打包成区块，并添加到区块链的末端。
   - 区块链网络中的每个节点都更新自己的账本，记录新的交易区块。

3. **优点**：
   - **去中心化**：没有单点故障，系统更加健壮。
   - **公开透明**：区块链上的交易记录是公开可查的，任何人都可以查看和验证。
   - **开放共识**：交易验证由多个节点共同参与，避免了单个机构的权力垄断。
   - **安全可靠**：区块链使用加密技术和共识机制，确保交易记录的不可篡改性和安全性。

区块链交易模式在透明性、安全性和去中心化等方面的显著优势，而传统交易模式在效率和依赖性上存在明显的局限性。 

## 9 区块链分类

|            | 公有链                 | 联盟链                     | 私有链         |
| ---------- | ---------------------- | -------------------------- | -------------- |
| 参与者     | 任何人可自由出入       | 联盟或许可成员             | 公司内部       |
| 记账人     | 任何人                 | 许可成员                   | 公司自定义     |
| 激励机制   | 必须要                 | 可选                       | 可选           |
| 中心化程度 | 去中心化               | 弱中心化                   | 中心化         |
| 特点       | 区块链所有的特点       | 准入性（CA+权限）          | 内部透明和追溯 |
| 共识机制   | POW/POS/DPOS           | PBFT/kafka                 | PBFT           |
| 监管       | 不支持                 | 支持                       | 支持           |
| 应用场景   | Dapp开发可支持很多场景 | 支付/结算/政务/物联网/运输 | 审计/积分      |

国内主要支持：

- 联盟链，如百度的
- 私有链，如蚂蚁的
