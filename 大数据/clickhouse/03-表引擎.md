## 0 表引擎（即表的类型）的意义

决定了：

- 数据的存储方式和位置，写到哪里，从哪里读数据
- 支持哪些查询及如何支持
- 并发数据访问
- 索引的使用（如果存在）
- 是否可以执行多线程请求
- 数据复制参数

即ClickHouse表引擎决定底层数据的存储位置、数据组织结构数据的计算方式，是否可以使用索引、分区以及是否支持数据副本和容错等。

来看各种引擎类型。无默认，建表时必须显式指定引擎！

## 1 MergeTree

最广泛和通用的表引擎，高性能的数据写入和查询，支持副本、高可用和原子写入等。

适用于高负载任务的最通用和功能最强大的表引擎。这些引擎的共同特点是可以快速插入数据并进行后续的后台数据处理。 MergeTree系列引擎支持数据复制（使用[Replicated*](https://clickhouse.com/docs/zh/engines/table-engines/mergetree-family/replication#table_engines-replication) 的引擎版本），分区和一些其他引擎不支持的其他功能。

该类型引擎包括：

- [MergeTree](https://clickhouse.com/docs/zh/engines/table-engines/mergetree-family/mergetree#mergetree)
- [ReplacingMergeTree](https://clickhouse.com/docs/zh/engines/table-engines/mergetree-family/replacingmergetree#replacingmergetree)
- [SummingMergeTree](https://clickhouse.com/docs/zh/engines/table-engines/mergetree-family/summingmergetree#summingmergetree)
- [AggregatingMergeTree](https://clickhouse.com/docs/zh/engines/table-engines/mergetree-family/aggregatingmergetree#aggregatingmergetree)
- [CollapsingMergeTree](https://clickhouse.com/docs/zh/engines/table-engines/mergetree-family/collapsingmergetree#table_engine-collapsingmergetree)
- [VersionedCollapsingMergeTree](https://clickhouse.com/docs/zh/engines/table-engines/mergetree-family/versionedcollapsingmergetree#versionedcollapsingmergetree)
- [GraphiteMergeTree](https://clickhouse.com/docs/zh/engines/table-engines/mergetree-family/graphitemergetree#graphitemergetree)

支持副本，在每类引擎名称前加上Replicated前缀即可。如：

- ReplicatedMergeTree
- ReplicatedReplacingMergeTree

## 2 Log家族(高吞吐写入)

具有最小功能的[轻量级引擎](https://clickhouse.com/docs/zh/engines/table-engines/log-family)。专为高吞吐、低延迟的写入操作设计，通常用于日志收集等场景。

当你需要快速写入许多小表（最多约100万行）并在以后整体读取它们时，该类型引擎最有效。

### 2.1 TinyLog

最简单的表引擎，用于将数据存储在磁盘。每列都存储在单独的压缩文件。写入时，数据将附加到文件末尾。

并发数据访问不受任何限制：

- 如同时从表中读取并在不同查询中写入，则读操作抛异常
- 如同时写入多个查询中的表，则数据将被破坏

这种表引擎的典型用法是 write-once：首先只写入一次数据，然后根据需要多次读取。查询在单个流中执行。此引擎适用于相对较小的表（建议最多1,000,000行）。

如有许多小表，则使用此表引擎是适合的，因为它比Log引擎更简单（需要打开的文件更少）。拥有大量小表时，可能会导致性能低下，但在可能已经在其它 DBMS 时使用过，则你可能会发现切换使用 TinyLog 类型的表更容易。**不支持索引**。

在 Yandex.Metrica 中，TinyLog 表用于小批量处理的中间数据。

### 2.2 StripeLog

该引擎属于日志引擎系列。请在[日志引擎系列](https://clickhouse.com/docs/zh/engines/table-engines/log-family)文章中查看引擎的共同属性和差异。

在你需要写入许多小数据量（小于一百万行）的表的场景下使用这个引擎。

#### 建表

```text
CREATE TABLE [IF NOT EXISTS] [db.]table_name [ON CLUSTER cluster]
(
    column1_name [type1] [DEFAULT|MATERIALIZED|ALIAS expr1],
    column2_name [type2] [DEFAULT|MATERIALIZED|ALIAS expr2],
    ...
) ENGINE = StripeLog
```



查看[建表](https://clickhouse.com/docs/zh/engines/table-engines/log-family/stripelog#create-table-query)请求的详细说明。

#### 写数据

`StripeLog` 引擎将所有列存储在一个文件中。对每一次 `Insert` 请求，ClickHouse 将数据块追加在表文件的末尾，逐列写入。

ClickHouse 为每张表写入以下文件：

- `data.bin` — 数据文件。
- `index.mrk` — 带标记的文件。标记包含了已插入的每个数据块中每列的偏移量。

`StripeLog` 引擎不支持 `ALTER UPDATE` 和 `ALTER DELETE` 操作。

#### 读数据

带标记的文件使得 ClickHouse 可以并行的读取数据。这意味着 `SELECT` 请求返回行的顺序是不可预测的。使用 `ORDER BY` 子句对行进行排序。

#### 使用示例

建表：

```sql
CREATE TABLE stripe_log_table
(
    timestamp DateTime,
    message_type String,
    message String
)
ENGINE = StripeLog
```



插入数据：

```sql
INSERT INTO stripe_log_table VALUES (now(),'REGULAR','The first regular message')
INSERT INTO stripe_log_table VALUES (now(),'REGULAR','The second regular message'),(now(),'WARNING','The first warning message')
```

使用两次 `INSERT` 请求从而在 `data.bin` 文件中创建两个数据块。

ClickHouse 在查询数据时使用多线程。每个线程读取单独的数据块并在完成后独立的返回结果行。大多数情况下，输出中块的顺序和输入时相应块的顺序不同。如：

```sql
SELECT * FROM stripe_log_table
```

```text
┌───────────timestamp─┬─message_type─┬─message────────────────────┐
│ 2019-01-18 14:27:32 │ REGULAR      │ The second regular message │
│ 2019-01-18 14:34:53 │ WARNING      │ The first warning message  │
└─────────────────────┴──────────────┴────────────────────────────┘
┌───────────timestamp─┬─message_type─┬─message───────────────────┐
│ 2019-01-18 14:23:43 │ REGULAR      │ The first regular message │
└─────────────────────┴──────────────┴───────────────────────────┘
```

对结果排序（默认增序）：

```sql
SELECT * FROM stripe_log_table ORDER BY timestamp
```

```text
┌───────────timestamp─┬─message_type─┬─message────────────────────┐
│ 2019-01-18 14:23:43 │ REGULAR      │ The first regular message  │
│ 2019-01-18 14:27:32 │ REGULAR      │ The second regular message │
│ 2019-01-18 14:34:53 │ WARNING      │ The first warning message  │
└─────────────────────┴──────────────┴────────────────────────────┘
```

### 2.3 Log

`Log` 与 `TinyLog` 的不同之处在于，«标记» 的小文件与列文件存在一起。这些标记写在每个数据块上，并且包含偏移量，这些偏移量指示从哪里开始读取文件以便跳过指定的行数。这使得可以在多个线程中读取表数据。对于并发数据访问，可以同时执行读取操作，而写入操作则阻塞读取和其它写入。`Log`引擎不支持索引。同样，如果写入表失败，则该表将被破坏，并且从该表读取将返回错误。`Log`引擎适用于临时数据，write-once 表以及测试或演示目的。

## 3 集成引擎

与其他的数据存储与处理系统集成的引擎。 包括：

- [Kafka](https://clickhouse.com/docs/zh/engines/table-engines/integrations/kafka#kafka)
- [MySQL](https://clickhouse.com/docs/zh/engines/table-engines/integrations/mysql#mysql)
- [ODBC](https://clickhouse.com/docs/zh/engines/table-engines/integrations/odbc#table-engine-odbc)
- [JDBC](https://clickhouse.com/docs/zh/engines/table-engines/integrations/jdbc#table-engine-jdbc)
- [HDFS](https://clickhouse.com/docs/zh/engines/table-engines/integrations/hdfs#hdfs)

## 3 特定功能引擎

包括：

- [Distributed](https://clickhouse.com/docs/zh/engines/table-engines/special/distributed#distributed)
- [MaterializedView](https://clickhouse.com/docs/zh/engines/table-engines/special/materializedview#materializedview)
- [Dictionary](https://clickhouse.com/docs/zh/engines/table-engines/special/dictionary#dictionary)
- [Merge](https://clickhouse.com/docs/zh/engines/table-engines/special/merge#merge)
- [File](https://clickhouse.com/docs/zh/engines/table-engines/special/file#file)
- [Null](https://clickhouse.com/docs/zh/engines/table-engines/special/null#null)
- [Set](https://clickhouse.com/docs/zh/engines/table-engines/special/set#set)
- [Join](https://clickhouse.com/docs/zh/engines/table-engines/special/join#join)
- [URL](https://clickhouse.com/docs/zh/engines/table-engines/special/url#table_engines-url)
- [View](https://clickhouse.com/docs/zh/engines/table-engines/special/view#table_engines-view)
- [Memory](https://clickhouse.com/docs/zh/engines/table-engines/special/memory#memory)
- [Buffer](https://clickhouse.com/docs/zh/engines/table-engines/special/buffer#buffer)

## 4 虚拟列

虚拟列是表引擎组成的一部分，它在对应的表引擎的源代码中定义。

你不能在 `CREATE TABLE` 中指定虚拟列，并且虚拟列不会包含在 `SHOW CREATE TABLE` 和 `DESCRIBE TABLE` 的查询结果中。虚拟列是只读的，所以你不能向虚拟列中写入数据。

如果想要查询虚拟列中的数据，你必须在SELECT查询中包含虚拟列的名字。`SELECT *` 不会返回虚拟列的内容。

若你创建的表中有一列与虚拟列的名字相同，那么虚拟列将不能再被访问。我们不建议你这样做。为了避免这种列名的冲突，虚拟列的名字一般都以下划线开头。
